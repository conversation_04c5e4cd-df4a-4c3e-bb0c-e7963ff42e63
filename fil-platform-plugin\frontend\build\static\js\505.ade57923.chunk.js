"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[505],{505:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),c=r(1072),n=r(8602),l=r(8628),d=r(4282),i=r(4117),o=r(1283),m=r(4312),f=r(579);const x=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)(null),[x,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("agent_profiles").select("*, maker_profiles(brand_name)").eq("user_id",s.id).single();t?console.error("Error fetching agent profile:",t):r(a),h(!1)})()},[]),x?(0,f.jsx)("div",{children:e("loading_agent_dashboard")}):s?(0,f.jsxs)(t.A,{fluid:!0,children:[(0,f.jsx)(c.A,{className:"mb-3",children:(0,f.jsx)(n.A,{children:(0,f.jsx)("h2",{children:e("agent_dashboard")})})}),(0,f.jsxs)(c.A,{children:[(0,f.jsx)(n.A,{md:4,children:(0,f.jsx)(l.A,{className:"bg-primary text-white mb-3",children:(0,f.jsxs)(l.A.Body,{children:[(0,f.jsx)(l.A.Title,{children:e("brand_name")}),(0,f.jsx)("h3",{children:s.brand_name||"N/A"})]})})}),(0,f.jsx)(n.A,{md:4,children:(0,f.jsx)(l.A,{className:"bg-success text-white mb-3",children:(0,f.jsxs)(l.A.Body,{children:[(0,f.jsx)(l.A.Title,{children:e("commission_rate")}),(0,f.jsx)("h3",{children:s.commission_pct?100*s.commission_pct+"%":"N/A"})]})})}),(0,f.jsx)(n.A,{md:4,children:(0,f.jsx)(l.A,{className:"bg-info text-white mb-3",children:(0,f.jsxs)(l.A.Body,{children:[(0,f.jsx)(l.A.Title,{children:e("kyc_status")}),(0,f.jsx)("h3",{children:s.kyc_status||"N/A"})]})})})]}),(0,f.jsxs)(c.A,{className:"mt-4",children:[(0,f.jsx)(n.A,{md:6,className:"text-center",children:(0,f.jsx)(l.A,{children:(0,f.jsxs)(l.A.Body,{children:[(0,f.jsx)("h4",{children:e("member_management")}),(0,f.jsx)("p",{children:e("my_subordinate_members")}),(0,f.jsx)(d.A,{as:o.N_,to:"/agent/members",variant:"primary",children:e("enter_member_list")})]})})}),(0,f.jsx)(n.A,{md:6,className:"text-center",children:(0,f.jsx)(l.A,{children:(0,f.jsxs)(l.A.Body,{children:[(0,f.jsx)("h4",{children:e("product_management")}),(0,f.jsx)("p",{children:e("products_on_sale")}),(0,f.jsx)(d.A,{as:o.N_,to:"/agent/products",variant:"success",children:e("browse_agent_products")})]})})})]})]}):(0,f.jsx)("div",{className:"alert alert-warning",children:e("not_agent")})}},1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),c=r(5043),n=r(7852),l=r(579);const d=c.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...d}=e;const i=(0,n.oU)(r,"row"),o=(0,n.gy)(),m=(0,n.Jm)(),f=`${i}-cols`,x=[];return o.forEach(e=>{const s=d[e];let r;delete d[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&x.push(`${f}${a}-${r}`)}),(0,l.jsx)(c,{ref:s,...d,className:t()(a,i,...x)})});d.displayName="Row";const i=d},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),c=r(5043),n=r(7852),l=r(579);const d=c.forwardRef((e,s)=>{const[{className:r,...a},{as:c="div",bsPrefix:d,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...c}=e;r=(0,n.oU)(r,"col");const l=(0,n.gy)(),d=(0,n.Jm)(),i=[],o=[];return l.forEach(e=>{const s=c[e];let a,t,n;delete c[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const l=e!==d?`-${e}`:"";a&&i.push(!0===a?`${r}${l}`:`${r}${l}-${a}`),null!=n&&o.push(`order${l}-${n}`),null!=t&&o.push(`offset${l}-${t}`)}),[{...c,className:t()(a,...i,...o)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,l.jsx)(c,{...a,ref:s,className:t()(r,!i.length&&d)})});d.displayName="Col";const i=d},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),c=r(5043),n=r(7852),l=r(579);const d=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c="div",...d}=e;return a=(0,n.oU)(a,"card-body"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});d.displayName="CardBody";const i=d,o=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c="div",...d}=e;return a=(0,n.oU)(a,"card-footer"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});o.displayName="CardFooter";const m=o;var f=r(1778);const x=c.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...i}=e;const o=(0,n.oU)(r,"card-header"),m=(0,c.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,l.jsx)(f.A.Provider,{value:m,children:(0,l.jsx)(d,{ref:s,...i,className:t()(a,o)})})});x.displayName="CardHeader";const h=x,u=c.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:c,as:d="img",...i}=e;const o=(0,n.oU)(r,"card-img");return(0,l.jsx)(d,{ref:s,className:t()(c?`${o}-${c}`:o,a),...i})});u.displayName="CardImg";const j=u,N=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c="div",...d}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});N.displayName="CardImgOverlay";const b=N,p=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c="a",...d}=e;return a=(0,n.oU)(a,"card-link"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});p.displayName="CardLink";const y=p;var A=r(4488);const g=(0,A.A)("h6"),_=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c=g,...d}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});_.displayName="CardSubtitle";const v=_,w=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c="p",...d}=e;return a=(0,n.oU)(a,"card-text"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});w.displayName="CardText";const $=w,P=(0,A.A)("h5"),C=c.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:c=P,...d}=e;return a=(0,n.oU)(a,"card-title"),(0,l.jsx)(c,{ref:s,className:t()(r,a),...d})});C.displayName="CardTitle";const R=C,U=c.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:c,text:d,border:o,body:m=!1,children:f,as:x="div",...h}=e;const u=(0,n.oU)(r,"card");return(0,l.jsx)(x,{ref:s,...h,className:t()(a,u,c&&`bg-${c}`,d&&`text-${d}`,o&&`border-${o}`),children:m?(0,l.jsx)(i,{children:f}):f})});U.displayName="Card";const k=Object.assign(U,{Img:j,Title:R,Subtitle:v,Body:i,Link:y,Text:$,Header:h,Footer:m,ImgOverlay:b})}}]);
//# sourceMappingURL=505.ade57923.chunk.js.map