
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const AgentMemberList = () => {
    const { t } = useTranslation();
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchMembers = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch customers referred by this agent
            const { data, error } = await supabase
                .from('customer_profiles')
                .select(`
                    user_id,
                    real_name,
                    verify_status,
                    users ( email, created_at )
                `)
                .eq('agent_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching members:', error);
            } else {
                setMembers(data);
            }
            setLoading(false);
        };

        fetchMembers();
    }, []);

    if (loading) {
        return <div>{t('loading_members')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('my_subordinates')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('user_id')}</th>
                                        <th>{t('email')}</th>
                                        <th>{t('real_name')}</th>
                                        <th>{t('kyc_status')}</th>
                                        <th>{t('registration_time')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {members.length === 0 ? (
                                        <tr>
                                            <td colSpan="5" className="text-center">{t('no_subordinates')}</td>
                                        </tr>
                                    ) : (
                                        members.map(member => (
                                            <tr key={member.user_id}>
                                                <td>{member.user_id.substring(0, 8)}...</td>
                                                <td>{member.users?.email || 'N/A'}</td>
                                                <td>{member.real_name || 'N/A'}</td>
                                                <td><Badge bg={member.verify_status === 'approved' ? 'success' : 'warning'}>{member.verify_status}</Badge></td>
                                                <td>{new Date(member.users?.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AgentMemberList;
