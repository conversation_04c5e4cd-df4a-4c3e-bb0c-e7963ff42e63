"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[93],{9093:(e,s,t)=>{t.r(s),t.d(s,{default:()=>h});var r=t(5043),n=t(8628),a=t(1719),i=t(3814),l=t(4282),d=t(4117),o=t(4312),c=t(579);const h=()=>{const{t:e}=(0,d.Bd)(),[s,t]=(0,r.useState)(""),[h,u]=(0,r.useState)(""),[g,x]=(0,r.useState)(""),[j,m]=(0,r.useState)(!1);return(0,c.jsx)("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"80vh"},children:(0,c.jsx)(n.A,{style:{width:"400px"},children:(0,c.jsxs)(n.A.Body,{children:[(0,c.jsx)("h2",{className:"text-center mb-4",children:e("login")}),g&&(0,c.jsx)(a.A,{variant:"danger",children:g}),(0,c.jsxs)(i.A,{onSubmit:async t=>{t.preventDefault(),x(""),m(!0);try{const e=(0,o.b)(),{data:t,error:r}=await e.auth.signInWithPassword({email:s,password:h});if(r)throw r;console.log("Login Success:",t)}catch(r){x(r.message||e("login_failed")),console.error("Login Error:",r)}m(!1)},children:[(0,c.jsxs)(i.A.Group,{id:"email",children:[(0,c.jsx)(i.A.Label,{children:e("email_address")}),(0,c.jsx)(i.A.Control,{type:"email",required:!0,value:s,onChange:e=>t(e.target.value)})]}),(0,c.jsxs)(i.A.Group,{id:"password",children:[(0,c.jsx)(i.A.Label,{children:e("password")}),(0,c.jsx)(i.A.Control,{type:"password",required:!0,value:h,onChange:e=>u(e.target.value)})]}),(0,c.jsx)(l.A,{disabled:j,className:"w-100 mt-3",type:"submit",children:e(j?"logging_in":"login")})]})]})})})}}}]);
//# sourceMappingURL=93.4cc1ddf4.chunk.js.map