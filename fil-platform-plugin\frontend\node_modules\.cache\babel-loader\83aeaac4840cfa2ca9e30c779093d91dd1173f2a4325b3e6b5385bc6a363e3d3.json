{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Button}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RecommendPage=()=>{const{t}=useTranslation();const[referrals,setReferrals]=useState([]);const[loading,setLoading]=useState(true);const[inviteCode,setInviteCode]=useState('');useEffect(()=>{const fetchReferrals=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch current user's invite code\nconst{data:userData,error:userError}=await supabase.from('users').select('invite_code').eq('id',user.id).single();if(userError){console.error('Error fetching invite code:',userError);}else if(userData){setInviteCode(userData.invite_code);}// Fetch users referred by current user\nconst{data,error}=await supabase.from('users').select('id, email, created_at').eq('referred_by',user.id).order('created_at',{ascending:false});if(error){console.error('Error fetching referrals:',error);}else{setReferrals(data);}setLoading(false);};fetchReferrals();},[]);const copyToClipboard=()=>{navigator.clipboard.writeText(inviteCode);alert(t('invite_code_copied'));};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_referral_data')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('my_recommendations')}),/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('my_invite_code')}),/*#__PURE__*/_jsx(\"p\",{className:\"lead\",children:/*#__PURE__*/_jsx(\"strong\",{children:inviteCode||'N/A'})}),inviteCode&&/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:copyToClipboard,children:t('copy_invite_code')}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-3 text-muted\",children:t('share_invite_description')})]})})})}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('my_subordinate_users')}),/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('user_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('email')}),/*#__PURE__*/_jsx(\"th\",{children:t('registration_time')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:referrals.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"3\",className:\"text-center\",children:t('no_subordinate_users')})}):referrals.map(ref=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[ref.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:ref.email}),/*#__PURE__*/_jsx(\"td\",{children:new Date(ref.created_at).toLocaleString()})]},ref.id))})]})]})})})})]});};export default RecommendPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "RecommendPage", "t", "referrals", "setReferrals", "loading", "setLoading", "inviteCode", "setInviteCode", "fetchReferrals", "supabase", "data", "user", "auth", "getUser", "userData", "error", "userError", "from", "select", "eq", "id", "single", "console", "invite_code", "order", "ascending", "copyToClipboard", "navigator", "clipboard", "writeText", "alert", "children", "className", "Body", "Title", "variant", "onClick", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "ref", "substring", "email", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/RecommendPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst RecommendPage = () => {\n    const { t } = useTranslation();\n    const [referrals, setReferrals] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [inviteCode, setInviteCode] = useState('');\n\n    useEffect(() => {\n        const fetchReferrals = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch current user's invite code\n            const { data: userData, error: userError } = await supabase\n                .from('users')\n                .select('invite_code')\n                .eq('id', user.id)\n                .single();\n\n            if (userError) {\n                console.error('Error fetching invite code:', userError);\n            } else if (userData) {\n                setInviteCode(userData.invite_code);\n            }\n\n            // Fetch users referred by current user\n            const { data, error } = await supabase\n                .from('users')\n                .select('id, email, created_at')\n                .eq('referred_by', user.id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching referrals:', error);\n            } else {\n                setReferrals(data);\n            }\n            setLoading(false);\n        };\n\n        fetchReferrals();\n    }, []);\n\n    const copyToClipboard = () => {\n        navigator.clipboard.writeText(inviteCode);\n        alert(t('invite_code_copied'));\n    };\n\n    if (loading) {\n        return <div>{t('loading_referral_data')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_recommendations')}</h2>\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('my_invite_code')}</Card.Title>\n                            <p className=\"lead\"><strong>{inviteCode || 'N/A'}</strong></p>\n                            {inviteCode && (\n                                <Button variant=\"primary\" onClick={copyToClipboard}>\n                                    {t('copy_invite_code')}\n                                </Button>\n                            )}\n                            <p className=\"mt-3 text-muted\">{t('share_invite_description')}</p>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('my_subordinate_users')}</Card.Title>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('user_id')}</th>\n                                        <th>{t('email')}</th>\n                                        <th>{t('registration_time')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {referrals.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"3\" className=\"text-center\">{t('no_subordinate_users')}</td>\n                                        </tr>\n                                    ) : (\n                                        referrals.map(ref => (\n                                            <tr key={ref.id}>\n                                                <td>{ref.id.substring(0, 8)}...</td>\n                                                <td>{ref.email}</td>\n                                                <td>{new Date(ref.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default RecommendPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,KAAQ,iBAAiB,CAC1E,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAqB,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACe,QAAQ,CAAE,OAEfJ,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEK,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPN,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEK,IAAI,CAAEI,QAAQ,CAAEC,KAAK,CAAEC,SAAU,CAAC,CAAG,KAAM,CAAAP,QAAQ,CACtDQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,aAAa,CAAC,CACrBC,EAAE,CAAC,IAAI,CAAER,IAAI,CAACS,EAAE,CAAC,CACjBC,MAAM,CAAC,CAAC,CAEb,GAAIL,SAAS,CAAE,CACXM,OAAO,CAACP,KAAK,CAAC,6BAA6B,CAAEC,SAAS,CAAC,CAC3D,CAAC,IAAM,IAAIF,QAAQ,CAAE,CACjBP,aAAa,CAACO,QAAQ,CAACS,WAAW,CAAC,CACvC,CAEA;AACA,KAAM,CAAEb,IAAI,CAAEK,KAAM,CAAC,CAAG,KAAM,CAAAN,QAAQ,CACjCQ,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC,uBAAuB,CAAC,CAC/BC,EAAE,CAAC,aAAa,CAAER,IAAI,CAACS,EAAE,CAAC,CAC1BI,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIV,KAAK,CAAE,CACPO,OAAO,CAACP,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACrD,CAAC,IAAM,CACHZ,YAAY,CAACO,IAAI,CAAC,CACtB,CACAL,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDG,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkB,eAAe,CAAGA,CAAA,GAAM,CAC1BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACvB,UAAU,CAAC,CACzCwB,KAAK,CAAC7B,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAClC,CAAC,CAED,GAAIG,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAkC,QAAA,CAAM9B,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CAClD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA2C,QAAA,eACNlC,IAAA,OAAImC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE9B,CAAC,CAAC,oBAAoB,CAAC,CAAK,CAAC,cACnDJ,IAAA,CAACR,GAAG,EAAC2C,SAAS,CAAC,MAAM,CAAAD,QAAA,cACjBlC,IAAA,CAACP,GAAG,EAAAyC,QAAA,cACAlC,IAAA,CAACN,IAAI,EAAAwC,QAAA,cACDhC,KAAA,CAACR,IAAI,CAAC0C,IAAI,EAAAF,QAAA,eACNlC,IAAA,CAACN,IAAI,CAAC2C,KAAK,EAAAH,QAAA,CAAE9B,CAAC,CAAC,gBAAgB,CAAC,CAAa,CAAC,cAC9CJ,IAAA,MAAGmC,SAAS,CAAC,MAAM,CAAAD,QAAA,cAAClC,IAAA,WAAAkC,QAAA,CAASzB,UAAU,EAAI,KAAK,CAAS,CAAC,CAAG,CAAC,CAC7DA,UAAU,eACPT,IAAA,CAACJ,MAAM,EAAC0C,OAAO,CAAC,SAAS,CAACC,OAAO,CAAEV,eAAgB,CAAAK,QAAA,CAC9C9B,CAAC,CAAC,kBAAkB,CAAC,CAClB,CACX,cACDJ,IAAA,MAAGmC,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAAE9B,CAAC,CAAC,0BAA0B,CAAC,CAAI,CAAC,EAC3D,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAENJ,IAAA,CAACR,GAAG,EAAA0C,QAAA,cACAlC,IAAA,CAACP,GAAG,EAAAyC,QAAA,cACAlC,IAAA,CAACN,IAAI,EAAAwC,QAAA,cACDhC,KAAA,CAACR,IAAI,CAAC0C,IAAI,EAAAF,QAAA,eACNlC,IAAA,CAACN,IAAI,CAAC2C,KAAK,EAAAH,QAAA,CAAE9B,CAAC,CAAC,sBAAsB,CAAC,CAAa,CAAC,cACpDF,KAAA,CAACP,KAAK,EAAC6C,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAT,QAAA,eACpClC,IAAA,UAAAkC,QAAA,cACIhC,KAAA,OAAAgC,QAAA,eACIlC,IAAA,OAAAkC,QAAA,CAAK9B,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,cACvBJ,IAAA,OAAAkC,QAAA,CAAK9B,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAkC,QAAA,CAAK9B,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,EACjC,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAkC,QAAA,CACK7B,SAAS,CAACuC,MAAM,GAAK,CAAC,cACnB5C,IAAA,OAAAkC,QAAA,cACIlC,IAAA,OAAI6C,OAAO,CAAC,GAAG,CAACV,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAE9B,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,CACxE,CAAC,CAELC,SAAS,CAACyC,GAAG,CAACC,GAAG,eACb7C,KAAA,OAAAgC,QAAA,eACIhC,KAAA,OAAAgC,QAAA,EAAKa,GAAG,CAACxB,EAAE,CAACyB,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cACpChD,IAAA,OAAAkC,QAAA,CAAKa,GAAG,CAACE,KAAK,CAAK,CAAC,cACpBjD,IAAA,OAAAkC,QAAA,CAAK,GAAI,CAAAgB,IAAI,CAACH,GAAG,CAACI,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GAH/CL,GAAG,CAACxB,EAIT,CACP,CACJ,CACE,CAAC,EACL,CAAC,EACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAApB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}