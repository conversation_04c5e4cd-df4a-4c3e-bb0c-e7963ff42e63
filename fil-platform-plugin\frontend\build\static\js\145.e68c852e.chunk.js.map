{"version": 3, "file": "static/js/145.e68c852e.chunk.js", "mappings": "gKAGA,MAgCA,EAhCoBA,KAChB,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MAQjB,OACIC,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CACRC,SAAU,QACVC,IAAK,OACLC,MAAO,OACPC,WAAY,kBACZC,MAAO,QACPC,QAAS,OACTC,aAAc,MACdC,SAAU,OACVC,OAAQ,MACVC,SAAA,EACEX,EAAAA,EAAAA,MAAA,OAAAW,SAAA,CAAK,iBAAef,EAASgB,aAC7BZ,EAAAA,EAAAA,MAAA,OAAAW,SAAA,CAAK,SAAOf,EAASiB,SACrBb,EAAAA,EAAAA,MAAA,OAAAW,SAAA,CAAK,WAASf,EAASkB,WACvBC,EAAAA,EAAAA,KAAA,UAAQC,QArBaC,KACzBC,QAAQC,IAAI,oBAAqBvB,GACjCsB,QAAQC,IAAI,qCACZrB,EAAS,YAkBkCG,MAAO,CAAEmB,UAAW,OAAQT,SAAC,gC", "sources": ["components/RouterDebug.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nconst RouterDebug = () => {\n    const location = useLocation();\n    const navigate = useNavigate();\n\n    const handleTestNavigation = () => {\n        console.log('Current location:', location);\n        console.log('Attempting to navigate to /wallet');\n        navigate('/wallet');\n    };\n\n    return (\n        <div style={{ \n            position: 'fixed', \n            top: '10px', \n            right: '10px', \n            background: 'rgba(0,0,0,0.8)', \n            color: 'white', \n            padding: '10px',\n            borderRadius: '5px',\n            fontSize: '12px',\n            zIndex: 9999\n        }}>\n            <div>Current Path: {location.pathname}</div>\n            <div>Hash: {location.hash}</div>\n            <div>Search: {location.search}</div>\n            <button onClick={handleTestNavigation} style={{ marginTop: '5px' }}>\n                Test Navigate to /wallet\n            </button>\n        </div>\n    );\n};\n\nexport default RouterDebug;\n"], "names": ["RouterDebug", "location", "useLocation", "navigate", "useNavigate", "_jsxs", "style", "position", "top", "right", "background", "color", "padding", "borderRadius", "fontSize", "zIndex", "children", "pathname", "hash", "search", "_jsx", "onClick", "handleTestNavigation", "console", "log", "marginTop"], "sourceRoot": ""}