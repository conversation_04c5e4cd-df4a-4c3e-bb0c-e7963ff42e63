
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MyGainsPage = () => {
    const { t } = useTranslation();
    const [earnings, setEarnings] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchEarnings = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // This is a simplified fetch. In a real app, you'd join with orders/products
            // to get more context for each earning.
            const { data, error } = await supabase
                .from('order_distributions') // Assuming this table holds customer earnings
                .select(`
                    id,
                    batch_id,
                    order_id,
                    customer_id,
                    share_amount,
                    reward_amount,
                    fee_amount,
                    progress,
                    created_at
                `)
                .eq('customer_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching earnings:', error);
            } else {
                setEarnings(data);
            }
            setLoading(false);
        };

        fetchEarnings();
    }, []);

    if (loading) {
        return <div>{t('loading_earnings')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('my_gains')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('gain_id')}</th>
                                        <th>{t('order_id')}</th>
                                        <th>{t('shares')}</th>
                                        <th>{t('gain_amount')}</th>
                                        <th>{t('fee')}</th>
                                        <th>{t('progress')}</th>
                                        <th>{t('time')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {earnings.length === 0 ? (
                                        <tr>
                                            <td colSpan="7" className="text-center">{t('no_gains_record')}</td>
                                        </tr>
                                    ) : (
                                        earnings.map(earning => (
                                            <tr key={earning.id}>
                                                <td>{earning.id.substring(0, 8)}...</td>
                                                <td>{earning.order_id ? earning.order_id.substring(0, 8) + '...' : 'N/A'}</td>
                                                <td>{earning.share_amount}</td>
                                                <td>{earning.reward_amount}</td>
                                                <td>{earning.fee_amount}</td>
                                                <td><Badge bg={earning.progress === 1 ? 'success' : 'info'}>{earning.progress * 100}%</Badge></td>
                                                <td>{new Date(earning.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MyGainsPage;
