import React, { Suspense, useEffect, useState } from 'react';
import { initSupabase } from './supabaseClient';
import { HashRouter, Routes, Route, Link } from 'react-router-dom';
import { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

// Lazy load components for better performance
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));
const ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));
const OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));
const WalletPage = React.lazy(() => import('./pages/customer/WalletPage'));
const MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));
const MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));
const KycPage = React.lazy(() => import('./pages/customer/KycPage'));
const RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));
const AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));
const AgentMemberList = React.lazy(() => import('./pages/agent/AgentMemberList'));
const AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));
const MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));
const MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));
const MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));

function App() {
  const { t, i18n } = useTranslation();
  const [supabase, setSupabase] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initialize = async () => {
      const supa = await initSupabase();
      setSupabase(supa);
      setLoading(false);
    };
    initialize();
  }, []);

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  return (
    <HashRouter>
      <div>
        <Navbar bg="dark" variant="dark" expand="lg">
        <Container>
          <Navbar.Brand as={Link} to="/">FIL Platform</Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto">
              <Nav.Link as={Link} to="/">{t('dashboard')}</Nav.Link>
              {/* Add other nav links based on role later */}
            </Nav>
            <Nav>
                <NavDropdown title={t('language')} id="basic-nav-dropdown">
                    <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>
                    <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>
                    <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>
                </NavDropdown>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container className="mt-4">
        <Suspense fallback={<div>{t('loading')}</div>}>
          {loading ? (
            <div>{t('initializing_platform')}</div>
          ) : !supabase ? (
            <div className="alert alert-danger">{t('backend_connection_failed')}</div>
          ) : (
            <Routes>
              {/* Common Route */}
              <Route path="/login" element={<LoginPage />} />

              {/* Customer Routes */}
              <Route path="/" element={<CustomerDashboard />} />
            <Route path="/products" element={<ProductListPage />} />
            <Route path="/orders" element={<OrderListPage />} />
            <Route path="/wallet" element={<WalletPage />} />
            <Route path="/my" element={<MyAccountPage />} />
            <Route path="/my-gains" element={<MyGainsPage />} />
            <Route path="/my/kyc" element={<KycPage />} />
            <Route path="/my/recommend" element={<RecommendPage />} />
              {/* e.g., <Route path="/orders" element={<CustomerOrders />} /> */}

              {/* Agent Routes */}
              <Route path="/agent" element={<AgentDashboard />} />
            <Route path="/agent/members" element={<AgentMemberList />} />
            <Route path="/agent/products" element={<AgentProductListPage />} />
              {/* e.g., <Route path="/agent/members" element={<AgentMembers />} /> */}

              {/* Maker Routes */}
              <Route path="/maker" element={<MakerDashboard />} />
            <Route path="/maker/products" element={<MakerProductListPage />} />
            <Route path="/maker/orders" element={<MakerOrderListPage />} />
              {/* e.g., <Route path="/maker/products" element={<MakerProducts />} /> */}
            </Routes>
          )}
        </Suspense>
      </Container>
      </div>
    </HashRouter>
  );
}

export default App;