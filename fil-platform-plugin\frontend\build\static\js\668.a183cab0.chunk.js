"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[668],{668:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),l=r(1072),d=r(8602),c=r(8628),n=r(4282),i=r(4117),o=r(1283),m=r(4312),f=r(579);const x=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)(null),[x,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("maker_profiles").select("*").eq("user_id",s.id).single();t?console.error("Error fetching maker profile:",t):r(a),h(!1)})()},[]),x?(0,f.jsx)("div",{children:e("loading_maker_dashboard")}):s?(0,f.jsxs)(t.A,{fluid:!0,children:[(0,f.jsx)(l.A,{className:"mb-3",children:(0,f.jsx)(d.A,{children:(0,f.jsx)("h2",{children:e("maker_dashboard")})})}),(0,f.jsxs)(l.A,{children:[(0,f.jsx)(d.A,{md:4,children:(0,f.jsx)(c.A,{className:"bg-primary text-white mb-3",children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)(c.A.Title,{children:e("domain")}),(0,f.jsx)("h3",{children:s.domain||"N/A"})]})})}),(0,f.jsx)(d.A,{md:4,children:(0,f.jsx)(c.A,{className:"bg-success text-white mb-3",children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)(c.A.Title,{children:e("support_email")}),(0,f.jsx)("h3",{children:s.support_email||"N/A"})]})})}),(0,f.jsx)(d.A,{md:4,children:(0,f.jsx)(c.A,{className:"bg-info text-white mb-3",children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)(c.A.Title,{children:e("sms_signature")}),(0,f.jsx)("h3",{children:s.sms_signature||"N/A"})]})})})]}),(0,f.jsxs)(l.A,{className:"mt-4",children:[(0,f.jsx)(d.A,{md:6,className:"text-center",children:(0,f.jsx)(c.A,{children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)("h4",{children:e("product_management")}),(0,f.jsx)("p",{children:e("manage_your_products")}),(0,f.jsx)(n.A,{as:o.N_,to:"/maker/products",variant:"primary",children:e("enter_product_list")})]})})}),(0,f.jsx)(d.A,{md:6,className:"text-center",children:(0,f.jsx)(c.A,{children:(0,f.jsxs)(c.A.Body,{children:[(0,f.jsx)("h4",{children:e("order_management")}),(0,f.jsx)("p",{children:e("all_orders")}),(0,f.jsx)(n.A,{as:o.N_,to:"/maker/orders",variant:"success",children:e("enter_order_list")})]})})})]})]}):(0,f.jsx)("div",{className:"alert alert-warning",children:e("not_maker")})}},1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),c=r(579);const n=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...n}=e;const i=(0,d.oU)(r,"row"),o=(0,d.gy)(),m=(0,d.Jm)(),f=`${i}-cols`,x=[];return o.forEach(e=>{const s=n[e];let r;delete n[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&x.push(`${f}${a}-${r}`)}),(0,c.jsx)(l,{ref:s,...n,className:t()(a,i,...x)})});n.displayName="Row";const i=n},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),c=r(579);const n=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:n,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,d.oU)(r,"col");const c=(0,d.gy)(),n=(0,d.Jm)(),i=[],o=[];return c.forEach(e=>{const s=l[e];let a,t,d;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:d}=s):a=s;const c=e!==n?`-${e}`:"";a&&i.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=d&&o.push(`order${c}-${d}`),null!=t&&o.push(`offset${c}-${t}`)}),[{...l,className:t()(a,...i,...o)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,c.jsx)(l,{...a,ref:s,className:t()(r,!i.length&&n)})});n.displayName="Col";const i=n},8628:(e,s,r)=>{r.d(s,{A:()=>U});var a=r(8139),t=r.n(a),l=r(5043),d=r(7852),c=r(579);const n=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,d.oU)(a,"card-body"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});n.displayName="CardBody";const i=n,o=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,d.oU)(a,"card-footer"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});o.displayName="CardFooter";const m=o;var f=r(1778);const x=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...i}=e;const o=(0,d.oU)(r,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,c.jsx)(f.A.Provider,{value:m,children:(0,c.jsx)(n,{ref:s,...i,className:t()(a,o)})})});x.displayName="CardHeader";const h=x,u=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:n="img",...i}=e;const o=(0,d.oU)(r,"card-img");return(0,c.jsx)(n,{ref:s,className:t()(l?`${o}-${l}`:o,a),...i})});u.displayName="CardImg";const j=u,N=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});N.displayName="CardImgOverlay";const p=N,b=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...n}=e;return a=(0,d.oU)(a,"card-link"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});b.displayName="CardLink";const A=b;var y=r(4488);const g=(0,y.A)("h6"),v=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=g,...n}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});v.displayName="CardSubtitle";const w=v,_=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...n}=e;return a=(0,d.oU)(a,"card-text"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});_.displayName="CardText";const $=_,P=(0,y.A)("h5"),k=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=P,...n}=e;return a=(0,d.oU)(a,"card-title"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...n})});k.displayName="CardTitle";const C=k,R=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:n,border:o,body:m=!1,children:f,as:x="div",...h}=e;const u=(0,d.oU)(r,"card");return(0,c.jsx)(x,{ref:s,...h,className:t()(a,u,l&&`bg-${l}`,n&&`text-${n}`,o&&`border-${o}`),children:m?(0,c.jsx)(i,{children:f}):f})});R.displayName="Card";const U=Object.assign(R,{Img:j,Title:C,Subtitle:w,Body:i,Link:A,Text:$,Header:h,Footer:m,ImgOverlay:p})}}]);
//# sourceMappingURL=668.a183cab0.chunk.js.map