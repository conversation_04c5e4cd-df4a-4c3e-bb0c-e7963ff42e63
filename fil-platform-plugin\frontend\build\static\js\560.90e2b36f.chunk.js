"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[560],{1072:(e,s,a)=>{a.d(s,{A:()=>c});var r=a(8139),t=a.n(r),l=a(5043),d=a(7852),n=a(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,as:l="div",...o}=e;const c=(0,d.oU)(a,"row"),i=(0,d.gy)(),f=(0,d.Jm)(),m=`${c}-cols`,x=[];return i.forEach(e=>{const s=o[e];let a;delete o[e],null!=s&&"object"===typeof s?({cols:a}=s):a=s;const r=e!==f?`-${e}`:"";null!=a&&x.push(`${m}${r}-${a}`)}),(0,n.jsx)(l,{ref:s,...o,className:t()(r,c,...x)})});o.displayName="Row";const c=o},3560:(e,s,a)=>{a.r(s),a.d(s,{default:()=>x});var r=a(5043),t=a(3519),l=a(1072),d=a(8602),n=a(8628),o=a(4063),c=a(4282),i=a(4117),f=a(4312),m=a(579);const x=()=>{const{t:e}=(0,i.Bd)(),[s,a]=(0,r.useState)([]),[x,u]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return;u(!0);const{data:s,error:r}=await e.from("products").select("\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    effective_delay_days,\n                    min_purchase,\n                    is_disabled,\n                    maker_profiles ( brand_name: user_id, maker_brand: domain ) \n                ").eq("is_disabled",!1).order("created_at",{ascending:!1});r?console.error("Error fetching products:",r):a(s),u(!1)})()},[]),x?(0,m.jsx)("div",{children:e("loading_products")}):(0,m.jsxs)(t.A,{children:[(0,m.jsx)("h2",{className:"mb-4",children:e("power_products")}),(0,m.jsx)(l.A,{children:s.map(s=>{var a;return(0,m.jsx)(d.A,{md:4,className:"mb-4",children:(0,m.jsx)(n.A,{className:s.is_disabled?"bg-light":"",children:(0,m.jsxs)(n.A.Body,{children:[(0,m.jsxs)(n.A.Title,{className:"d-flex justify-content-between",children:[s.name,"spot"===s.category?(0,m.jsx)(o.A,{bg:"success",children:e("spot_category")}):(0,m.jsx)(o.A,{bg:"primary",children:e("futures_category")})]}),(0,m.jsxs)(n.A.Subtitle,{className:"mb-2 text-muted",children:[e("provided_by")," ",(null===(a=s.maker_profiles)||void 0===a?void 0:a.maker_brand)||e("official")]}),(0,m.jsxs)(n.A.Text,{children:[(0,m.jsxs)("strong",{children:[e("price_label"),":"]})," ",s.price," ",e("usdt_per_share")," ",(0,m.jsx)("br",{}),(0,m.jsxs)("strong",{children:[e("total_shares_label"),":"]})," ",s.total_shares," ",(0,m.jsx)("br",{}),(0,m.jsxs)("strong",{children:[e("remaining_shares_label"),":"]})," ",s.total_shares-s.sold_shares," ",(0,m.jsx)("br",{}),(0,m.jsxs)("strong",{children:[e("min_purchase_label"),":"]})," ",s.min_purchase," ",e("shares_unit")," ",(0,m.jsx)("br",{}),(0,m.jsxs)("strong",{children:[e("waiting_period_label"),":"]})," ",s.effective_delay_days," ",e("days_unit")]}),(0,m.jsx)(c.A,{variant:"primary",disabled:s.is_disabled||s.total_shares-s.sold_shares===0,children:s.total_shares-s.sold_shares===0?e("sold_out"):e("buy_now")})]})})},s.id)})})]})}},4063:(e,s,a)=>{a.d(s,{A:()=>c});var r=a(8139),t=a.n(r),l=a(5043),d=a(7852),n=a(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:a,bg:r="primary",pill:l=!1,text:o,className:c,as:i="span",...f}=e;const m=(0,d.oU)(a,"badge");return(0,n.jsx)(i,{ref:s,...f,className:t()(c,m,l&&"rounded-pill",o&&`text-${o}`,r&&`bg-${r}`)})});o.displayName="Badge";const c=o},8602:(e,s,a)=>{a.d(s,{A:()=>c});var r=a(8139),t=a.n(r),l=a(5043),d=a(7852),n=a(579);const o=l.forwardRef((e,s)=>{const[{className:a,...r},{as:l="div",bsPrefix:o,spans:c}]=function(e){let{as:s,bsPrefix:a,className:r,...l}=e;a=(0,d.oU)(a,"col");const n=(0,d.gy)(),o=(0,d.Jm)(),c=[],i=[];return n.forEach(e=>{const s=l[e];let r,t,d;delete l[e],"object"===typeof s&&null!=s?({span:r,offset:t,order:d}=s):r=s;const n=e!==o?`-${e}`:"";r&&c.push(!0===r?`${a}${n}`:`${a}${n}-${r}`),null!=d&&i.push(`order${n}-${d}`),null!=t&&i.push(`offset${n}-${t}`)}),[{...l,className:t()(r,...c,...i)},{as:s,bsPrefix:a,spans:c}]}(e);return(0,n.jsx)(l,{...r,ref:s,className:t()(a,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,s,a)=>{a.d(s,{A:()=>k});var r=a(8139),t=a.n(r),l=a(5043),d=a(7852),n=a(579);const o=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,d.oU)(r,"card-body"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});o.displayName="CardBody";const c=o,i=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,d.oU)(r,"card-footer"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});i.displayName="CardFooter";const f=i;var m=a(1778);const x=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,as:o="div",...c}=e;const i=(0,d.oU)(a,"card-header"),f=(0,l.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,n.jsx)(m.A.Provider,{value:f,children:(0,n.jsx)(o,{ref:s,...c,className:t()(r,i)})})});x.displayName="CardHeader";const u=x,b=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,variant:l,as:o="img",...c}=e;const i=(0,d.oU)(a,"card-img");return(0,n.jsx)(o,{ref:s,className:t()(l?`${i}-${l}`:i,r),...c})});b.displayName="CardImg";const h=b,p=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="div",...o}=e;return r=(0,d.oU)(r,"card-img-overlay"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});p.displayName="CardImgOverlay";const N=p,_=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="a",...o}=e;return r=(0,d.oU)(r,"card-link"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});_.displayName="CardLink";const j=_;var y=a(4488);const g=(0,y.A)("h6"),v=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l=g,...o}=e;return r=(0,d.oU)(r,"card-subtitle"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});v.displayName="CardSubtitle";const w=v,$=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l="p",...o}=e;return r=(0,d.oU)(r,"card-text"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});$.displayName="CardText";const A=$,P=(0,y.A)("h5"),R=l.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:l=P,...o}=e;return r=(0,d.oU)(r,"card-title"),(0,n.jsx)(l,{ref:s,className:t()(a,r),...o})});R.displayName="CardTitle";const C=R,U=l.forwardRef((e,s)=>{let{bsPrefix:a,className:r,bg:l,text:o,border:i,body:f=!1,children:m,as:x="div",...u}=e;const b=(0,d.oU)(a,"card");return(0,n.jsx)(x,{ref:s,...u,className:t()(r,b,l&&`bg-${l}`,o&&`text-${o}`,i&&`border-${i}`),children:f?(0,n.jsx)(c,{children:m}):m})});U.displayName="Card";const k=Object.assign(U,{Img:h,Title:C,Subtitle:w,Body:c,Link:j,Text:A,Header:u,Footer:f,ImgOverlay:N})}}]);
//# sourceMappingURL=560.90e2b36f.chunk.js.map