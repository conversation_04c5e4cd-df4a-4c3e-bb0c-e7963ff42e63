{"ast": null, "code": "import React from'react';import{useLocation,useNavigate}from'react-router-dom';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const RouterDebug=()=>{const location=useLocation();const navigate=useNavigate();const handleTestNavigation=()=>{console.log('Current location:',location);console.log('Attempting to navigate to /wallet');navigate('/wallet');};return/*#__PURE__*/_jsxs(\"div\",{style:{position:'fixed',top:'10px',right:'10px',background:'rgba(0,0,0,0.8)',color:'white',padding:'10px',borderRadius:'5px',fontSize:'12px',zIndex:9999},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"Current Path: \",location.pathname]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"Hash: \",location.hash]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"Search: \",location.search]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleTestNavigation,style:{marginTop:'5px'},children:\"Test Navigate to /wallet\"})]});};export default RouterDebug;", "map": {"version": 3, "names": ["React", "useLocation", "useNavigate", "jsxs", "_jsxs", "jsx", "_jsx", "RouterDebug", "location", "navigate", "handleTestNavigation", "console", "log", "style", "position", "top", "right", "background", "color", "padding", "borderRadius", "fontSize", "zIndex", "children", "pathname", "hash", "search", "onClick", "marginTop"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/components/RouterDebug.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nconst RouterDebug = () => {\n    const location = useLocation();\n    const navigate = useNavigate();\n\n    const handleTestNavigation = () => {\n        console.log('Current location:', location);\n        console.log('Attempting to navigate to /wallet');\n        navigate('/wallet');\n    };\n\n    return (\n        <div style={{ \n            position: 'fixed', \n            top: '10px', \n            right: '10px', \n            background: 'rgba(0,0,0,0.8)', \n            color: 'white', \n            padding: '10px',\n            borderRadius: '5px',\n            fontSize: '12px',\n            zIndex: 9999\n        }}>\n            <div>Current Path: {location.pathname}</div>\n            <div>Hash: {location.hash}</div>\n            <div>Search: {location.search}</div>\n            <button onClick={handleTestNavigation} style={{ marginTop: '5px' }}>\n                Test Navigate to /wallet\n            </button>\n        </div>\n    );\n};\n\nexport default RouterDebug;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAE5D,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAQ,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAQ,oBAAoB,CAAGA,CAAA,GAAM,CAC/BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEJ,QAAQ,CAAC,CAC1CG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChDH,QAAQ,CAAC,SAAS,CAAC,CACvB,CAAC,CAED,mBACIL,KAAA,QAAKS,KAAK,CAAE,CACRC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,iBAAiB,CAC7BC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,IACZ,CAAE,CAAAC,QAAA,eACEnB,KAAA,QAAAmB,QAAA,EAAK,gBAAc,CAACf,QAAQ,CAACgB,QAAQ,EAAM,CAAC,cAC5CpB,KAAA,QAAAmB,QAAA,EAAK,QAAM,CAACf,QAAQ,CAACiB,IAAI,EAAM,CAAC,cAChCrB,KAAA,QAAAmB,QAAA,EAAK,UAAQ,CAACf,QAAQ,CAACkB,MAAM,EAAM,CAAC,cACpCpB,IAAA,WAAQqB,OAAO,CAAEjB,oBAAqB,CAACG,KAAK,CAAE,CAAEe,SAAS,CAAE,KAAM,CAAE,CAAAL,QAAA,CAAC,0BAEpE,CAAQ,CAAC,EACR,CAAC,CAEd,CAAC,CAED,cAAe,CAAAhB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}