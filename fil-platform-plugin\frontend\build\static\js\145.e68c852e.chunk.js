"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[145],{5145:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});n(5043);var o=n(1283),s=n(579);const r=()=>{const e=(0,o.zy)(),t=(0,o.Zp)();return(0,s.jsxs)("div",{style:{position:"fixed",top:"10px",right:"10px",background:"rgba(0,0,0,0.8)",color:"white",padding:"10px",borderRadius:"5px",fontSize:"12px",zIndex:9999},children:[(0,s.jsxs)("div",{children:["Current Path: ",e.pathname]}),(0,s.jsxs)("div",{children:["Hash: ",e.hash]}),(0,s.jsxs)("div",{children:["Search: ",e.search]}),(0,s.jsx)("button",{onClick:()=>{console.log("Current location:",e),console.log("Attempting to navigate to /wallet"),t("/wallet")},style:{marginTop:"5px"},children:"Test Navigate to /wallet"})]})}}}]);
//# sourceMappingURL=145.e68c852e.chunk.js.map