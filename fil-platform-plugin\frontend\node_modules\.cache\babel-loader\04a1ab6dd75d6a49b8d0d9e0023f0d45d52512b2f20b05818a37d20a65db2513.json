{"ast": null, "code": "import React,{useState}from'react';import{<PERSON>,<PERSON><PERSON>,Card,Alert}from'react-bootstrap';import{useTranslation}from'react-i18next';import{getSupabase}from'../supabaseClient';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=()=>{const{t}=useTranslation();const[email,setEmail]=useState('');const[password,setPassword]=useState('');const[error,setError]=useState('');const[loading,setLoading]=useState(false);const handleSubmit=async e=>{e.preventDefault();setError('');setLoading(true);try{const supabase=getSupabase();const{data,error}=await supabase.auth.signInWithPassword({email:email,password:password});if(error){throw error;}console.log('Login Success:',data);// TODO: Handle successful login (e.g., save session, redirect based on role)\n}catch(err){setError(err.message||t('login_failed'));console.error('Login Error:',err);}setLoading(false);};return/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center align-items-center\",style:{minHeight:'80vh'},children:/*#__PURE__*/_jsx(Card,{style:{width:'400px'},children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-center mb-4\",children:t('login')}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Form.Group,{id:\"email\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('email_address')}),/*#__PURE__*/_jsx(Form.Control,{type:\"email\",required:true,value:email,onChange:e=>setEmail(e.target.value)})]}),/*#__PURE__*/_jsxs(Form.Group,{id:\"password\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('password')}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",required:true,value:password,onChange:e=>setPassword(e.target.value)})]}),/*#__PURE__*/_jsx(Button,{disabled:loading,className:\"w-100 mt-3\",type:\"submit\",children:loading?t('logging_in'):t('login')})]})]})})});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "useTranslation", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "t", "email", "setEmail", "password", "setPassword", "error", "setError", "loading", "setLoading", "handleSubmit", "e", "preventDefault", "supabase", "data", "auth", "signInWithPassword", "console", "log", "err", "message", "className", "style", "minHeight", "children", "width", "Body", "variant", "onSubmit", "Group", "id", "Label", "Control", "type", "required", "value", "onChange", "target", "disabled"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/LoginPage.js"], "sourcesContent": ["\nimport React, { useState } from 'react';\nimport { Form, Button, Card, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../supabaseClient';\n\nconst LoginPage = () => {\n    const { t } = useTranslation();\n    const [email, setEmail] = useState('');\n    const [password, setPassword] = useState('');\n    const [error, setError] = useState('');\n    const [loading, setLoading] = useState(false);\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n\n        try {\n            const supabase = getSupabase();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: email,\n                password: password,\n            });\n\n            if (error) {\n                throw error;\n            }\n\n            console.log('Login Success:', data);\n            // TODO: Handle successful login (e.g., save session, redirect based on role)\n\n        } catch (err) {\n            setError(err.message || t('login_failed'));\n            console.error('Login Error:', err);\n        }\n\n        setLoading(false);\n    };\n\n    return (\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '80vh' }}>\n            <Card style={{ width: '400px' }}>\n                <Card.Body>\n                    <h2 className=\"text-center mb-4\">{t('login')}</h2>\n                    {error && <Alert variant=\"danger\">{error}</Alert>}\n                    <Form onSubmit={handleSubmit}>\n                        <Form.Group id=\"email\">\n                            <Form.Label>{t('email_address')}</Form.Label>\n                            <Form.Control type=\"email\" required value={email} onChange={(e) => setEmail(e.target.value)} />\n                        </Form.Group>\n                        <Form.Group id=\"password\">\n                            <Form.Label>{t('password')}</Form.Label>\n                            <Form.Control type=\"password\" required value={password} onChange={(e) => setPassword(e.target.value)} />\n                        </Form.Group>\n                        <Button disabled={loading} className=\"w-100 mt-3\" type=\"submit\">\n                            {loading ? t('logging_in') : t('login')}\n                        </Button>\n                    </Form>\n                </Card.Body>\n            </Card>\n        </div>\n    );\n};\n\nexport default LoginPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,KAAQ,iBAAiB,CAC3D,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAEC,CAAE,CAAC,CAAGP,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACQ,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACe,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAAqB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBL,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACA,KAAM,CAAAI,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmB,IAAI,CAAER,KAAM,CAAC,CAAG,KAAM,CAAAO,QAAQ,CAACE,IAAI,CAACC,kBAAkB,CAAC,CAC3Dd,KAAK,CAAEA,KAAK,CACZE,QAAQ,CAAEA,QACd,CAAC,CAAC,CAEF,GAAIE,KAAK,CAAE,CACP,KAAM,CAAAA,KAAK,CACf,CAEAW,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEJ,IAAI,CAAC,CACnC;AAEJ,CAAE,MAAOK,GAAG,CAAE,CACVZ,QAAQ,CAACY,GAAG,CAACC,OAAO,EAAInB,CAAC,CAAC,cAAc,CAAC,CAAC,CAC1CgB,OAAO,CAACX,KAAK,CAAC,cAAc,CAAEa,GAAG,CAAC,CACtC,CAEAV,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,mBACIZ,IAAA,QAAKwB,SAAS,CAAC,kDAAkD,CAACC,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAC,QAAA,cAC3F3B,IAAA,CAACL,IAAI,EAAC8B,KAAK,CAAE,CAAEG,KAAK,CAAE,OAAQ,CAAE,CAAAD,QAAA,cAC5BzB,KAAA,CAACP,IAAI,CAACkC,IAAI,EAAAF,QAAA,eACN3B,IAAA,OAAIwB,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAEvB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,CACjDK,KAAK,eAAIT,IAAA,CAACJ,KAAK,EAACkC,OAAO,CAAC,QAAQ,CAAAH,QAAA,CAAElB,KAAK,CAAQ,CAAC,cACjDP,KAAA,CAACT,IAAI,EAACsC,QAAQ,CAAElB,YAAa,CAAAc,QAAA,eACzBzB,KAAA,CAACT,IAAI,CAACuC,KAAK,EAACC,EAAE,CAAC,OAAO,CAAAN,QAAA,eAClB3B,IAAA,CAACP,IAAI,CAACyC,KAAK,EAAAP,QAAA,CAAEvB,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CJ,IAAA,CAACP,IAAI,CAAC0C,OAAO,EAACC,IAAI,CAAC,OAAO,CAACC,QAAQ,MAACC,KAAK,CAAEjC,KAAM,CAACkC,QAAQ,CAAGzB,CAAC,EAAKR,QAAQ,CAACQ,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE,CAAE,CAAC,EACvF,CAAC,cACbpC,KAAA,CAACT,IAAI,CAACuC,KAAK,EAACC,EAAE,CAAC,UAAU,CAAAN,QAAA,eACrB3B,IAAA,CAACP,IAAI,CAACyC,KAAK,EAAAP,QAAA,CAAEvB,CAAC,CAAC,UAAU,CAAC,CAAa,CAAC,cACxCJ,IAAA,CAACP,IAAI,CAAC0C,OAAO,EAACC,IAAI,CAAC,UAAU,CAACC,QAAQ,MAACC,KAAK,CAAE/B,QAAS,CAACgC,QAAQ,CAAGzB,CAAC,EAAKN,WAAW,CAACM,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE,CAAE,CAAC,EAChG,CAAC,cACbtC,IAAA,CAACN,MAAM,EAAC+C,QAAQ,CAAE9B,OAAQ,CAACa,SAAS,CAAC,YAAY,CAACY,IAAI,CAAC,QAAQ,CAAAT,QAAA,CAC1DhB,OAAO,CAAGP,CAAC,CAAC,YAAY,CAAC,CAAGA,CAAC,CAAC,OAAO,CAAC,CACnC,CAAC,EACP,CAAC,EACA,CAAC,CACV,CAAC,CACN,CAAC,CAEd,CAAC,CAED,cAAe,CAAAD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}