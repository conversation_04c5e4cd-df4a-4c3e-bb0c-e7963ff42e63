{"ast": null, "code": "import React,{Suspense,useEffect,useState}from'react';import{initSupabase}from'./supabaseClient';import{HashRouter,Routes,Route,Link}from'react-router-dom';import{Container,Navbar,Nav,NavDropdown}from'react-bootstrap';import{useTranslation}from'react-i18next';// Lazy load components for better performance\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=/*#__PURE__*/React.lazy(()=>import('./pages/LoginPage'));const CustomerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Dashboard'));const ProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ProductListPage'));const OrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/OrderListPage'));const WalletPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/WalletPage'));const MyAccountPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyAccountPage'));const MyGainsPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyGainsPage'));const KycPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/KycPage'));const RecommendPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/RecommendPage'));const AgentDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Dashboard'));const AgentMemberList=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentMemberList'));const AgentProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentProductListPage'));const MakerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Dashboard'));const MakerProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerProductListPage'));const MakerOrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerOrderListPage'));// Debug component\nconst RouterDebug=/*#__PURE__*/React.lazy(()=>import('./components/RouterDebug'));function App(){const{t,i18n}=useTranslation();const[supabase,setSupabase]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{const initialize=async()=>{const supa=await initSupabase();setSupabase(supa);setLoading(false);};initialize();},[]);const changeLanguage=lng=>{i18n.changeLanguage(lng);};// Debug: Log current URL and hash\nReact.useEffect(()=>{console.log('App mounted. Current URL:',window.location.href);console.log('Hash:',window.location.hash);},[]);return/*#__PURE__*/_jsx(HashRouter,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Navbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Navbar.Brand,{as:Link,to:\"/\",children:\"FIL Platform\"}),/*#__PURE__*/_jsx(Navbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(Navbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsx(Nav,{className:\"me-auto\",children:/*#__PURE__*/_jsx(Nav.Link,{href:\"#/\",children:t('dashboard')})}),/*#__PURE__*/_jsx(Nav,{children:/*#__PURE__*/_jsxs(NavDropdown,{title:t('language'),id:\"basic-nav-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('en'),children:\"English\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('zh'),children:\"\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('ja'),children:\"\\u65E5\\u672C\\u8A9E\"})]})})]})]})}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsxs(Suspense,{fallback:/*#__PURE__*/_jsx(\"div\",{children:t('loading')}),children:[/*#__PURE__*/_jsx(RouterDebug,{}),loading?/*#__PURE__*/_jsx(\"div\",{children:t('initializing_platform')}):!supabase?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:t('backend_connection_failed')}):/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(CustomerDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(ProductListPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(OrderListPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/wallet\",element:/*#__PURE__*/_jsx(WalletPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/my\",element:/*#__PURE__*/_jsx(MyAccountPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/my-gains\",element:/*#__PURE__*/_jsx(MyGainsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/my/kyc\",element:/*#__PURE__*/_jsx(KycPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/my/recommend\",element:/*#__PURE__*/_jsx(RecommendPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/agent\",element:/*#__PURE__*/_jsx(AgentDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/members\",element:/*#__PURE__*/_jsx(AgentMemberList,{})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/products\",element:/*#__PURE__*/_jsx(AgentProductListPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/maker\",element:/*#__PURE__*/_jsx(MakerDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/products\",element:/*#__PURE__*/_jsx(MakerProductListPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/orders\",element:/*#__PURE__*/_jsx(MakerOrderListPage,{})})]})]})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "initSupabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Link", "Container", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "lazy", "CustomerDashboard", "ProductListPage", "OrderListPage", "WalletPage", "MyAccountPage", "MyGainsPage", "KycPage", "RecommendPage", "AgentDashboard", "AgentMemberList", "AgentProductListPage", "MakerDashboard", "MakerProductListPage", "MakerOrderListPage", "RouterDebug", "App", "t", "i18n", "supabase", "set<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "initialize", "supa", "changeLanguage", "lng", "console", "log", "window", "location", "href", "hash", "children", "bg", "variant", "expand", "Brand", "as", "to", "Toggle", "Collapse", "id", "className", "title", "<PERSON><PERSON>", "onClick", "fallback", "path", "element"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/App.js"], "sourcesContent": ["import React, { Suspense, useEffect, useState } from 'react';\nimport { initSupabase } from './supabaseClient';\nimport { HashRouter, Routes, Route, Link } from 'react-router-dom';\nimport { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\n\n// Lazy load components for better performance\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'));\nconst CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));\nconst ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));\nconst OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));\nconst WalletPage = React.lazy(() => import('./pages/customer/WalletPage'));\nconst MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));\nconst MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));\nconst KycPage = React.lazy(() => import('./pages/customer/KycPage'));\nconst RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));\nconst AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));\nconst AgentMemberList = React.lazy(() => import('./pages/agent/AgentMemberList'));\nconst AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));\nconst MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));\nconst MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));\nconst MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));\n\n// Debug component\nconst RouterDebug = React.lazy(() => import('./components/RouterDebug'));\n\nfunction App() {\n  const { t, i18n } = useTranslation();\n  const [supabase, setSupabase] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initialize = async () => {\n      const supa = await initSupabase();\n      setSupabase(supa);\n      setLoading(false);\n    };\n    initialize();\n  }, []);\n\n  const changeLanguage = (lng) => {\n    i18n.changeLanguage(lng);\n  };\n\n  // Debug: Log current URL and hash\n  React.useEffect(() => {\n    console.log('App mounted. Current URL:', window.location.href);\n    console.log('Hash:', window.location.hash);\n  }, []);\n\n  return (\n    <HashRouter>\n      <div>\n        <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n        <Container>\n          <Navbar.Brand as={Link} to=\"/\">FIL Platform</Navbar.Brand>\n          <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n          <Navbar.Collapse id=\"basic-navbar-nav\">\n            <Nav className=\"me-auto\">\n              <Nav.Link href=\"#/\">{t('dashboard')}</Nav.Link>\n              {/* Add other nav links based on role later */}\n            </Nav>\n            <Nav>\n                <NavDropdown title={t('language')} id=\"basic-nav-dropdown\">\n                    <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>\n                    <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>\n                    <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>\n                </NavDropdown>\n            </Nav>\n          </Navbar.Collapse>\n        </Container>\n      </Navbar>\n\n      <Container className=\"mt-4\">\n        <Suspense fallback={<div>{t('loading')}</div>}>\n          <RouterDebug />\n          {loading ? (\n            <div>{t('initializing_platform')}</div>\n          ) : !supabase ? (\n            <div className=\"alert alert-danger\">{t('backend_connection_failed')}</div>\n          ) : (\n            <Routes>\n              {/* Common Route */}\n              <Route path=\"/login\" element={<LoginPage />} />\n\n              {/* Customer Routes */}\n              <Route path=\"/\" element={<CustomerDashboard />} />\n            <Route path=\"/products\" element={<ProductListPage />} />\n            <Route path=\"/orders\" element={<OrderListPage />} />\n            <Route path=\"/wallet\" element={<WalletPage />} />\n            <Route path=\"/my\" element={<MyAccountPage />} />\n            <Route path=\"/my-gains\" element={<MyGainsPage />} />\n            <Route path=\"/my/kyc\" element={<KycPage />} />\n            <Route path=\"/my/recommend\" element={<RecommendPage />} />\n              {/* e.g., <Route path=\"/orders\" element={<CustomerOrders />} /> */}\n\n              {/* Agent Routes */}\n              <Route path=\"/agent\" element={<AgentDashboard />} />\n            <Route path=\"/agent/members\" element={<AgentMemberList />} />\n            <Route path=\"/agent/products\" element={<AgentProductListPage />} />\n              {/* e.g., <Route path=\"/agent/members\" element={<AgentMembers />} /> */}\n\n              {/* Maker Routes */}\n              <Route path=\"/maker\" element={<MakerDashboard />} />\n            <Route path=\"/maker/products\" element={<MakerProductListPage />} />\n            <Route path=\"/maker/orders\" element={<MakerOrderListPage />} />\n              {/* e.g., <Route path=\"/maker/products\" element={<MakerProducts />} /> */}\n            </Routes>\n          )}\n        </Suspense>\n      </Container>\n      </div>\n    </HashRouter>\n  );\n}\n\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,YAAY,KAAQ,kBAAkB,CAC/C,OAASC,UAAU,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,KAAQ,kBAAkB,CAClE,OAASC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,WAAW,KAAQ,iBAAiB,CACrE,OAASC,cAAc,KAAQ,eAAe,CAE9C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,cAAGlB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,iBAAiB,cAAGpB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAE,eAAe,cAAGrB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAG,aAAa,cAAGtB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAI,UAAU,cAAGvB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC1E,KAAM,CAAAK,aAAa,cAAGxB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAM,WAAW,cAAGzB,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC5E,KAAM,CAAAO,OAAO,cAAG1B,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACpE,KAAM,CAAAQ,aAAa,cAAG3B,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAS,cAAc,cAAG5B,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAU,eAAe,cAAG7B,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACjF,KAAM,CAAAW,oBAAoB,cAAG9B,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAY,cAAc,cAAG/B,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAa,oBAAoB,cAAGhC,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAc,kBAAkB,cAAGjC,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAEvF;AACA,KAAM,CAAAe,WAAW,cAAGlC,KAAK,CAACmB,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAExE,QAAS,CAAAgB,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGxB,cAAc,CAAC,CAAC,CACpC,KAAM,CAACyB,QAAQ,CAAEC,WAAW,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAvC,YAAY,CAAC,CAAC,CACjCmC,WAAW,CAACI,IAAI,CAAC,CACjBF,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACDC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAE,cAAc,CAAIC,GAAG,EAAK,CAC9BR,IAAI,CAACO,cAAc,CAACC,GAAG,CAAC,CAC1B,CAAC,CAED;AACA7C,KAAK,CAACE,SAAS,CAAC,IAAM,CACpB4C,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC9DJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEC,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAC,CAC5C,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEpC,IAAA,CAACV,UAAU,EAAA+C,QAAA,cACTnC,KAAA,QAAAmC,QAAA,eACErC,IAAA,CAACL,MAAM,EAAC2C,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAAH,QAAA,cAC5CnC,KAAA,CAACR,SAAS,EAAA2C,QAAA,eACRrC,IAAA,CAACL,MAAM,CAAC8C,KAAK,EAACC,EAAE,CAAEjD,IAAK,CAACkD,EAAE,CAAC,GAAG,CAAAN,QAAA,CAAC,cAAY,CAAc,CAAC,cAC1DrC,IAAA,CAACL,MAAM,CAACiD,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAClD1C,KAAA,CAACP,MAAM,CAACkD,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAT,QAAA,eACpCrC,IAAA,CAACJ,GAAG,EAACmD,SAAS,CAAC,SAAS,CAAAV,QAAA,cACtBrC,IAAA,CAACJ,GAAG,CAACH,IAAI,EAAC0C,IAAI,CAAC,IAAI,CAAAE,QAAA,CAAEhB,CAAC,CAAC,WAAW,CAAC,CAAW,CAAC,CAE5C,CAAC,cACNrB,IAAA,CAACJ,GAAG,EAAAyC,QAAA,cACAnC,KAAA,CAACL,WAAW,EAACmD,KAAK,CAAE3B,CAAC,CAAC,UAAU,CAAE,CAACyB,EAAE,CAAC,oBAAoB,CAAAT,QAAA,eACtDrC,IAAA,CAACH,WAAW,CAACoD,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMrB,cAAc,CAAC,IAAI,CAAE,CAAAQ,QAAA,CAAC,SAAO,CAAkB,CAAC,cACjFrC,IAAA,CAACH,WAAW,CAACoD,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMrB,cAAc,CAAC,IAAI,CAAE,CAAAQ,QAAA,CAAC,cAAE,CAAkB,CAAC,cAC5ErC,IAAA,CAACH,WAAW,CAACoD,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMrB,cAAc,CAAC,IAAI,CAAE,CAAAQ,QAAA,CAAC,oBAAG,CAAkB,CAAC,EACpE,CAAC,CACb,CAAC,EACS,CAAC,EACT,CAAC,CACN,CAAC,cAETrC,IAAA,CAACN,SAAS,EAACqD,SAAS,CAAC,MAAM,CAAAV,QAAA,cACzBnC,KAAA,CAAChB,QAAQ,EAACiE,QAAQ,cAAEnD,IAAA,QAAAqC,QAAA,CAAMhB,CAAC,CAAC,SAAS,CAAC,CAAM,CAAE,CAAAgB,QAAA,eAC5CrC,IAAA,CAACmB,WAAW,GAAE,CAAC,CACdM,OAAO,cACNzB,IAAA,QAAAqC,QAAA,CAAMhB,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CACrC,CAACE,QAAQ,cACXvB,IAAA,QAAK+C,SAAS,CAAC,oBAAoB,CAAAV,QAAA,CAAEhB,CAAC,CAAC,2BAA2B,CAAC,CAAM,CAAC,cAE1EnB,KAAA,CAACX,MAAM,EAAA8C,QAAA,eAELrC,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAErD,IAAA,CAACG,SAAS,GAAE,CAAE,CAAE,CAAC,cAG/CH,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,GAAG,CAACC,OAAO,cAAErD,IAAA,CAACK,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACpDL,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,WAAW,CAACC,OAAO,cAAErD,IAAA,CAACM,eAAe,GAAE,CAAE,CAAE,CAAC,cACxDN,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,SAAS,CAACC,OAAO,cAAErD,IAAA,CAACO,aAAa,GAAE,CAAE,CAAE,CAAC,cACpDP,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,SAAS,CAACC,OAAO,cAAErD,IAAA,CAACQ,UAAU,GAAE,CAAE,CAAE,CAAC,cACjDR,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,KAAK,CAACC,OAAO,cAAErD,IAAA,CAACS,aAAa,GAAE,CAAE,CAAE,CAAC,cAChDT,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,WAAW,CAACC,OAAO,cAAErD,IAAA,CAACU,WAAW,GAAE,CAAE,CAAE,CAAC,cACpDV,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,SAAS,CAACC,OAAO,cAAErD,IAAA,CAACW,OAAO,GAAE,CAAE,CAAE,CAAC,cAC9CX,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,eAAe,CAACC,OAAO,cAAErD,IAAA,CAACY,aAAa,GAAE,CAAE,CAAE,CAAC,cAIxDZ,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAErD,IAAA,CAACa,cAAc,GAAE,CAAE,CAAE,CAAC,cACtDb,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAErD,IAAA,CAACc,eAAe,GAAE,CAAE,CAAE,CAAC,cAC7Dd,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAErD,IAAA,CAACe,oBAAoB,GAAE,CAAE,CAAE,CAAC,cAIjEf,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAErD,IAAA,CAACgB,cAAc,GAAE,CAAE,CAAE,CAAC,cACtDhB,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAErD,IAAA,CAACiB,oBAAoB,GAAE,CAAE,CAAE,CAAC,cACnEjB,IAAA,CAACR,KAAK,EAAC4D,IAAI,CAAC,eAAe,CAACC,OAAO,cAAErD,IAAA,CAACkB,kBAAkB,GAAE,CAAE,CAAE,CAAC,EAEvD,CACT,EACO,CAAC,CACF,CAAC,EACP,CAAC,CACI,CAAC,CAEjB,CAEA,cAAe,CAAAE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}