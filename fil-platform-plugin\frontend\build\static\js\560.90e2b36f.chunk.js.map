{"version": 3, "file": "static/js/560.90e2b36f.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sJCjCA,MA6EA,EA7EwBC,KACpB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAoCvC,OAlCAG,EAAAA,EAAAA,WAAU,KACgBC,WAClB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAM,KAAEK,EAAI,MAAEC,SAAgBH,EACzBI,KAAK,YACLC,OAAO,4YAYPC,GAAG,eAAe,GAClBC,MAAM,aAAc,CAAEC,WAAW,IAElCL,EACAM,QAAQN,MAAM,2BAA4BA,GAE1CT,EAAYQ,GAEhBL,GAAW,IAGfa,IACD,IAECd,GACOT,EAAAA,EAAAA,KAAA,OAAAwB,SAAMpB,EAAE,uBAIfqB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACNxB,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM0C,SAAEpB,EAAE,qBACxBJ,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAA+C,SACClB,EAASqB,IAAIC,IAAO,IAAAC,EAAA,OACjB7B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAG,CAACC,GAAI,EAAoBjD,UAAU,OAAM0C,UACzCxB,EAAAA,EAAAA,KAACgC,EAAAA,EAAI,CAAClD,UAAW8C,EAAQK,YAAc,WAAa,GAAGT,UACnDC,EAAAA,EAAAA,MAACO,EAAAA,EAAKE,KAAI,CAAAV,SAAA,EACNC,EAAAA,EAAAA,MAACO,EAAAA,EAAKG,MAAK,CAACrD,UAAU,iCAAgC0C,SAAA,CACjDI,EAAQQ,KACa,SAArBR,EAAQS,UACLrC,EAAAA,EAAAA,KAACsC,EAAAA,EAAK,CAACC,GAAG,UAASf,SAAEpB,EAAE,oBACvBJ,EAAAA,EAAAA,KAACsC,EAAAA,EAAK,CAACC,GAAG,UAASf,SAAEpB,EAAE,0BAE/BqB,EAAAA,EAAAA,MAACO,EAAAA,EAAKQ,SAAQ,CAAC1D,UAAU,kBAAiB0C,SAAA,CAAEpB,EAAE,eAAe,KAAwB,QAAtByB,EAAAD,EAAQa,sBAAc,IAAAZ,OAAA,EAAtBA,EAAwBa,cAAetC,EAAE,gBACxGqB,EAAAA,EAAAA,MAACO,EAAAA,EAAKW,KAAI,CAAAnB,SAAA,EACNC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAASpB,EAAE,eAAe,OAAU,IAAEwB,EAAQgB,MAAM,IAAExC,EAAE,kBAAkB,KAACJ,EAAAA,EAAAA,KAAA,UAC3EyB,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAASpB,EAAE,sBAAsB,OAAU,IAAEwB,EAAQiB,aAAa,KAAC7C,EAAAA,EAAAA,KAAA,UACnEyB,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAASpB,EAAE,0BAA0B,OAAU,IAAEwB,EAAQiB,aAAejB,EAAQkB,YAAY,KAAC9C,EAAAA,EAAAA,KAAA,UAC7FyB,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAASpB,EAAE,sBAAsB,OAAU,IAAEwB,EAAQmB,aAAa,IAAE3C,EAAE,eAAe,KAACJ,EAAAA,EAAAA,KAAA,UACtFyB,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAASpB,EAAE,wBAAwB,OAAU,IAAEwB,EAAQoB,qBAAqB,IAAE5C,EAAE,iBAEpFJ,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,UAAUC,SAAUvB,EAAQK,aAAgBL,EAAQiB,aAAejB,EAAQkB,cAAgB,EAAGtB,SACxGI,EAAQiB,aAAejB,EAAQkB,cAAgB,EAAK1C,EAAE,YAAcA,EAAE,mBAlBvEwB,EAAQwB,W,sFChD7C,MAAMd,EAAqB5D,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACR0D,EAAK,UAAS,KACdc,GAAO,EAAK,KACZC,EAAI,UACJxE,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAM4E,GAASpE,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyE,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQf,GAAM,MAAMA,SAGzGD,EAAMpC,YAAc,QACpB,S,sFCuBA,MAAM4B,EAAmBpD,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACG0E,IAEHzE,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACR4E,IAjDG,SAAe9E,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBkE,EAAQ,GACRhE,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAI+D,EACAC,EACAvC,SAHGnC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjC8D,OACAC,SACAvC,SACExB,GAEJ8D,EAAO9D,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxD+D,GAAMD,EAAM1D,MAAc,IAAT2D,EAAgB,GAAG7E,IAAWiB,IAAU,GAAGjB,IAAWiB,KAAS4D,KACvE,MAATtC,GAAe3B,EAAQM,KAAK,QAAQD,KAASsB,KACnC,MAAVuC,GAAgBlE,EAAQM,KAAK,SAASD,KAAS6D,OAE9C,CAAC,IACH1E,EACHH,UAAWmB,IAAWnB,KAAc2E,KAAUhE,IAC7C,CACDV,KACAF,WACA4E,SAEJ,CAWOG,CAAO3E,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BwE,EACH5E,IAAKA,EACLE,UAAWmB,IAAWnB,GAAY2E,EAAMI,QAAUhF,OAGtDiD,EAAI5B,YAAc,MAClB,S,sFC1DA,MAAM4D,EAAwBpF,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP6E,EAAS5D,YAAc,WACvB,UCdM6D,EAA0BrF,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP8E,EAAW7D,YAAc,aACzB,U,cCZA,MAAM8D,EAA0BtF,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4E,GAASpE,EAAAA,EAAAA,IAAmBN,EAAU,eACtCoF,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBZ,IAClB,CAACA,IACL,OAAoBvD,EAAAA,EAAAA,KAAKoE,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPzC,UAAuBxB,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyE,SAIvCS,EAAW9D,YAAc,aACzB,UCvBMqE,EAAuB7F,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACToE,EACAnE,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4E,GAASpE,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWiD,EAAU,GAAGK,KAAUL,IAAYK,EAAQzE,MAC9DG,MAGPsF,EAAQrE,YAAc,UACtB,UCjBMsE,EAA8B9F,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPuF,EAAetE,YAAc,iBAC7B,UCdMuE,EAAwB/F,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPwF,EAASvE,YAAc,WACvB,U,cCbA,MAAMwE,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BlG,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAY0F,KACbzF,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP2F,EAAa1E,YAAc,eAC3B,UChBM2E,EAAwBnG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP4F,EAAS3E,YAAc,WACvB,UCbM4E,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBrG,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAY8F,KACb7F,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP8F,EAAU7E,YAAc,YACxB,UCPM8B,EAAoBtD,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACTyD,EAAE,KACFe,EAAI,OACJ0B,EAAM,KACNC,GAAO,EAAK,SACZzD,EAEAzC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4E,GAASpE,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyE,EAAQhB,GAAM,MAAMA,IAAMe,GAAQ,QAAQA,IAAQ0B,GAAU,UAAUA,KACvGxD,SAAUyD,GAAoBjF,EAAAA,EAAAA,KAAK8D,EAAU,CAC3CtC,SAAUA,IACPA,MAGTQ,EAAK9B,YAAc,OACnB,QAAegF,OAAOC,OAAOnD,EAAM,CACjCoD,IAAKb,EACLpC,MAAO4C,EACPvC,SAAUoC,EACV1C,KAAM4B,EACNuB,KAAMZ,EACN9B,KAAMkC,EACNS,OAAQtB,EACRuB,OAAQxB,EACRyB,WAAYhB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "pages/customer/ProductListPage.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../../supabaseClient';\n\nconst ProductListPage = () => {\n    const { t } = useTranslation();\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchProducts = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data, error } = await supabase\n                .from('products')\n                .select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    effective_delay_days,\n                    min_purchase,\n                    is_disabled,\n                    maker_profiles ( brand_name: user_id, maker_brand: domain ) \n                `)\n                .eq('is_disabled', false)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching products:', error);\n            } else {\n                setProducts(data);\n            }\n            setLoading(false);\n        };\n\n        fetchProducts();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_products')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('power_products')}</h2>\n            <Row>\n                {products.map(product => (\n                    <Col md={4} key={product.id} className=\"mb-4\">\n                        <Card className={product.is_disabled ? 'bg-light' : ''}>\n                            <Card.Body>\n                                <Card.Title className=\"d-flex justify-content-between\">\n                                    {product.name}\n                                    {product.category === 'spot' ?\n                                        <Badge bg=\"success\">{t('spot_category')}</Badge> :\n                                        <Badge bg=\"primary\">{t('futures_category')}</Badge>}\n                                </Card.Title>\n                                <Card.Subtitle className=\"mb-2 text-muted\">{t('provided_by')} {product.maker_profiles?.maker_brand || t('official')}</Card.Subtitle>\n                                <Card.Text>\n                                    <strong>{t('price_label')}:</strong> {product.price} {t('usdt_per_share')} <br />\n                                    <strong>{t('total_shares_label')}:</strong> {product.total_shares} <br />\n                                    <strong>{t('remaining_shares_label')}:</strong> {product.total_shares - product.sold_shares} <br />\n                                    <strong>{t('min_purchase_label')}:</strong> {product.min_purchase} {t('shares_unit')} <br />\n                                    <strong>{t('waiting_period_label')}:</strong> {product.effective_delay_days} {t('days_unit')}\n                                </Card.Text>\n                                <Button variant=\"primary\" disabled={product.is_disabled || (product.total_shares - product.sold_shares === 0)}>\n                                    {(product.total_shares - product.sold_shares === 0) ? t('sold_out') : t('buy_now')}\n                                </Button>\n                            </Card.Body>\n                        </Card>\n                    </Col>\n                ))}\n            </Row>\n        </Container>\n    );\n};\n\nexport default ProductListPage;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "ProductListPage", "t", "useTranslation", "products", "setProducts", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "error", "from", "select", "eq", "order", "ascending", "console", "fetchProducts", "children", "_jsxs", "Container", "map", "product", "_product$maker_profil", "Col", "md", "Card", "is_disabled", "Body", "Title", "name", "category", "Badge", "bg", "Subtitle", "maker_profiles", "maker_brand", "Text", "price", "total_shares", "sold_shares", "min_purchase", "effective_delay_days", "<PERSON><PERSON>", "variant", "disabled", "id", "pill", "text", "prefix", "colProps", "spans", "span", "offset", "useCol", "length", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Link", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}