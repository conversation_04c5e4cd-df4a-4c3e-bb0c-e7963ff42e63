
import { createClient } from '@supabase/supabase-js';

// This is a placeholder that will be replaced by the actual config from WordPress
let supabaseUrl = null;
let supabaseAnonKey = null;

let supabase = null;

export const initSupabase = async () => {
    if (supabase) {
        return supabase;
    }

    try {
        // Fetch the config from our WordPress backend
        const response = await fetch(window.wpData.apiUrl + 'config');
        const config = await response.json();

        if (!config.url || !config.anonKey) {
            throw new Error('Supabase config not found.');
        }

        supabaseUrl = config.url;
        supabaseAnonKey = config.anonKey;

        supabase = createClient(supabaseUrl, supabaseAnonKey);
        return supabase;

    } catch (error) {
        console.error("Failed to initialize Supabase client:", error);
        // You might want to show an error message to the user here
        return null;
    }
};

// Export a getter function to ensure Supabase is initialized before use
export const getSupabase = () => {
    if (!supabase) {
        console.error("Supabase has not been initialized. Call initSupabase() first.");
    }
    return supabase;
};
