import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { getSupabase } from '../../supabaseClient';

const MakerDashboard = () => {
    const { t } = useTranslation();
    const [makerProfile, setMakerProfile] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchMakerData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch maker profile
            const { data: profileData, error: profileError } = await supabase
                .from('maker_profiles')
                .select('*')
                .eq('user_id', user.id)
                .single();

            if (profileError) {
                console.error('Error fetching maker profile:', profileError);
            } else {
                setMakerProfile(profileData);
            }
            setLoading(false);
        };

        fetchMakerData();
    }, []);

    if (loading) {
        return <div>{t('loading_maker_dashboard')}</div>;
    }

    if (!makerProfile) {
        return <div className="alert alert-warning">{t('not_maker')}</div>;
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('maker_dashboard')}</h2>
                </Col>
            </Row>

            <Row>
                <Col md={4}>
                    <Card className="bg-primary text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('domain')}</Card.Title>
                            <h3>{makerProfile.domain || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-success text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('support_email')}</Card.Title>
                            <h3>{makerProfile.support_email || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-info text-white mb-3">
                        <Card.Body>
                            <Card.Title>{t('sms_signature')}</Card.Title>
                            <h3>{makerProfile.sms_signature || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('product_management')}</h4>
                            <p>{t('manage_your_products')}</p>
                            <Button as={Link} to="/maker/products" variant="primary">{t('enter_product_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('order_management')}</h4>
                            <p>{t('all_orders')}</p>
                            <Button as={Link} to="/maker/orders" variant="success">{t('enter_order_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MakerDashboard;