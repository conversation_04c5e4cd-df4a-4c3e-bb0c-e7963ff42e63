{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Button,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MakerProductListPage=()=>{const{t}=useTranslation();const[products,setProducts]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchProducts=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}const{data,error}=await supabase.from('products').select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    review_status,\n                    created_at\n                `).eq('maker_id',user.id).order('created_at',{ascending:false});if(error){console.error('Error fetching products:',error);}else{setProducts(data);}setLoading(false);};fetchProducts();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_products')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('my_products')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Button,{variant:\"success\",className:\"mb-3\",children:t('add_new_product')}),/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('product_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('product_name_header')}),/*#__PURE__*/_jsx(\"th\",{children:t('category')}),/*#__PURE__*/_jsx(\"th\",{children:t('price_per_share')}),/*#__PURE__*/_jsx(\"th\",{children:t('total_shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('sold_shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('status')}),/*#__PURE__*/_jsx(\"th\",{children:t('review_status')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')}),/*#__PURE__*/_jsx(\"th\",{children:t('actions')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:products.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"10\",className:\"text-center\",children:t('no_products_available')})}):products.map(product=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[product.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:product.name}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:product.category==='spot'?'success':'primary',children:product.category==='spot'?t('spot_category'):t('futures_category')})}),/*#__PURE__*/_jsx(\"td\",{children:product.price}),/*#__PURE__*/_jsx(\"td\",{children:product.total_shares}),/*#__PURE__*/_jsx(\"td\",{children:product.sold_shares}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:product.is_disabled?'danger':'success',children:product.is_disabled?t('disabled'):t('enabled')})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:product.review_status==='approved'?'success':'warning',children:product.review_status})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(product.created_at).toLocaleString()}),/*#__PURE__*/_jsxs(\"td\",{children:[/*#__PURE__*/_jsx(Button,{variant:\"info\",size:\"sm\",className:\"me-2\",children:t('edit')}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",children:t('delete')})]})]},product.id))})]})]})})})})]});};export default MakerProductListPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "MakerProductListPage", "t", "products", "setProducts", "loading", "setLoading", "fetchProducts", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "order", "ascending", "console", "children", "className", "Body", "variant", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "product", "substring", "name", "bg", "category", "price", "total_shares", "sold_shares", "is_disabled", "review_status", "Date", "created_at", "toLocaleString", "size"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/MakerProductListPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MakerProductListPage = () => {\n    const { t } = useTranslation();\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchProducts = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            const { data, error } = await supabase\n                .from('products')\n                .select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    review_status,\n                    created_at\n                `)\n                .eq('maker_id', user.id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching products:', error);\n            } else {\n                setProducts(data);\n            }\n            setLoading(false);\n        };\n\n        fetchProducts();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_products')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_products')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Button variant=\"success\" className=\"mb-3\">{t('add_new_product')}</Button>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('product_id')}</th>\n                                        <th>{t('product_name_header')}</th>\n                                        <th>{t('category')}</th>\n                                        <th>{t('price_per_share')}</th>\n                                        <th>{t('total_shares')}</th>\n                                        <th>{t('sold_shares')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('review_status')}</th>\n                                        <th>{t('created_at')}</th>\n                                        <th>{t('actions')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"10\" className=\"text-center\">{t('no_products_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        products.map(product => (\n                                            <tr key={product.id}>\n                                                <td>{product.id.substring(0, 8)}...</td>\n                                                <td>{product.name}</td>\n                                                <td><Badge bg={product.category === 'spot' ? 'success' : 'primary'}>{product.category === 'spot' ? t('spot_category') : t('futures_category')}</Badge></td>\n                                                <td>{product.price}</td>\n                                                <td>{product.total_shares}</td>\n                                                <td>{product.sold_shares}</td>\n                                                <td><Badge bg={product.is_disabled ? 'danger' : 'success'}>{product.is_disabled ? t('disabled') : t('enabled')}</Badge></td>\n                                                <td><Badge bg={product.review_status === 'approved' ? 'success' : 'warning'}>{product.review_status}</Badge></td>\n                                                <td>{new Date(product.created_at).toLocaleString()}</td>\n                                                <td>\n                                                    <Button variant=\"info\" size=\"sm\" className=\"me-2\">{t('edit')}</Button>\n                                                    <Button variant=\"danger\" size=\"sm\">{t('delete')}</Button>\n                                                </td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MakerProductListPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CAAEC,KAAK,KAAQ,iBAAiB,CACjF,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAoB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,EAAE,CAAC,UAAU,CAAEN,IAAI,CAACO,EAAE,CAAC,CACvBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,KAAK,CAAE,CACPO,OAAO,CAACP,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CACpD,CAAC,IAAM,CACHT,WAAW,CAACK,IAAI,CAAC,CACrB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,aAAa,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,kBAAkB,CAAC,CAAM,CAAC,CAC7C,CAEA,mBACIF,KAAA,CAACZ,SAAS,EAAAiC,QAAA,eACNvB,IAAA,OAAIwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC5CJ,IAAA,CAACT,GAAG,EAAAgC,QAAA,cACAvB,IAAA,CAACR,GAAG,EAAA+B,QAAA,cACAvB,IAAA,CAACP,IAAI,EAAA8B,QAAA,cACDrB,KAAA,CAACT,IAAI,CAACgC,IAAI,EAAAF,QAAA,eACNvB,IAAA,CAACL,MAAM,EAAC+B,OAAO,CAAC,SAAS,CAACF,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,iBAAiB,CAAC,CAAS,CAAC,cAC1EF,KAAA,CAACR,KAAK,EAACiC,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAP,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,qBAAqB,CAAC,CAAK,CAAC,cACnCJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAC/BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,EACvB,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,QAAQ,CAAC0B,MAAM,GAAK,CAAC,cAClB/B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAIgC,OAAO,CAAC,IAAI,CAACR,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEnB,CAAC,CAAC,uBAAuB,CAAC,CAAK,CAAC,CAC1E,CAAC,CAELC,QAAQ,CAAC4B,GAAG,CAACC,OAAO,eAChBhC,KAAA,OAAAqB,QAAA,eACIrB,KAAA,OAAAqB,QAAA,EAAKW,OAAO,CAACf,EAAE,CAACgB,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cACxCnC,IAAA,OAAAuB,QAAA,CAAKW,OAAO,CAACE,IAAI,CAAK,CAAC,cACvBpC,IAAA,OAAAuB,QAAA,cAAIvB,IAAA,CAACJ,KAAK,EAACyC,EAAE,CAAEH,OAAO,CAACI,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAU,CAAAf,QAAA,CAAEW,OAAO,CAACI,QAAQ,GAAK,MAAM,CAAGlC,CAAC,CAAC,eAAe,CAAC,CAAGA,CAAC,CAAC,kBAAkB,CAAC,CAAQ,CAAC,CAAI,CAAC,cAC3JJ,IAAA,OAAAuB,QAAA,CAAKW,OAAO,CAACK,KAAK,CAAK,CAAC,cACxBvC,IAAA,OAAAuB,QAAA,CAAKW,OAAO,CAACM,YAAY,CAAK,CAAC,cAC/BxC,IAAA,OAAAuB,QAAA,CAAKW,OAAO,CAACO,WAAW,CAAK,CAAC,cAC9BzC,IAAA,OAAAuB,QAAA,cAAIvB,IAAA,CAACJ,KAAK,EAACyC,EAAE,CAAEH,OAAO,CAACQ,WAAW,CAAG,QAAQ,CAAG,SAAU,CAAAnB,QAAA,CAAEW,OAAO,CAACQ,WAAW,CAAGtC,CAAC,CAAC,UAAU,CAAC,CAAGA,CAAC,CAAC,SAAS,CAAC,CAAQ,CAAC,CAAI,CAAC,cAC5HJ,IAAA,OAAAuB,QAAA,cAAIvB,IAAA,CAACJ,KAAK,EAACyC,EAAE,CAAEH,OAAO,CAACS,aAAa,GAAK,UAAU,CAAG,SAAS,CAAG,SAAU,CAAApB,QAAA,CAAEW,OAAO,CAACS,aAAa,CAAQ,CAAC,CAAI,CAAC,cACjH3C,IAAA,OAAAuB,QAAA,CAAK,GAAI,CAAAqB,IAAI,CAACV,OAAO,CAACW,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,cACxD5C,KAAA,OAAAqB,QAAA,eACIvB,IAAA,CAACL,MAAM,EAAC+B,OAAO,CAAC,MAAM,CAACqB,IAAI,CAAC,IAAI,CAACvB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,MAAM,CAAC,CAAS,CAAC,cACtEJ,IAAA,CAACL,MAAM,EAAC+B,OAAO,CAAC,QAAQ,CAACqB,IAAI,CAAC,IAAI,CAAAxB,QAAA,CAAEnB,CAAC,CAAC,QAAQ,CAAC,CAAS,CAAC,EACzD,CAAC,GAbA8B,OAAO,CAACf,EAcb,CACP,CACJ,CACE,CAAC,EACL,CAAC,EACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAhB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}