{"version": 3, "file": "static/js/505.ade57923.chunk.js", "mappings": "uOAMA,MAuGA,EAvGuBA,KACnB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,OAC1CC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAiCvC,OA/BAG,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAQK,KAAMI,EAAaC,MAAOC,SAAuBR,EACpDS,KAAK,kBACLC,OAAO,iCACPC,GAAG,UAAWR,EAAKS,IACnBC,SAEDL,EACAM,QAAQP,MAAM,gCAAiCC,GAE/Cd,EAAgBY,GAEpBT,GAAW,IAGfkB,IACD,IAECnB,GACOoB,EAAAA,EAAAA,KAAA,OAAAC,SAAM1B,EAAE,6BAGdE,GAKDyB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAACC,OAAK,EAAAH,SAAA,EACZD,EAAAA,EAAAA,KAACK,EAAAA,EAAG,CAACC,UAAU,OAAML,UACjBD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAAAN,UACAD,EAAAA,EAAAA,KAAA,MAAAC,SAAK1B,EAAE,0BAIf2B,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAAAJ,SAAA,EACAD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,6BAA4BL,UACxCC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE1B,EAAE,iBACfyB,EAAAA,EAAAA,KAAA,MAAAC,SAAKxB,EAAamC,YAAc,gBAI5CZ,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,6BAA4BL,UACxCC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE1B,EAAE,sBACfyB,EAAAA,EAAAA,KAAA,MAAAC,SAAKxB,EAAaoC,eAAkD,IAA9BpC,EAAaoC,eAAhB,IAA0C,gBAIzFb,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAEP,UACPD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAACH,UAAU,0BAAyBL,UACrCC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAACS,EAAAA,EAAKE,MAAK,CAAAV,SAAE1B,EAAE,iBACfyB,EAAAA,EAAAA,KAAA,MAAAC,SAAKxB,EAAaqC,YAAc,mBAMhDZ,EAAAA,EAAAA,MAACG,EAAAA,EAAG,CAACC,UAAU,OAAML,SAAA,EACjBD,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAGF,UAAU,cAAaL,UAC/BD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAAAR,UACDC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAAC,SAAK1B,EAAE,wBACPyB,EAAAA,EAAAA,KAAA,KAAAC,SAAI1B,EAAE,6BACNyB,EAAAA,EAAAA,KAACe,EAAAA,EAAM,CAACC,GAAIC,EAAAA,GAAMC,GAAG,iBAAiBC,QAAQ,UAASlB,SAAE1B,EAAE,+BAIvEyB,EAAAA,EAAAA,KAACO,EAAAA,EAAG,CAACC,GAAI,EAAGF,UAAU,cAAaL,UAC/BD,EAAAA,EAAAA,KAACS,EAAAA,EAAI,CAAAR,UACDC,EAAAA,EAAAA,MAACO,EAAAA,EAAKC,KAAI,CAAAT,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAAC,SAAK1B,EAAE,yBACPyB,EAAAA,EAAAA,KAAA,KAAAC,SAAI1B,EAAE,uBACNyB,EAAAA,EAAAA,KAACe,EAAAA,EAAM,CAACC,GAAIC,EAAAA,GAAMC,GAAG,kBAAkBC,QAAQ,UAASlB,SAAE1B,EAAE,yCArDzEyB,EAAAA,EAAAA,KAAA,OAAKM,UAAU,sBAAqBL,SAAE1B,EAAE,e,sFCzCvD,MAAM8B,EAAmBe,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRjB,EAEAU,GAAIQ,EAAY,SACbC,GACJJ,EACC,MAAMK,GAAoBC,EAAAA,EAAAA,IAAmBJ,EAAU,OACjDK,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCrC,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,KACFG,EACHnB,UAAWkC,IAAWlC,EAAWoB,KAAsBO,OAG3D5B,EAAIoC,YAAc,MAClB,S,sFCOA,MAAMlC,EAAmBa,EAAAA,WAEzB,CAACK,EAAOH,KACN,OAAO,UACLhB,KACGoC,IAEH1B,GAAIQ,EAAY,MAAK,SACrBD,EAAQ,MACRoB,IAjDG,SAAetB,GAKnB,IALoB,GACrBL,EAAE,SACFO,EAAQ,UACRjB,KACGmB,GACJJ,EACCE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,OACxC,MAAMK,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBY,EAAQ,GACRV,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIS,EACAC,EACAC,SAHGrB,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCQ,OACAC,SACAC,SACEV,GAEJQ,EAAOR,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDS,GAAMD,EAAMJ,MAAc,IAATK,EAAgB,GAAGrB,IAAWe,IAAU,GAAGf,IAAWe,KAASM,KACvE,MAATE,GAAeb,EAAQM,KAAK,QAAQD,KAASQ,KACnC,MAAVD,GAAgBZ,EAAQM,KAAK,SAASD,KAASO,OAE9C,CAAC,IACHpB,EACHnB,UAAWkC,IAAWlC,KAAcqC,KAAUV,IAC7C,CACDjB,KACAO,WACAoB,SAEJ,CAWOI,CAAOtB,GACZ,OAAoBzB,EAAAA,EAAAA,KAAKwB,EAAW,IAC/BkB,EACHpB,IAAKA,EACLhB,UAAWkC,IAAWlC,GAAYqC,EAAMK,QAAUzB,OAGtDhB,EAAIkC,YAAc,MAClB,S,sFC1DA,MAAMQ,EAAwB7B,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ChB,EAAS,SACTiB,EACAP,GAAIQ,EAAY,SACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,cACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGPwB,EAASR,YAAc,WACvB,UCdMS,EAA0B9B,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDhB,EAAS,SACTiB,EACAP,GAAIQ,EAAY,SACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,gBACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGPyB,EAAWT,YAAc,aACzB,U,cCZA,MAAMU,EAA0B/B,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRjB,EAEAU,GAAIQ,EAAY,SACbC,GACJJ,EACC,MAAM+B,GAASzB,EAAAA,EAAAA,IAAmBJ,EAAU,eACtC8B,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoBpD,EAAAA,EAAAA,KAAKwD,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPpD,UAAuBD,EAAAA,EAAAA,KAAKwB,EAAW,CACrCF,IAAKA,KACFG,EACHnB,UAAWkC,IAAWlC,EAAW8C,SAIvCD,EAAWV,YAAc,aACzB,UCvBMkB,EAAuBvC,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRjB,EAAS,QACTa,EACAH,GAAIQ,EAAY,SACbC,GACJJ,EACC,MAAM+B,GAASzB,EAAAA,EAAAA,IAAmBJ,EAAU,YAC5C,OAAoBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWrB,EAAU,GAAGiC,KAAUjC,IAAYiC,EAAQ9C,MAC9DmB,MAGPkC,EAAQlB,YAAc,UACtB,UCjBMmB,EAA8BxC,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDhB,EAAS,SACTiB,EACAP,GAAIQ,EAAY,SACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,qBACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGPmC,EAAenB,YAAc,iBAC7B,UCdMoB,EAAwBzC,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ChB,EAAS,SACTiB,EACAP,GAAIQ,EAAY,OACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,cACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGPoC,EAASpB,YAAc,WACvB,U,cCbA,MAAMqB,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4B5C,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDhB,EAAS,SACTiB,EACAP,GAAIQ,EAAYsC,KACbrC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,kBACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGPuC,EAAavB,YAAc,eAC3B,UChBMwB,EAAwB7C,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ChB,EAAS,SACTiB,EACAP,GAAIQ,EAAY,OACbC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,cACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGPwC,EAASxB,YAAc,WACvB,UCbMyB,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB/C,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/ChB,EAAS,SACTiB,EACAP,GAAIQ,EAAY0C,KACbzC,GACJJ,EAEC,OADAE,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,eACpBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,EACLhB,UAAWkC,IAAWlC,EAAWiB,MAC9BE,MAGP0C,EAAU1B,YAAc,YACxB,UCPMhC,EAAoBW,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRjB,EAAS,GACT8D,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZtE,EAEAe,GAAIQ,EAAY,SACbC,GACJJ,EACC,MAAM+B,GAASzB,EAAAA,EAAAA,IAAmBJ,EAAU,QAC5C,OAAoBvB,EAAAA,EAAAA,KAAKwB,EAAW,CAClCF,IAAKA,KACFG,EACHnB,UAAWkC,IAAWlC,EAAW8C,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvGrE,SAAUsE,GAAoBvE,EAAAA,EAAAA,KAAKiD,EAAU,CAC3ChD,SAAUA,IACPA,MAGTQ,EAAKgC,YAAc,OACnB,QAAe+B,OAAOC,OAAOhE,EAAM,CACjCiE,IAAKf,EACLhD,MAAOwD,EACPQ,SAAUX,EACVtD,KAAMuC,EACNhC,KAAM4C,EACNe,KAAMX,EACNY,OAAQ1B,EACR2B,OAAQ5B,EACR6B,WAAYnB,G", "sources": ["pages/agent/Dashboard.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\nconst AgentDashboard = () => {\n    const { t } = useTranslation();\n    const [agentProfile, setAgentProfile] = useState(null);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchAgentData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch agent profile\n            const { data: profileData, error: profileError } = await supabase\n                .from('agent_profiles')\n                .select('*, maker_profiles(brand_name)')\n                .eq('user_id', user.id)\n                .single();\n\n            if (profileError) {\n                console.error('Error fetching agent profile:', profileError);\n            } else {\n                setAgentProfile(profileData);\n            }\n            setLoading(false);\n        };\n\n        fetchAgentData();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_agent_dashboard')}</div>;\n    }\n\n    if (!agentProfile) {\n        return <div className=\"alert alert-warning\">{t('not_agent')}</div>;\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('agent_dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('brand_name')}</Card.Title>\n                            <h3>{agentProfile.brand_name || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('commission_rate')}</Card.Title>\n                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('kyc_status')}</Card.Title>\n                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('member_management')}</h4>\n                            <p>{t('my_subordinate_members')}</p>\n                            <Button as={Link} to=\"/agent/members\" variant=\"primary\">{t('enter_member_list')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('product_management')}</h4>\n                            <p>{t('products_on_sale')}</p>\n                            <Button as={Link} to=\"/agent/products\" variant=\"success\">{t('browse_agent_products')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentDashboard;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["AgentDashboard", "t", "useTranslation", "agentProfile", "setAgentProfile", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "profileData", "error", "profileError", "from", "select", "eq", "id", "single", "console", "fetchAgentData", "_jsx", "children", "_jsxs", "Container", "fluid", "Row", "className", "Col", "md", "Card", "Body", "Title", "brand_name", "commission_pct", "kyc_status", "<PERSON><PERSON>", "as", "Link", "to", "variant", "React", "_ref", "ref", "bsPrefix", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "classNames", "displayName", "colProps", "spans", "span", "offset", "order", "useCol", "length", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Subtitle", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}