{"version": 3, "file": "static/js/365.7aaa2f05.chunk.js", "mappings": "uOAMA,MAwGA,EAxG2BA,KACvB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GA8CvC,OA5CAG,EAAAA,EAAAA,WAAU,KACcC,WAChB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,UACLC,OAAO,8cAcPC,OAAO,oBAAqB,KAAMN,EAAKO,IACvCC,MAAM,aAAc,CAAEC,WAAW,IAElCN,EACAO,QAAQP,MAAM,yBAA0BA,GAExCZ,EAAUQ,GAEdL,GAAW,IAGfiB,IACD,IAEClB,GACOmB,EAAAA,EAAAA,KAAA,OAAAC,SAAMzB,EAAE,0BAIf0B,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAII,UAAU,OAAMH,SAAEzB,EAAE,iBACxBwB,EAAAA,EAAAA,KAACK,EAAAA,EAAG,CAAAJ,UACAD,EAAAA,EAAAA,KAACM,EAAAA,EAAG,CAAAL,UACAD,EAAAA,EAAAA,KAACO,EAAAA,EAAI,CAAAN,UACDD,EAAAA,EAAAA,KAACO,EAAAA,EAAKC,KAAI,CAAAP,UACNC,EAAAA,EAAAA,MAACO,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAZ,SAAA,EACpCD,EAAAA,EAAAA,KAAA,SAAAC,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACID,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,eACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,mBACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,eACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,YACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,aACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,mBACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,kBACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,aACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,sBAGfwB,EAAAA,EAAAA,KAAA,SAAAC,SACuB,IAAlBvB,EAAOoC,QACJd,EAAAA,EAAAA,KAAA,MAAAC,UACID,EAAAA,EAAAA,KAAA,MAAIe,QAAQ,IAAIX,UAAU,cAAaH,SAAEzB,EAAE,2BAG/CE,EAAOsC,IAAIpB,IAAK,IAAAqB,EAAAC,EAAAC,EAAA,OACZjB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKL,EAAMD,GAAGyB,UAAU,EAAG,GAAG,UAC9BpB,EAAAA,EAAAA,KAAA,MAAAC,UAAmB,QAAdgB,EAAArB,EAAMyB,gBAAQ,IAAAJ,OAAA,EAAdA,EAAgBK,OAAQ,SAC7BtB,EAAAA,EAAAA,KAAA,MAAAC,UAA4B,QAAvBiB,EAAAtB,EAAM2B,yBAAiB,IAAAL,OAAA,EAAvBA,EAAyBM,YAAa,SAC3CxB,EAAAA,EAAAA,KAAA,MAAAC,UAAyB,QAApBkB,EAAAvB,EAAM6B,sBAAc,IAAAN,OAAA,EAApBA,EAAsBO,aAAc,SACzC1B,EAAAA,EAAAA,KAAA,MAAAC,SAAKL,EAAM+B,UACX3B,EAAAA,EAAAA,KAAA,MAAAC,SAAKL,EAAMgC,gBACX5B,EAAAA,EAAAA,KAAA,MAAAC,SAAKL,EAAMiC,eACX7B,EAAAA,EAAAA,KAAA,MAAAC,UAAID,EAAAA,EAAAA,KAAC8B,EAAAA,EAAK,CAACC,GAA4B,aAAxBnC,EAAMoC,cAA+B,UAAY,UAAU/B,SAAEL,EAAMoC,mBAClFhC,EAAAA,EAAAA,KAAA,MAAAC,SAAK,IAAIgC,KAAKrC,EAAMsC,YAAYC,qBAT3BvC,EAAMD,sB,sFCjF3D,MAAMU,EAAmB+B,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRnC,EAEAoC,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,OACjDM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCtD,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,KACFI,EACHtC,UAAWqD,IAAWrD,EAAWuC,KAAsBO,OAG3D7C,EAAIqD,YAAc,MAClB,S,sFCjCA,MAAM5B,EAAqBM,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRR,EAAK,UAAS,KACd4B,GAAO,EAAK,KACZC,EAAI,UACJxD,EACAoC,GAAIC,EAAY,UACbC,GACJL,EACC,MAAMwB,GAASjB,EAAAA,EAAAA,IAAmBL,EAAU,SAC5C,OAAoBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,KACFI,EACHtC,UAAWqD,IAAWrD,EAAWyD,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQ7B,GAAM,MAAMA,SAGzGD,EAAM4B,YAAc,QACpB,S,sFCjBA,MAAMjD,EAAqB2B,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRnC,EAAS,QACTM,EAAO,SACPC,EAAQ,WACRmD,EAAU,MACVlD,EAAK,KACLmD,EAAI,QACJC,EAAO,WACPnD,KACG6B,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,SACjDW,EAAUO,IAAWrD,EAAWuC,EAAmBqB,GAAW,GAAGrB,KAAqBqB,IAAWD,GAAQ,GAAGpB,KAAqBoB,IAAQrD,GAAW,GAAGiC,KAAwC,kBAAZjC,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGgC,aAA8BmB,GAAc,GAAGnB,eAAgC/B,GAAS,GAAG+B,WACxVsB,GAAqBjE,EAAAA,EAAAA,KAAK,QAAS,IACpC0C,EACHtC,UAAW8C,EACXZ,IAAKA,IAEP,GAAIzB,EAAY,CACd,IAAIqD,EAAkB,GAAGvB,eAIzB,MAH0B,kBAAf9B,IACTqD,EAAkB,GAAGA,KAAmBrD,MAEtBb,EAAAA,EAAAA,KAAK,MAAO,CAC9BI,UAAW8D,EACXjE,SAAUgE,GAEd,CACA,OAAOA,IAETxD,EAAMiD,YAAc,QACpB,S,sFCQA,MAAMpD,EAAmB8B,EAAAA,WAEzB,CAACM,EAAOJ,KACN,OAAO,UACLlC,KACG+D,IAEH3B,GAAIC,EAAY,MAAK,SACrBF,EAAQ,MACR6B,IAjDG,SAAe/B,GAKnB,IALoB,GACrBG,EAAE,SACFD,EAAQ,UACRnC,KACGsC,GACJL,EACCE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,OACxC,MAAMM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBoB,EAAQ,GACRlB,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIiB,EACAC,EACA1E,SAHG8C,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCgB,OACAC,SACA1E,SACEyD,GAEJgB,EAAOhB,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDiB,GAAMD,EAAMZ,MAAc,IAATa,EAAgB,GAAG9B,IAAWgB,IAAU,GAAGhB,IAAWgB,KAASc,KACvE,MAATzE,GAAesD,EAAQM,KAAK,QAAQD,KAAS3D,KACnC,MAAV0E,GAAgBpB,EAAQM,KAAK,SAASD,KAASe,OAE9C,CAAC,IACH5B,EACHtC,UAAWqD,IAAWrD,KAAcgE,KAAUlB,IAC7C,CACDV,KACAD,WACA6B,SAEJ,CAWOG,CAAO7B,GACZ,OAAoB1C,EAAAA,EAAAA,KAAKyC,EAAW,IAC/B0B,EACH7B,IAAKA,EACLlC,UAAWqD,IAAWrD,GAAYgE,EAAMtD,QAAUyB,OAGtDjC,EAAIoD,YAAc,MAClB,S,sFC1DA,MAAMc,EAAwBpC,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ClC,EAAS,SACTmC,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGP8B,EAASd,YAAc,WACvB,UCdMe,EAA0BrC,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDlC,EAAS,SACTmC,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,gBACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGP+B,EAAWf,YAAc,aACzB,U,cCZA,MAAMgB,EAA0BtC,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRnC,EAEAoC,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMwB,GAASjB,EAAAA,EAAAA,IAAmBL,EAAU,eACtCoC,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBhB,IAClB,CAACA,IACL,OAAoB7D,EAAAA,EAAAA,KAAK8E,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP1E,UAAuBD,EAAAA,EAAAA,KAAKyC,EAAW,CACrCH,IAAKA,KACFI,EACHtC,UAAWqD,IAAWrD,EAAWyD,SAIvCa,EAAWhB,YAAc,aACzB,UCvBMuB,EAAuB7C,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRnC,EAAS,QACT4D,EACAxB,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMwB,GAASjB,EAAAA,EAAAA,IAAmBL,EAAU,YAC5C,OAAoBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWO,EAAU,GAAGH,KAAUG,IAAYH,EAAQzD,MAC9DsC,MAGPuC,EAAQvB,YAAc,UACtB,UCjBMwB,EAA8B9C,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDlC,EAAS,SACTmC,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,qBACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGPwC,EAAexB,YAAc,iBAC7B,UCdMyB,EAAwB/C,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ClC,EAAS,SACTmC,EACAC,GAAIC,EAAY,OACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGPyC,EAASzB,YAAc,WACvB,U,cCbA,MAAM0B,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BlD,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDlC,EAAS,SACTmC,EACAC,GAAIC,EAAY2C,KACb1C,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,kBACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGP4C,EAAa5B,YAAc,eAC3B,UChBM6B,EAAwBnD,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9ClC,EAAS,SACTmC,EACAC,GAAIC,EAAY,OACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGP6C,EAAS7B,YAAc,WACvB,UCbM8B,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBrD,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/ClC,EAAS,SACTmC,EACAC,GAAIC,EAAY+C,KACb9C,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,eACpBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,EACLlC,UAAWqD,IAAWrD,EAAWmC,MAC9BG,MAGP+C,EAAU/B,YAAc,YACxB,UCPMnD,EAAoB6B,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRnC,EAAS,GACT2B,EAAE,KACF6B,EAAI,OACJ8B,EAAM,KACNC,GAAO,EAAK,SACZ1F,EAEAuC,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMwB,GAASjB,EAAAA,EAAAA,IAAmBL,EAAU,QAC5C,OAAoBvC,EAAAA,EAAAA,KAAKyC,EAAW,CAClCH,IAAKA,KACFI,EACHtC,UAAWqD,IAAWrD,EAAWyD,EAAQ9B,GAAM,MAAMA,IAAM6B,GAAQ,QAAQA,IAAQ8B,GAAU,UAAUA,KACvGzF,SAAU0F,GAAoB3F,EAAAA,EAAAA,KAAKwE,EAAU,CAC3CvE,SAAUA,IACPA,MAGTM,EAAKmD,YAAc,OACnB,QAAekC,OAAOC,OAAOtF,EAAM,CACjCuF,IAAKb,EACLc,MAAON,EACPO,SAAUV,EACV9E,KAAMgE,EACNyB,KAAMd,EACNe,KAAMX,EACNY,OAAQzB,EACR0B,OAAQ3B,EACR4B,WAAYnB,G", "sources": ["pages/maker/MakerOrderListPage.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MakerOrderListPage = () => {\n    const { t } = useTranslation();\n    const [orders, setOrders] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchOrders = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch orders associated with products from this maker\n            const { data, error } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    products ( name, maker_id ),\n                    customer_profiles ( real_name ),\n                    agent_profiles ( brand_name )\n                `)\n                .filter('products.maker_id', 'eq', user.id) // Filter by maker_id from products table\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching orders:', error);\n            } else {\n                setOrders(data);\n            }\n            setLoading(false);\n        };\n\n        fetchOrders();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_orders_text')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('all_orders')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('agent')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('storage_cost')}</th>\n                                        <th>{t('pledge_cost')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {orders.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_orders_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>{order.id.substring(0, 8)}...</td>\n                                                <td>{order.products?.name || 'N/A'}</td>\n                                                <td>{order.customer_profiles?.real_name || 'N/A'}</td>\n                                                <td>{order.agent_profiles?.brand_name || 'N/A'}</td>\n                                                <td>{order.shares}</td>\n                                                <td>{order.storage_cost}</td>\n                                                <td>{order.pledge_cost}</td>\n                                                <td><Badge bg={order.review_status === 'approved' ? 'success' : 'warning'}>{order.review_status}</Badge></td>\n                                                <td>{new Date(order.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MakerOrderListPage;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["MakerOrderListPage", "t", "useTranslation", "orders", "setOrders", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "filter", "id", "order", "ascending", "console", "fetchOrders", "_jsx", "children", "_jsxs", "Container", "className", "Row", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "_order$products", "_order$customer_profi", "_order$agent_profiles", "substring", "products", "name", "customer_profiles", "real_name", "agent_profiles", "brand_name", "shares", "storage_cost", "pledge_cost", "Badge", "bg", "review_status", "Date", "created_at", "toLocaleString", "React", "_ref", "ref", "bsPrefix", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "classNames", "displayName", "pill", "text", "prefix", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}