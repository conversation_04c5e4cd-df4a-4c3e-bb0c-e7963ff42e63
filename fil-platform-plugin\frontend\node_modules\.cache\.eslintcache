[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\components\\RouterDebug.js": "20"}, {"size": 499, "mtime": *************, "results": "21", "hashOfConfig": "22"}, {"size": 5152, "mtime": *************, "results": "23", "hashOfConfig": "22"}, {"size": 22975, "mtime": *************, "results": "24", "hashOfConfig": "22"}, {"size": 1212, "mtime": 1751873051207, "results": "25", "hashOfConfig": "22"}, {"size": 2452, "mtime": 1751946181573, "results": "26", "hashOfConfig": "22"}, {"size": 4612, "mtime": 1751946354832, "results": "27", "hashOfConfig": "22"}, {"size": 3841, "mtime": 1751946553380, "results": "28", "hashOfConfig": "22"}, {"size": 4610, "mtime": 1751946228349, "results": "29", "hashOfConfig": "22"}, {"size": 5064, "mtime": 1751946317771, "results": "30", "hashOfConfig": "22"}, {"size": 8598, "mtime": 1751939997191, "results": "31", "hashOfConfig": "22"}, {"size": 4230, "mtime": 1751940026705, "results": "32", "hashOfConfig": "22"}, {"size": 1735, "mtime": 1751940008151, "results": "33", "hashOfConfig": "22"}, {"size": 4711, "mtime": 1751946191614, "results": "34", "hashOfConfig": "22"}, {"size": 4495, "mtime": 1751940037703, "results": "35", "hashOfConfig": "22"}, {"size": 3655, "mtime": 1751948557098, "results": "36", "hashOfConfig": "22"}, {"size": 4863, "mtime": 1751950041198, "results": "37", "hashOfConfig": "22"}, {"size": 3929, "mtime": 1751946465633, "results": "38", "hashOfConfig": "22"}, {"size": 4027, "mtime": 1751945104895, "results": "39", "hashOfConfig": "22"}, {"size": 3679, "mtime": 1751944188070, "results": "40", "hashOfConfig": "22"}, {"size": 1051, "mtime": 1751949948194, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ji7irk", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", ["102"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["103", "104", "105", "106", "107", "108"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["109"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["110"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\components\\RouterDebug.js", [], [], {"ruleId": "111", "severity": 2, "message": "112", "line": 11, "column": 3, "nodeType": "113", "messageId": "114", "endLine": 11, "endColumn": 26}, {"ruleId": "115", "severity": 1, "message": "116", "line": 138, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 138, "endColumn": 18}, {"ruleId": "115", "severity": 1, "message": "119", "line": 140, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 140, "endColumn": 23}, {"ruleId": "115", "severity": 1, "message": "116", "line": 311, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 311, "endColumn": 18}, {"ruleId": "115", "severity": 1, "message": "119", "line": 313, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 313, "endColumn": 23}, {"ruleId": "115", "severity": 1, "message": "116", "line": 484, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 484, "endColumn": 18}, {"ruleId": "115", "severity": 1, "message": "119", "line": 486, "column": 7, "nodeType": "117", "messageId": "118", "endLine": 486, "endColumn": 23}, {"ruleId": "120", "severity": 1, "message": "121", "line": 49, "column": 8, "nodeType": "122", "endLine": 49, "endColumn": 10, "suggestions": "123"}, {"ruleId": "124", "severity": 1, "message": "125", "line": 3, "column": 44, "nodeType": "113", "messageId": "126", "endLine": 3, "endColumn": 50}, "no-undef", "'__webpack_public_path__' is not defined.", "Identifier", "undef", "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["127"], "no-unused-vars", "'Button' is defined but never used.", "unusedVar", {"desc": "128", "fix": "129"}, "Update the dependencies array to be: [t]", {"range": "130", "text": "131"}, [1977, 1979], "[t]"]