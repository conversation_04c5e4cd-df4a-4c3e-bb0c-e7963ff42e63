<?php
/**
 * Plugin Name: FIL Platform
 * Description: A custom plugin to display a Filecoin platform interface, built with React.
 * Version: 1.0
 * Author: Gemini
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Function to enqueue scripts and styles for the React app
function fil_platform_enqueue_scripts() {
    $post = get_post();
    $has_shortcode = false;
    if ($post) {
        $has_shortcode = has_shortcode($post->post_content, 'fil_platform_app');
    }

    // Only load on the page with our shortcode
    if (is_page() && $has_shortcode) {
        $asset_path = plugin_dir_path(__FILE__) . 'frontend/build/asset-manifest.json';

        if (file_exists($asset_path)) {
            $asset_manifest_content = file_get_contents($asset_path);
            $asset_manifest = json_decode($asset_manifest_content, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                $main_js = isset($asset_manifest['files']['main.js']) ? $asset_manifest['files']['main.js'] : '';
                $main_css = isset($asset_manifest['files']['main.css']) ? $asset_manifest['files']['main.css'] : '';

                // 构建正确的插件URL路径
                $plugin_url = plugin_dir_url(__FILE__) . 'frontend/build/';

                if ($main_js) {
                    // 移除路径开头的 './' 并构建完整URL
                    $js_path = ltrim($main_js, './');
                    $js_url = $plugin_url . $js_path;
                    wp_enqueue_script(
                        'fil-platform-react-app',
                        $js_url,
                        [],
                        '1.0',
                        true
                    );
                }

                if ($main_css) {
                    // 移除路径开头的 './' 并构建完整URL
                    $css_path = ltrim($main_css, './');
                    $css_url = $plugin_url . $css_path;
                    wp_enqueue_style(
                        'fil-platform-react-app-css',
                        $css_url,
                        [],
                        '1.0'
                    );
                }

                // Debug output
                error_log('FIL Platform Final Solution:');
                error_log('Plugin URL: ' . $plugin_url);
                error_log('JS path from manifest: ' . $main_js);
                error_log('CSS path from manifest: ' . $main_css);
                if (isset($js_url)) error_log('Final JS URL: ' . $js_url);
                if (isset($css_url)) error_log('Final CSS URL: ' . $css_url);

                // Pass data to the React app, including the base path for React Router
                wp_localize_script('fil-platform-react-app', 'wpData', [
                    'apiUrl' => home_url('/wp-json/fil-platform/v1/'),
                    'nonce' => wp_create_nonce('wp_rest'),
                    'basePath' => parse_url(get_permalink($post->ID), PHP_URL_PATH),
                ]);
            } else {
                error_log('FIL Platform: Error parsing asset-manifest.json - ' . json_last_error_msg());
            }
        } else {
            error_log('FIL Platform: asset-manifest.json not found at ' . $asset_path);
        }
    }
}
add_action('wp_enqueue_scripts', 'fil_platform_enqueue_scripts');

// Shortcode to display the React app
function fil_platform_shortcode() {
    return '<div id="fil-platform-root"></div>';
}
add_shortcode('fil_platform_app', 'fil_platform_shortcode');

// Include the API routes
require_once plugin_dir_path(__FILE__) . 'includes/api-routes.php';