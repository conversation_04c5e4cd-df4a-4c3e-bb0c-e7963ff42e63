{"version": 3, "file": "static/js/738.d9ae6fa6.chunk.js", "mappings": "8NAMA,MA+LA,EA/LgBA,KAAO,IAADC,EAAAC,EAClB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,OACtCK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,OACpCO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAS,YAC1CS,EAASC,IAAcV,EAAAA,EAAAA,WAAS,IAChCW,EAAYC,IAAiBZ,EAAAA,EAAAA,WAAS,IACtCa,EAASC,IAAcd,EAAAA,EAAAA,UAAS,CAAEe,KAAM,GAAIC,KAAM,MAEzDC,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEf,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAED,YADAZ,GAAW,GAIf,MAAM,KAAEW,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,qBACLC,OAAO,kEACPC,GAAG,UAAWN,EAAKO,IACnBC,SAEDL,GAAwB,aAAfA,EAAMM,MACfC,QAAQP,MAAM,6BAA8BA,GAC5CX,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,gCAC9ByB,IACPtB,EAAYsB,EAAKY,WAAa,IAC9B/B,EAAYmB,EAAKa,WAAa,IAC9B9B,EAAciB,EAAKc,cAAgB,MACnC7B,EAAae,EAAKe,aAAe,MACjC5B,EAAgBa,EAAKgB,eAAiB,YAE1C3B,GAAW,IAGf4B,IACD,IAEH,MAAMC,EAAmBA,CAACC,EAAGC,KACrBD,EAAEE,OAAOC,OAASH,EAAEE,OAAOC,MAAM,IACjCF,EAASD,EAAEE,OAAOC,MAAM,KAgEhC,OAAIlC,GACOmC,EAAAA,EAAAA,KAAA,OAAAC,SAAMjD,EAAE,yBAIfkD,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAII,UAAU,OAAMH,SAAEjD,EAAE,uBACxBgD,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAAAJ,UACDC,EAAAA,EAAAA,MAACG,EAAAA,EAAKC,KAAI,CAAAL,SAAA,CACLhC,EAAQG,OAAQ4B,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CAACC,QAASvC,EAAQE,KAAK8B,SAAEhC,EAAQG,OAEtC,aAAjBT,IACGqC,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CAACC,QAAQ,UAASP,SAAEjD,EAAE,kBAEd,YAAjBW,IACGqC,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CAACC,QAAQ,OAAMP,SAAEjD,EAAE,wBAEX,aAAjBW,IACGqC,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CAACC,QAAQ,SAAQP,SAAEjD,EAAE,mBAG/BkD,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACC,SAjFDpC,UACjBsB,EAAEe,iBACF3C,GAAc,GACdE,EAAW,CAAEC,KAAM,GAAIC,KAAM,KAE7B,MAAMG,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEf,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAGD,OAFAR,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,6BACrCgB,GAAc,GAIlB,IACI,IAAI4C,EAAgBrD,EAChBsD,EAAepD,EAGnB,GAAIF,aAAsBuD,KAAM,CAC5B,MAAQrC,KAAMsC,EAAiBlC,MAAOmC,SAA2BzC,EAAS0C,QACrEnC,KAAK,iBACLoC,OAAO,GAAGxC,EAAKO,YAAYkC,KAAKC,QAAS7D,EAAY,CAAE8D,aAAc,OAAQC,QAAQ,IAC1F,GAAIN,EAAkB,MAAMA,EAC5BJ,EAAgBG,EAAgBQ,IACpC,CAEA,GAAI9D,aAAqBqD,KAAM,CAC3B,MAAQrC,KAAM+C,EAAgB3C,MAAO4C,SAA0BlD,EAAS0C,QACnEnC,KAAK,iBACLoC,OAAO,GAAGxC,EAAKO,WAAWkC,KAAKC,QAAS3D,EAAW,CAAE4D,aAAc,OAAQC,QAAQ,IACxF,GAAIG,EAAiB,MAAMA,EAC3BZ,EAAeW,EAAeD,IAClC,CAEA,MAAM,MAAE1C,SAAgBN,EACnBO,KAAK,qBACLwC,OAAO,CACJI,QAAShD,EAAKO,GACdI,UAAWnC,EACXoC,UAAWjC,EACXkC,aAAcqB,EACdpB,YAAaqB,EACbpB,cAAe,WAChB,CAAEkC,WAAY,YAErB,GAAI9C,EAAO,MAAMA,EAEjBX,EAAW,CAAEC,KAAM,UAAWC,KAAMpB,EAAE,wBACtCY,EAAgB,UAEpB,CAAE,MAAOiB,GACLO,QAAQP,MAAM,wBAAyBA,GACvCX,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,wBAA0B,KAAO6B,EAAMZ,SAChF,CAEAD,GAAc,IAwB2BiC,SAAA,EACzBC,EAAAA,EAAAA,MAACO,EAAAA,EAAKmB,MAAK,CAACxB,UAAU,OAAMH,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAKoB,MAAK,CAAA5B,SAAEjD,EAAE,gBACfgD,EAAAA,EAAAA,KAACS,EAAAA,EAAKqB,QAAO,CACT3D,KAAK,OACL4D,MAAO7E,EACP8E,SAAWpC,GAAMzC,EAAYyC,EAAEE,OAAOiC,OACtCE,UAAQ,EACRC,SAA2B,YAAjBvE,GAA+C,aAAjBA,QAGhDuC,EAAAA,EAAAA,MAACO,EAAAA,EAAKmB,MAAK,CAACxB,UAAU,OAAMH,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAKoB,MAAK,CAAA5B,SAAEjD,EAAE,gBACfgD,EAAAA,EAAAA,KAACS,EAAAA,EAAKqB,QAAO,CACT3D,KAAK,OACL4D,MAAO1E,EACP2E,SAAWpC,GAAMtC,EAAYsC,EAAEE,OAAOiC,OACtCE,UAAQ,EACRC,SAA2B,YAAjBvE,GAA+C,aAAjBA,QAGhDuC,EAAAA,EAAAA,MAACO,EAAAA,EAAKmB,MAAK,CAACxB,UAAU,OAAMH,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAKoB,MAAK,CAAA5B,SAAEjD,EAAE,eACfgD,EAAAA,EAAAA,KAACS,EAAAA,EAAKqB,QAAO,CACT3D,KAAK,OACL6D,SAAWpC,GAAMD,EAAiBC,EAAGpC,GACrC2E,OAAO,UACPD,SAA2B,YAAjBvE,GAA+C,aAAjBA,IAE3CJ,GAAoC,kBAAfA,IAClByC,EAAAA,EAAAA,KAAA,OAAKoC,IAAkB,QAAftF,GAAE0B,EAAAA,EAAAA,YAAa,IAAA1B,OAAA,EAAbA,EAAemE,QAAQnC,KAAK,iBAAiBuD,aAAa9E,GAAYkB,KAAK6D,UAAWC,IAAI,WAAWnC,UAAU,qBAAqBoC,MAAO,CAAEC,SAAU,eAGzKvC,EAAAA,EAAAA,MAACO,EAAAA,EAAKmB,MAAK,CAACxB,UAAU,OAAMH,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAKoB,MAAK,CAAA5B,SAAEjD,EAAE,cACfgD,EAAAA,EAAAA,KAACS,EAAAA,EAAKqB,QAAO,CACT3D,KAAK,OACL6D,SAAWpC,GAAMD,EAAiBC,EAAGlC,GACrCyE,OAAO,UACPD,SAA2B,YAAjBvE,GAA+C,aAAjBA,IAE3CF,GAAkC,kBAAdA,IACjBuC,EAAAA,EAAAA,KAAA,OAAKoC,IAAkB,QAAfrF,GAAEyB,EAAAA,EAAAA,YAAa,IAAAzB,OAAA,EAAbA,EAAekE,QAAQnC,KAAK,iBAAiBuD,aAAa5E,GAAWgB,KAAK6D,UAAWC,IAAI,UAAUnC,UAAU,qBAAqBoC,MAAO,CAAEC,SAAU,eAIvKzC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAM,CACHlC,QAAQ,UACRrC,KAAK,SACL+D,SAAUnE,GAA+B,YAAjBJ,GAA+C,aAAjBA,EAA4BsC,SAEpEjD,EAAbe,EAAe,aAAkB,8B", "sources": ["pages/customer/KycPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Form, Button, Card, Alert } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst KycPage = () => {\n    const { t } = useTranslation();\n    const [realName, setRealName] = useState('');\n    const [idNumber, setIdNumber] = useState('');\n    const [idImgFront, setIdImgFront] = useState(null);\n    const [idImgBack, setIdImgBack] = useState(null);\n    const [verifyStatus, setVerifyStatus] = useState('pending'); // or 'approved', 'rejected'\n    const [loading, setLoading] = useState(true);\n    const [submitting, setSubmitting] = useState(false);\n    const [message, setMessage] = useState({ type: '', text: '' });\n\n    useEffect(() => {\n        const fetchKycStatus = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .select('real_name, id_number, id_img_front, id_img_back, verify_status')\n                .eq('user_id', user.id)\n                .single();\n\n            if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found\n                console.error('Error fetching KYC status:', error);\n                setMessage({ type: 'danger', text: t('failed_to_load_kyc_status') });\n            } else if (data) {\n                setRealName(data.real_name || '');\n                setIdNumber(data.id_number || '');\n                setIdImgFront(data.id_img_front || null);\n                setIdImgBack(data.id_img_back || null);\n                setVerifyStatus(data.verify_status || 'pending');\n            }\n            setLoading(false);\n        };\n\n        fetchKycStatus();\n    }, []);\n\n    const handleFileChange = (e, setImage) => {\n        if (e.target.files && e.target.files[0]) {\n            setImage(e.target.files[0]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setSubmitting(true);\n        setMessage({ type: '', text: '' });\n\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            setMessage({ type: 'danger', text: t('user_not_logged_in') });\n            setSubmitting(false);\n            return;\n        }\n\n        try {\n            let frontImageUrl = idImgFront;\n            let backImageUrl = idImgBack;\n\n            // Upload images if they are new File objects\n            if (idImgFront instanceof File) {\n                const { data: uploadDataFront, error: uploadErrorFront } = await supabase.storage\n                    .from('kyc-documents')\n                    .upload(`${user.id}/front_${Date.now()}`, idImgFront, { cacheControl: '3600', upsert: false });\n                if (uploadErrorFront) throw uploadErrorFront;\n                frontImageUrl = uploadDataFront.path;\n            }\n\n            if (idImgBack instanceof File) {\n                const { data: uploadDataBack, error: uploadErrorBack } = await supabase.storage\n                    .from('kyc-documents')\n                    .upload(`${user.id}/back_${Date.now()}`, idImgBack, { cacheControl: '3600', upsert: false });\n                if (uploadErrorBack) throw uploadErrorBack;\n                backImageUrl = uploadDataBack.path;\n            }\n\n            const { error } = await supabase\n                .from('customer_profiles')\n                .upsert({\n                    user_id: user.id,\n                    real_name: realName,\n                    id_number: idNumber,\n                    id_img_front: frontImageUrl,\n                    id_img_back: backImageUrl,\n                    verify_status: 'pending' // Always set to pending on submission\n                }, { onConflict: 'user_id' });\n\n            if (error) throw error;\n\n            setMessage({ type: 'success', text: t('kyc_submit_success') });\n            setVerifyStatus('pending');\n\n        } catch (error) {\n            console.error('KYC submission error:', error);\n            setMessage({ type: 'danger', text: t('failed_to_submit_kyc') + ': ' + error.message });\n        }\n\n        setSubmitting(false);\n    };\n\n    if (loading) {\n        return <div>{t('loading_kyc_status')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('kyc_verification')}</h2>\n            <Card>\n                <Card.Body>\n                    {message.text && <Alert variant={message.type}>{message.text}</Alert>}\n                    \n                    {verifyStatus === 'approved' && (\n                        <Alert variant=\"success\">{t('kyc_approved')}</Alert>\n                    )}\n                    {verifyStatus === 'pending' && (\n                        <Alert variant=\"info\">{t('kyc_pending_review')}</Alert>\n                    )}\n                    {verifyStatus === 'rejected' && (\n                        <Alert variant=\"danger\">{t('kyc_rejected')}</Alert>\n                    )}\n\n                    <Form onSubmit={handleSubmit}>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('real_name')}</Form.Label>\n                            <Form.Control \n                                type=\"text\" \n                                value={realName} \n                                onChange={(e) => setRealName(e.target.value)} \n                                required \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_number')}</Form.Label>\n                            <Form.Control \n                                type=\"text\" \n                                value={idNumber} \n                                onChange={(e) => setIdNumber(e.target.value)} \n                                required \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_front')}</Form.Label>\n                            <Form.Control \n                                type=\"file\" \n                                onChange={(e) => handleFileChange(e, setIdImgFront)} \n                                accept=\"image/*\" \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                            {idImgFront && typeof idImgFront === 'string' && (\n                                <img src={getSupabase()?.storage.from('kyc-documents').getPublicUrl(idImgFront).data.publicUrl} alt=\"ID Front\" className=\"img-thumbnail mt-2\" style={{ maxWidth: '200px' }} />\n                            )}\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_back')}</Form.Label>\n                            <Form.Control \n                                type=\"file\" \n                                onChange={(e) => handleFileChange(e, setIdImgBack)} \n                                accept=\"image/*\" \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                            {idImgBack && typeof idImgBack === 'string' && (\n                                <img src={getSupabase()?.storage.from('kyc-documents').getPublicUrl(idImgBack).data.publicUrl} alt=\"ID Back\" className=\"img-thumbnail mt-2\" style={{ maxWidth: '200px' }} />\n                            )}\n                        </Form.Group>\n                        \n                        <Button \n                            variant=\"primary\" \n                            type=\"submit\" \n                            disabled={submitting || verifyStatus === 'pending' || verifyStatus === 'approved'}\n                        >\n                            {submitting ? t('submitting') : t('submit_review')}\n                        </Button>\n                    </Form>\n                </Card.Body>\n            </Card>\n        </Container>\n    );\n};\n\nexport default KycPage;\n"], "names": ["KycPage", "_get<PERSON><PERSON><PERSON>se", "_getSupabase2", "t", "useTranslation", "realName", "setRealName", "useState", "idNumber", "setIdNumber", "idImgFront", "setIdImgFront", "idImgBack", "setIdImgBack", "verifyStatus", "setVerifyStatus", "loading", "setLoading", "submitting", "setSubmitting", "message", "setMessage", "type", "text", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "single", "code", "console", "real_name", "id_number", "id_img_front", "id_img_back", "verify_status", "fetchKycStatus", "handleFileChange", "e", "setImage", "target", "files", "_jsx", "children", "_jsxs", "Container", "className", "Card", "Body", "<PERSON><PERSON>", "variant", "Form", "onSubmit", "preventDefault", "frontImageUrl", "backImageUrl", "File", "uploadDataFront", "uploadErrorFront", "storage", "upload", "Date", "now", "cacheControl", "upsert", "path", "uploadDataBack", "uploadErrorBack", "user_id", "onConflict", "Group", "Label", "Control", "value", "onChange", "required", "disabled", "accept", "src", "getPublicUrl", "publicUrl", "alt", "style", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "sourceRoot": ""}