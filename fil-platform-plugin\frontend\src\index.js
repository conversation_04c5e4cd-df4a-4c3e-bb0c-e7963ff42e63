
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>hRouter  } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import './i18n'; // Initialize i18n
import App from './App';
import './index.css';

const root = ReactDOM.createRoot(document.getElementById('fil-platform-root'));

// Get the base path from wpData, which is localized from PHP
const basename = window.wpData && window.wpData.basePath ? window.wpData.basePath : '/';

root.render(
  <React.StrictMode>
    <HashRouter>
      <App />
    </HashRouter>
  </React.StrictMode>
);
