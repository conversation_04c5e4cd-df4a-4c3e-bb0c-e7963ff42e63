
import React from 'react';
import ReactDOM from 'react-dom/client';
import 'bootstrap/dist/css/bootstrap.min.css';
import './i18n'; // Initialize i18n
import App from './App';
import './index.css';

// Set the public path for dynamic imports
if (window.wpData && window.wpData.pluginUrl) {
  __webpack_public_path__ = window.wpData.pluginUrl;
}

const root = ReactDOM.createRoot(document.getElementById('fil-platform-root'));

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
