{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Button,Badge}from'react-bootstrap';import{useTranslation}from'react-i18next';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProductListPage=()=>{const{t}=useTranslation();const[products,setProducts]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchProducts=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data,error}=await supabase.from('products').select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    effective_delay_days,\n                    min_purchase,\n                    is_disabled,\n                    maker_profiles ( brand_name: user_id, maker_brand: domain ) \n                `).eq('is_disabled',false).order('created_at',{ascending:false});if(error){console.error('Error fetching products:',error);}else{setProducts(data);}setLoading(false);};fetchProducts();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_products')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('power_products')}),/*#__PURE__*/_jsx(Row,{children:products.map(product=>{var _product$maker_profil;return/*#__PURE__*/_jsx(Col,{md:4,className:\"mb-4\",children:/*#__PURE__*/_jsx(Card,{className:product.is_disabled?'bg-light':'',children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(Card.Title,{className:\"d-flex justify-content-between\",children:[product.name,product.category==='spot'?/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:t('spot_category')}):/*#__PURE__*/_jsx(Badge,{bg:\"primary\",children:t('futures_category')})]}),/*#__PURE__*/_jsxs(Card.Subtitle,{className:\"mb-2 text-muted\",children:[t('provided_by'),\" \",((_product$maker_profil=product.maker_profiles)===null||_product$maker_profil===void 0?void 0:_product$maker_profil.maker_brand)||t('official')]}),/*#__PURE__*/_jsxs(Card.Text,{children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('price_label'),\":\"]}),\" \",product.price,\" \",t('usdt_per_share'),\" \",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(\"strong\",{children:[t('total_shares_label'),\":\"]}),\" \",product.total_shares,\" \",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(\"strong\",{children:[t('remaining_shares_label'),\":\"]}),\" \",product.total_shares-product.sold_shares,\" \",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(\"strong\",{children:[t('min_purchase_label'),\":\"]}),\" \",product.min_purchase,\" \",t('shares_unit'),\" \",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(\"strong\",{children:[t('waiting_period_label'),\":\"]}),\" \",product.effective_delay_days,\" \",t('days_unit')]}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",disabled:product.is_disabled||product.total_shares-product.sold_shares===0,children:product.total_shares-product.sold_shares===0?t('sold_out'):t('buy_now')})]})})},product.id);})})]});};export default ProductListPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "useTranslation", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "ProductListPage", "t", "products", "setProducts", "loading", "setLoading", "fetchProducts", "supabase", "data", "error", "from", "select", "eq", "order", "ascending", "console", "children", "className", "map", "product", "_product$maker_profil", "md", "is_disabled", "Body", "Title", "name", "category", "bg", "Subtitle", "maker_profiles", "maker_brand", "Text", "price", "total_shares", "sold_shares", "min_purchase", "effective_delay_days", "variant", "disabled", "id"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/ProductListPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../../supabaseClient';\n\nconst ProductListPage = () => {\n    const { t } = useTranslation();\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchProducts = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data, error } = await supabase\n                .from('products')\n                .select(`\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    effective_delay_days,\n                    min_purchase,\n                    is_disabled,\n                    maker_profiles ( brand_name: user_id, maker_brand: domain ) \n                `)\n                .eq('is_disabled', false)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching products:', error);\n            } else {\n                setProducts(data);\n            }\n            setLoading(false);\n        };\n\n        fetchProducts();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_products')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('power_products')}</h2>\n            <Row>\n                {products.map(product => (\n                    <Col md={4} key={product.id} className=\"mb-4\">\n                        <Card className={product.is_disabled ? 'bg-light' : ''}>\n                            <Card.Body>\n                                <Card.Title className=\"d-flex justify-content-between\">\n                                    {product.name}\n                                    {product.category === 'spot' ?\n                                        <Badge bg=\"success\">{t('spot_category')}</Badge> :\n                                        <Badge bg=\"primary\">{t('futures_category')}</Badge>}\n                                </Card.Title>\n                                <Card.Subtitle className=\"mb-2 text-muted\">{t('provided_by')} {product.maker_profiles?.maker_brand || t('official')}</Card.Subtitle>\n                                <Card.Text>\n                                    <strong>{t('price_label')}:</strong> {product.price} {t('usdt_per_share')} <br />\n                                    <strong>{t('total_shares_label')}:</strong> {product.total_shares} <br />\n                                    <strong>{t('remaining_shares_label')}:</strong> {product.total_shares - product.sold_shares} <br />\n                                    <strong>{t('min_purchase_label')}:</strong> {product.min_purchase} {t('shares_unit')} <br />\n                                    <strong>{t('waiting_period_label')}:</strong> {product.effective_delay_days} {t('days_unit')}\n                                </Card.Text>\n                                <Button variant=\"primary\" disabled={product.is_disabled || (product.total_shares - product.sold_shares === 0)}>\n                                    {(product.total_shares - product.sold_shares === 0) ? t('sold_out') : t('buy_now')}\n                                </Button>\n                            </Card.Body>\n                        </Card>\n                    </Col>\n                ))}\n            </Row>\n        </Container>\n    );\n};\n\nexport default ProductListPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,KAAQ,iBAAiB,CAC1E,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,CAAE,CAAC,CAAGP,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACY,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAEC,KAAM,CAAC,CAAG,KAAM,CAAAF,QAAQ,CACjCG,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,EAAE,CAAC,aAAa,CAAE,KAAK,CAAC,CACxBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIL,KAAK,CAAE,CACPM,OAAO,CAACN,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CACpD,CAAC,IAAM,CACHN,WAAW,CAACK,IAAI,CAAC,CACrB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,aAAa,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAmB,QAAA,CAAMf,CAAC,CAAC,kBAAkB,CAAC,CAAM,CAAC,CAC7C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA4B,QAAA,eACNnB,IAAA,OAAIoB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEf,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC/CJ,IAAA,CAACR,GAAG,EAAA2B,QAAA,CACCd,QAAQ,CAACgB,GAAG,CAACC,OAAO,OAAAC,qBAAA,oBACjBvB,IAAA,CAACP,GAAG,EAAC+B,EAAE,CAAE,CAAE,CAAkBJ,SAAS,CAAC,MAAM,CAAAD,QAAA,cACzCnB,IAAA,CAACN,IAAI,EAAC0B,SAAS,CAAEE,OAAO,CAACG,WAAW,CAAG,UAAU,CAAG,EAAG,CAAAN,QAAA,cACnDjB,KAAA,CAACR,IAAI,CAACgC,IAAI,EAAAP,QAAA,eACNjB,KAAA,CAACR,IAAI,CAACiC,KAAK,EAACP,SAAS,CAAC,gCAAgC,CAAAD,QAAA,EACjDG,OAAO,CAACM,IAAI,CACZN,OAAO,CAACO,QAAQ,GAAK,MAAM,cACxB7B,IAAA,CAACJ,KAAK,EAACkC,EAAE,CAAC,SAAS,CAAAX,QAAA,CAAEf,CAAC,CAAC,eAAe,CAAC,CAAQ,CAAC,cAChDJ,IAAA,CAACJ,KAAK,EAACkC,EAAE,CAAC,SAAS,CAAAX,QAAA,CAAEf,CAAC,CAAC,kBAAkB,CAAC,CAAQ,CAAC,EAC/C,CAAC,cACbF,KAAA,CAACR,IAAI,CAACqC,QAAQ,EAACX,SAAS,CAAC,iBAAiB,CAAAD,QAAA,EAAEf,CAAC,CAAC,aAAa,CAAC,CAAC,GAAC,CAAC,EAAAmB,qBAAA,CAAAD,OAAO,CAACU,cAAc,UAAAT,qBAAA,iBAAtBA,qBAAA,CAAwBU,WAAW,GAAI7B,CAAC,CAAC,UAAU,CAAC,EAAgB,CAAC,cACpIF,KAAA,CAACR,IAAI,CAACwC,IAAI,EAAAf,QAAA,eACNjB,KAAA,WAAAiB,QAAA,EAASf,CAAC,CAAC,aAAa,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACkB,OAAO,CAACa,KAAK,CAAC,GAAC,CAAC/B,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,cAAAJ,IAAA,QAAK,CAAC,cACjFE,KAAA,WAAAiB,QAAA,EAASf,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACkB,OAAO,CAACc,YAAY,CAAC,GAAC,cAAApC,IAAA,QAAK,CAAC,cACzEE,KAAA,WAAAiB,QAAA,EAASf,CAAC,CAAC,wBAAwB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACkB,OAAO,CAACc,YAAY,CAAGd,OAAO,CAACe,WAAW,CAAC,GAAC,cAAArC,IAAA,QAAK,CAAC,cACnGE,KAAA,WAAAiB,QAAA,EAASf,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACkB,OAAO,CAACgB,YAAY,CAAC,GAAC,CAAClC,CAAC,CAAC,aAAa,CAAC,CAAC,GAAC,cAAAJ,IAAA,QAAK,CAAC,cAC5FE,KAAA,WAAAiB,QAAA,EAASf,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACkB,OAAO,CAACiB,oBAAoB,CAAC,GAAC,CAACnC,CAAC,CAAC,WAAW,CAAC,EACrF,CAAC,cACZJ,IAAA,CAACL,MAAM,EAAC6C,OAAO,CAAC,SAAS,CAACC,QAAQ,CAAEnB,OAAO,CAACG,WAAW,EAAKH,OAAO,CAACc,YAAY,CAAGd,OAAO,CAACe,WAAW,GAAK,CAAG,CAAAlB,QAAA,CACxGG,OAAO,CAACc,YAAY,CAAGd,OAAO,CAACe,WAAW,GAAK,CAAC,CAAIjC,CAAC,CAAC,UAAU,CAAC,CAAGA,CAAC,CAAC,SAAS,CAAC,CAC9E,CAAC,EACF,CAAC,CACV,CAAC,EArBMkB,OAAO,CAACoB,EAsBpB,CAAC,EACT,CAAC,CACD,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAvC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}