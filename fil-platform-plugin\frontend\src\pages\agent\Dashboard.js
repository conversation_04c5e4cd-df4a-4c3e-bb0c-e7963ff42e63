import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';

const AgentDashboard = () => {
    const { t } = useTranslation();
    const [agentProfile, setAgentProfile] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchAgentData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch agent profile
            const { data: profileData, error: profileError } = await supabase
                .from('agent_profiles')
                .select('*, maker_profiles(brand_name)')
                .eq('user_id', user.id)
                .single();

            if (profileError) {
                console.error('Error fetching agent profile:', profileError);
            } else {
                setAgentProfile(profileData);
            }
            setLoading(false);
        };

        fetchAgentData();
    }, []);

    if (loading) {
        return <div>{t('loading_agent_dashboard')}</div>;
    }

    if (!agentProfile) {
        return <div className="alert alert-warning">您不是代理商或代理商信息未找到。</div>;
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('dashboard')} - 代理商后台</h2>
                </Col>
            </Row>

            <Row>
                <Col md={4}>
                    <Card className="bg-primary text-white mb-3">
                        <Card.Body>
                            <Card.Title>品牌名称</Card.Title>
                            <h3>{agentProfile.brand_name || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-success text-white mb-3">
                        <Card.Body>
                            <Card.Title>佣金比例</Card.Title>
                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="bg-info text-white mb-3">
                        <Card.Body>
                            <Card.Title>KYC 状态</Card.Title>
                            <h3>{agentProfile.kyc_status || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>成员管理</h4>
                            <p>查看和管理您的下级成员</p>
                            <Button as={Link} to="/agent/members" variant="primary">进入成员列表</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>产品管理</h4>
                            <p>查看在售产品和现货产品</p>
                            <Button as={Link} to="/agent/products" variant="success">浏览产品</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AgentDashboard;