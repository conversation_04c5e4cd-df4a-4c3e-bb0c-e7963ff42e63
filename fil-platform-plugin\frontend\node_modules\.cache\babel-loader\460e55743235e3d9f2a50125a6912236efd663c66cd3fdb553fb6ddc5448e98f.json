{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MakerOrderListPage=()=>{const{t}=useTranslation();const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchOrders=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch orders associated with products from this maker\nconst{data,error}=await supabase.from('orders').select(`\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    products ( name, maker_id ),\n                    customer_profiles ( real_name ),\n                    agent_profiles ( brand_name )\n                `).filter('products.maker_id','eq',user.id)// Filter by maker_id from products table\n.order('created_at',{ascending:false});if(error){console.error('Error fetching orders:',error);}else{setOrders(data);}setLoading(false);};fetchOrders();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_orders_text')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('all_orders')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('order_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('product_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('customer')}),/*#__PURE__*/_jsx(\"th\",{children:t('agent')}),/*#__PURE__*/_jsx(\"th\",{children:t('shares')}),/*#__PURE__*/_jsx(\"th\",{children:t('storage_cost')}),/*#__PURE__*/_jsx(\"th\",{children:t('pledge_cost')}),/*#__PURE__*/_jsx(\"th\",{children:t('status')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:orders.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"9\",className:\"text-center\",children:t('no_orders_available')})}):orders.map(order=>{var _order$products,_order$customer_profi,_order$agent_profiles;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[order.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:((_order$products=order.products)===null||_order$products===void 0?void 0:_order$products.name)||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:((_order$customer_profi=order.customer_profiles)===null||_order$customer_profi===void 0?void 0:_order$customer_profi.real_name)||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:((_order$agent_profiles=order.agent_profiles)===null||_order$agent_profiles===void 0?void 0:_order$agent_profiles.brand_name)||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:order.shares}),/*#__PURE__*/_jsx(\"td\",{children:order.storage_cost}),/*#__PURE__*/_jsx(\"td\",{children:order.pledge_cost}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:order.review_status==='approved'?'success':'warning',children:order.review_status})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(order.created_at).toLocaleString()})]},order.id);})})]})})})})})]});};export default MakerOrderListPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "MakerOrderListPage", "t", "orders", "setOrders", "loading", "setLoading", "fetchOrders", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "filter", "id", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "_order$products", "_order$customer_profi", "_order$agent_profiles", "substring", "products", "name", "customer_profiles", "real_name", "agent_profiles", "brand_name", "shares", "storage_cost", "pledge_cost", "bg", "review_status", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/MakerOrderListPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MakerOrderListPage = () => {\n    const { t } = useTranslation();\n    const [orders, setOrders] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchOrders = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch orders associated with products from this maker\n            const { data, error } = await supabase\n                .from('orders')\n                .select(`\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    products ( name, maker_id ),\n                    customer_profiles ( real_name ),\n                    agent_profiles ( brand_name )\n                `)\n                .filter('products.maker_id', 'eq', user.id) // Filter by maker_id from products table\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching orders:', error);\n            } else {\n                setOrders(data);\n            }\n            setLoading(false);\n        };\n\n        fetchOrders();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_orders_text')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('all_orders')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('product_name')}</th>\n                                        <th>{t('customer')}</th>\n                                        <th>{t('agent')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('storage_cost')}</th>\n                                        <th>{t('pledge_cost')}</th>\n                                        <th>{t('status')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {orders.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_orders_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>{order.id.substring(0, 8)}...</td>\n                                                <td>{order.products?.name || 'N/A'}</td>\n                                                <td>{order.customer_profiles?.real_name || 'N/A'}</td>\n                                                <td>{order.agent_profiles?.brand_name || 'N/A'}</td>\n                                                <td>{order.shares}</td>\n                                                <td>{order.storage_cost}</td>\n                                                <td>{order.pledge_cost}</td>\n                                                <td><Badge bg={order.review_status === 'approved' ? 'success' : 'warning'}>{order.review_status}</Badge></td>\n                                                <td>{new Date(order.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MakerOrderListPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,MAAM,CAAC,mBAAmB,CAAE,IAAI,CAAEN,IAAI,CAACO,EAAE,CAAE;AAAA,CAC3CC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,KAAK,CAAE,CACPO,OAAO,CAACP,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAClD,CAAC,IAAM,CACHT,SAAS,CAACK,IAAI,CAAC,CACnB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,qBAAqB,CAAC,CAAM,CAAC,CAChD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAgC,QAAA,eACNvB,IAAA,OAAIwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC3CJ,IAAA,CAACR,GAAG,EAAA+B,QAAA,cACAvB,IAAA,CAACP,GAAG,EAAA8B,QAAA,cACAvB,IAAA,CAACN,IAAI,EAAA6B,QAAA,cACDvB,IAAA,CAACN,IAAI,CAAC+B,IAAI,EAAAF,QAAA,cACNrB,KAAA,CAACP,KAAK,EAAC+B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,aAAa,CAAC,CAAK,CAAC,cAC3BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,EAC1B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,MAAM,CAACyB,MAAM,GAAK,CAAC,cAChB9B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAI+B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEnB,CAAC,CAAC,qBAAqB,CAAC,CAAK,CAAC,CACvE,CAAC,CAELC,MAAM,CAAC2B,GAAG,CAACZ,KAAK,OAAAa,eAAA,CAAAC,qBAAA,CAAAC,qBAAA,oBACZjC,KAAA,OAAAqB,QAAA,eACIrB,KAAA,OAAAqB,QAAA,EAAKH,KAAK,CAACD,EAAE,CAACiB,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cACtCpC,IAAA,OAAAuB,QAAA,CAAK,EAAAU,eAAA,CAAAb,KAAK,CAACiB,QAAQ,UAAAJ,eAAA,iBAAdA,eAAA,CAAgBK,IAAI,GAAI,KAAK,CAAK,CAAC,cACxCtC,IAAA,OAAAuB,QAAA,CAAK,EAAAW,qBAAA,CAAAd,KAAK,CAACmB,iBAAiB,UAAAL,qBAAA,iBAAvBA,qBAAA,CAAyBM,SAAS,GAAI,KAAK,CAAK,CAAC,cACtDxC,IAAA,OAAAuB,QAAA,CAAK,EAAAY,qBAAA,CAAAf,KAAK,CAACqB,cAAc,UAAAN,qBAAA,iBAApBA,qBAAA,CAAsBO,UAAU,GAAI,KAAK,CAAK,CAAC,cACpD1C,IAAA,OAAAuB,QAAA,CAAKH,KAAK,CAACuB,MAAM,CAAK,CAAC,cACvB3C,IAAA,OAAAuB,QAAA,CAAKH,KAAK,CAACwB,YAAY,CAAK,CAAC,cAC7B5C,IAAA,OAAAuB,QAAA,CAAKH,KAAK,CAACyB,WAAW,CAAK,CAAC,cAC5B7C,IAAA,OAAAuB,QAAA,cAAIvB,IAAA,CAACJ,KAAK,EAACkD,EAAE,CAAE1B,KAAK,CAAC2B,aAAa,GAAK,UAAU,CAAG,SAAS,CAAG,SAAU,CAAAxB,QAAA,CAAEH,KAAK,CAAC2B,aAAa,CAAQ,CAAC,CAAI,CAAC,cAC7G/C,IAAA,OAAAuB,QAAA,CAAK,GAAI,CAAAyB,IAAI,CAAC5B,KAAK,CAAC6B,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GATjD9B,KAAK,CAACD,EAUX,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAhB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}