{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'bootstrap/dist/css/bootstrap.min.css';import'./i18n';// Initialize i18n\nimport App from'./App';import'./index.css';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('fil-platform-root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/index.js"], "sourcesContent": ["\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './i18n'; // Initialize i18n\nimport App from './App';\nimport './index.css';\n\nconst root = ReactDOM.createRoot(document.getElementById('fil-platform-root'));\n\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,sCAAsC,CAC7C,MAAO,QAAQ,CAAE;AACjB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAErB,KAAM,CAAAC,IAAI,CAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAE9EH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACJ,KAAK,CAACU,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACF,GAAG,GAAE,CAAC,CACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}