"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[190],{1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...c}=e;const o=(0,n.oU)(r,"row"),i=(0,n.gy)(),f=(0,n.Jm)(),m=`${o}-cols`,x=[];return i.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${m}${a}-${r}`)}),(0,l.jsx)(d,{ref:s,...c,className:t()(a,o,...x)})});c.displayName="Row";const o=c},4063:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:c,className:o,as:i="span",...f}=e;const m=(0,n.oU)(r,"badge");return(0,l.jsx)(i,{ref:s,...f,className:t()(o,m,d&&"rounded-pill",c&&`text-${c}`,a&&`bg-${a}`)})});c.displayName="Badge";const o=c},4196:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:c,borderless:o,hover:i,size:f,variant:m,responsive:x,...u}=e;const h=(0,n.oU)(r,"table"),b=t()(a,h,m&&`${h}-${m}`,f&&`${h}-${f}`,d&&`${h}-${"string"===typeof d?`striped-${d}`:"striped"}`,c&&`${h}-bordered`,o&&`${h}-borderless`,i&&`${h}-hover`),N=(0,l.jsx)("table",{...u,className:b,ref:s});if(x){let e=`${h}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,l.jsx)("div",{className:e,children:N})}return N});c.displayName="Table";const o=c},6190:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),d=r(1072),n=r(8602),l=r(8628),c=r(4196),o=r(4063),i=r(4312),f=r(4117),m=r(579);const x=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[x,u]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;u(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void u(!1);const{data:a,error:t}=await e.from("customer_profiles").select("\n                    user_id,\n                    real_name,\n                    verify_status,\n                    users ( email, created_at )\n                ").eq("agent_id",s.id).order("created_at",{ascending:!1});t?console.error("Error fetching members:",t):r(a),u(!1)})()},[]),x?(0,m.jsx)("div",{children:e("loading_members")}):(0,m.jsxs)(t.A,{children:[(0,m.jsx)("h2",{className:"mb-4",children:e("my_subordinates")}),(0,m.jsx)(d.A,{children:(0,m.jsx)(n.A,{children:(0,m.jsx)(l.A,{children:(0,m.jsx)(l.A.Body,{children:(0,m.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,m.jsx)("thead",{children:(0,m.jsxs)("tr",{children:[(0,m.jsx)("th",{children:e("user_id")}),(0,m.jsx)("th",{children:e("email")}),(0,m.jsx)("th",{children:e("real_name")}),(0,m.jsx)("th",{children:e("kyc_status")}),(0,m.jsx)("th",{children:e("registration_time")})]})}),(0,m.jsx)("tbody",{children:0===s.length?(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:"5",className:"text-center",children:e("no_subordinates")})}):s.map(e=>{var s,r;return(0,m.jsxs)("tr",{children:[(0,m.jsxs)("td",{children:[e.user_id.substring(0,8),"..."]}),(0,m.jsx)("td",{children:(null===(s=e.users)||void 0===s?void 0:s.email)||"N/A"}),(0,m.jsx)("td",{children:e.real_name||"N/A"}),(0,m.jsx)("td",{children:(0,m.jsx)(o.A,{bg:"approved"===e.verify_status?"success":"warning",children:e.verify_status})}),(0,m.jsx)("td",{children:new Date(null===(r=e.users)||void 0===r?void 0:r.created_at).toLocaleString()})]},e.user_id)})})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:c,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,n.oU)(r,"col");const l=(0,n.gy)(),c=(0,n.Jm)(),o=[],i=[];return l.forEach(e=>{const s=d[e];let a,t,n;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const l=e!==c?`-${e}`:"";a&&o.push(!0===a?`${r}${l}`:`${r}${l}-${a}`),null!=n&&i.push(`order${l}-${n}`),null!=t&&i.push(`offset${l}-${t}`)}),[{...d,className:t()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,l.jsx)(d,{...a,ref:s,className:t()(r,!o.length&&c)})});c.displayName="Col";const o=c},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-body"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const o=c,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-footer"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});i.displayName="CardFooter";const f=i;var m=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...o}=e;const i=(0,n.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,l.jsx)(m.A.Provider,{value:f,children:(0,l.jsx)(c,{ref:s,...o,className:t()(a,i)})})});x.displayName="CardHeader";const u=x,h=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:c="img",...o}=e;const i=(0,n.oU)(r,"card-img");return(0,l.jsx)(c,{ref:s,className:t()(d?`${i}-${d}`:i,a),...o})});h.displayName="CardImg";const b=h,N=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});N.displayName="CardImgOverlay";const j=N,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...c}=e;return a=(0,n.oU)(a,"card-link"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});p.displayName="CardLink";const v=p;var y=r(4488);const $=(0,y.A)("h6"),g=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=$,...c}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});g.displayName="CardSubtitle";const w=g,_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...c}=e;return a=(0,n.oU)(a,"card-text"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});_.displayName="CardText";const P=_,A=(0,y.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=A,...c}=e;return a=(0,n.oU)(a,"card-title"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:c,border:i,body:f=!1,children:m,as:x="div",...u}=e;const h=(0,n.oU)(r,"card");return(0,l.jsx)(x,{ref:s,...u,className:t()(a,h,d&&`bg-${d}`,c&&`text-${c}`,i&&`border-${i}`),children:f?(0,l.jsx)(o,{children:m}):m})});C.displayName="Card";const k=Object.assign(C,{Img:b,Title:U,Subtitle:w,Body:o,Link:v,Text:P,Header:u,Footer:f,ImgOverlay:j})}}]);
//# sourceMappingURL=190.2a8236f7.chunk.js.map