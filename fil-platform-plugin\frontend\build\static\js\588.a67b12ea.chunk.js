"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[588],{588:(e,s,n)=>{n.r(s),n.d(s,{default:()=>_});n(5043);var a=n(4117),i=n(8628),l=n(3519),r=n(1072),t=n(8602),d=n(4282),c=n(108),h=n(2998),x=n(7734),o=n(2185),j=n(760),u=n(9923),m=n(713),A=n(2872),f=n(1283),g=n(579);const p=[{name:"7/1",fil:.2,usd:1.1},{name:"7/2",fil:.25,usd:1.3},{name:"7/3",fil:.22,usd:1.2},{name:"7/4",fil:.3,usd:1.5},{name:"7/5",fil:.28,usd:1.4},{name:"7/6",fil:.35,usd:1.8},{name:"7/7",fil:.4,usd:2}],y=e=>{let{title:s,value:n,subValue:a,variant:l}=e;return(0,g.jsx)(i.A,{className:`bg-${l} text-white mb-3`,children:(0,g.jsxs)(i.A.Body,{children:[(0,g.jsx)(i.A.Title,{children:s}),(0,g.jsx)("h3",{children:n}),(0,g.jsx)("p",{children:a})]})})},_=()=>{const{t:e}=(0,a.Bd)(),s=(0,f.Zp)(),n=e=>{console.log("Navigating to:",e),console.log("Current URL:",window.location.href),s(e)};return(0,g.jsxs)(l.A,{fluid:!0,children:[(0,g.jsx)(r.A,{className:"mb-3",children:(0,g.jsx)(t.A,{children:(0,g.jsx)("h2",{children:e("dashboard")})})}),(0,g.jsxs)(r.A,{children:[(0,g.jsx)(t.A,{md:3,children:(0,g.jsx)(y,{title:e("total_earnings"),value:"12,345.67 FIL",subValue:"\u2248 $50,123.45 USD",variant:"primary"})}),(0,g.jsx)(t.A,{md:3,children:(0,g.jsx)(y,{title:e("yesterday_earnings"),value:"123.45 FIL",subValue:"\u2248 $501.23 USD",variant:"success"})}),(0,g.jsx)(t.A,{md:3,children:(0,g.jsx)(y,{title:e("available_balance"),value:"1,234.56 FIL",subValue:"\u2248 $5,012.34 USD",variant:"info"})}),(0,g.jsx)(t.A,{md:3,children:(0,g.jsx)(y,{title:e("power_pledge"),value:"10,000.00 FIL",subValue:"\u2248 $40,500.00 USD",variant:"warning"})})]}),(0,g.jsx)(r.A,{children:(0,g.jsx)(t.A,{children:(0,g.jsx)(i.A,{children:(0,g.jsxs)(i.A.Body,{children:[(0,g.jsx)(i.A.Title,{children:e("earnings_trend")}),(0,g.jsx)(c.u,{width:"100%",height:400,children:(0,g.jsxs)(h.b,{data:p,children:[(0,g.jsx)(x.d,{strokeDasharray:"3 3"}),(0,g.jsx)(o.W,{dataKey:"name"}),(0,g.jsx)(j.h,{yAxisId:"left",label:{value:e("fil"),angle:-90,position:"insideLeft"}}),(0,g.jsx)(j.h,{yAxisId:"right",orientation:"right",label:{value:e("usd"),angle:-90,position:"insideRight"}}),(0,g.jsx)(u.m,{formatter:(s,n)=>[s,e(n)]}),(0,g.jsx)(m.s,{}),(0,g.jsx)(A.N,{yAxisId:"left",type:"monotone",dataKey:"fil",stroke:"#8884d8",name:e("FIL_earnings")}),(0,g.jsx)(A.N,{yAxisId:"right",type:"monotone",dataKey:"usd",stroke:"#82ca9d",name:e("USD_estimate")})]})})]})})})}),(0,g.jsxs)(r.A,{className:"mt-4",children:[(0,g.jsx)(t.A,{md:6,className:"text-center",children:(0,g.jsx)(i.A,{children:(0,g.jsxs)(i.A.Body,{children:[(0,g.jsx)("h4",{children:e("wallet_management")}),(0,g.jsx)("p",{children:e("manage_your_digital_assets")}),(0,g.jsx)(d.A,{variant:"primary",onClick:()=>n("/wallet"),children:e("enter_wallet")})]})})}),(0,g.jsx)(t.A,{md:6,className:"text-center",children:(0,g.jsx)(i.A,{children:(0,g.jsxs)(i.A.Body,{children:[(0,g.jsx)("h4",{children:e("buy_power")}),(0,g.jsx)("p",{children:e("view_and_purchase_new_power_products")}),(0,g.jsx)(d.A,{variant:"success",onClick:()=>n("/products"),children:e("browse_products")})]})})})]})]})}}}]);
//# sourceMappingURL=588.a67b12ea.chunk.js.map