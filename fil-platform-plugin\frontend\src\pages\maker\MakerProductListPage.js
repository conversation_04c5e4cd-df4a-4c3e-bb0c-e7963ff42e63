
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchProducts = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    is_disabled,
                    review_status,
                    created_at
                `)
                .eq('maker_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching products:', error);
            } else {
                setProducts(data);
            }
            setLoading(false);
        };

        fetchProducts();
    }, []);

    if (loading) {
        return <div>Loading products...</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">我的产品</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Button variant="success" className="mb-3">新增产品</Button>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>产品ID</th>
                                        <th>名称</th>
                                        <th>类别</th>
                                        <th>价格 (USDT/份)</th>
                                        <th>总份额</th>
                                        <th>已售份额</th>
                                        <th>状态</th>
                                        <th>审核状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {products.length === 0 ? (
                                        <tr>
                                            <td colSpan="10" className="text-center">暂无产品</td>
                                        </tr>
                                    ) : (
                                        products.map(product => (
                                            <tr key={product.id}>
                                                <td>{product.id.substring(0, 8)}...</td>
                                                <td>{product.name}</td>
                                                <td><Badge bg={product.category === 'spot' ? 'success' : 'primary'}>{product.category === 'spot' ? '现货' : '期货'}</Badge></td>
                                                <td>{product.price}</td>
                                                <td>{product.total_shares}</td>
                                                <td>{product.sold_shares}</td>
                                                <td><Badge bg={product.is_disabled ? 'danger' : 'success'}>{product.is_disabled ? '已禁用' : '启用中'}</Badge></td>
                                                <td><Badge bg={product.review_status === 'approved' ? 'success' : 'warning'}>{product.review_status}</Badge></td>
                                                <td>{new Date(product.created_at).toLocaleString()}</td>
                                                <td>
                                                    <Button variant="info" size="sm" className="me-2">编辑</Button>
                                                    <Button variant="danger" size="sm">删除</Button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MakerProductListPage;
