"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[880],{880:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(4196),n=r(3519),d=r(1072),c=r(8602),l=r(8628),i=r(4737),o=r(4312),f=r(4117),h=r(579);const x=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[x,m]=(0,a.useState)(!0),[u,j]=(0,a.useState)("overview");if((0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;m(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void m(!1);const{data:a,error:t}=await e.from("user_assets").select("*").eq("user_id",s.id);t?console.error("Error fetching assets:",t):r(a),m(!1)})()},[]),x)return(0,h.jsx)("div",{children:e("loading_wallet")});return(0,h.jsxs)(n.A,{children:[(0,h.jsx)("h2",{className:"mb-4",children:e("my_wallet")}),(0,h.jsx)(d.A,{children:(0,h.jsx)(c.A,{children:(0,h.jsxs)(l.A,{children:[(0,h.jsx)(l.A.Header,{children:(0,h.jsxs)(i.A,{variant:"tabs",defaultActiveKey:"overview",onSelect:e=>j(e),children:[(0,h.jsx)(i.A.Item,{children:(0,h.jsx)(i.A.Link,{eventKey:"overview",children:e("overview")})}),(0,h.jsx)(i.A.Item,{children:(0,h.jsx)(i.A.Link,{eventKey:"deposit",children:e("deposit")})}),(0,h.jsx)(i.A.Item,{children:(0,h.jsx)(i.A.Link,{eventKey:"withdraw",children:e("withdraw")})}),(0,h.jsx)(i.A.Item,{children:(0,h.jsx)(i.A.Link,{eventKey:"exchange",children:e("exchange")})})]})}),(0,h.jsx)(l.A.Body,{children:(()=>{switch(u){case"overview":return(0,h.jsxs)(t.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,h.jsx)("thead",{children:(0,h.jsxs)("tr",{children:[(0,h.jsx)("th",{children:e("currency")}),(0,h.jsx)("th",{children:e("available_balance")}),(0,h.jsx)("th",{children:e("locked_balance")}),(0,h.jsx)("th",{children:e("total_balance")}),(0,h.jsx)("th",{children:e("withdrawn")})]})}),(0,h.jsx)("tbody",{children:0===s.length?(0,h.jsx)("tr",{children:(0,h.jsx)("td",{colSpan:"5",className:"text-center",children:e("no_assets")})}):s.map(e=>(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:e.currency_code}),(0,h.jsx)("td",{children:e.balance_available}),(0,h.jsx)("td",{children:e.balance_locked}),(0,h.jsx)("td",{children:e.balance_total}),(0,h.jsx)("td",{children:e.withdrawn_total})]},e.currency_code))})]});case"deposit":return(0,h.jsx)("div",{children:e("deposit_coming_soon")});case"withdraw":return(0,h.jsx)("div",{children:e("withdraw_coming_soon")});case"exchange":return(0,h.jsx)("div",{children:e("exchange_coming_soon")});default:return null}})()})]})})})]})}},1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),c=r(579);const l=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...l}=e;const i=(0,d.oU)(r,"row"),o=(0,d.gy)(),f=(0,d.Jm)(),h=`${i}-cols`,x=[];return o.forEach(e=>{const s=l[e];let r;delete l[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${h}${a}-${r}`)}),(0,c.jsx)(n,{ref:s,...l,className:t()(a,i,...x)})});l.displayName="Row";const i=l},4196:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),c=r(579);const l=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:n,bordered:l,borderless:i,hover:o,size:f,variant:h,responsive:x,...m}=e;const u=(0,d.oU)(r,"table"),j=t()(a,u,h&&`${u}-${h}`,f&&`${u}-${f}`,n&&`${u}-${"string"===typeof n?`striped-${n}`:"striped"}`,l&&`${u}-bordered`,i&&`${u}-borderless`,o&&`${u}-hover`),v=(0,c.jsx)("table",{...m,className:j,ref:s});if(x){let e=`${u}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,c.jsx)("div",{className:e,children:v})}return v});l.displayName="Table";const i=l},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),c=r(579);const l=n.forwardRef((e,s)=>{const[{className:r,...a},{as:n="div",bsPrefix:l,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...n}=e;r=(0,d.oU)(r,"col");const c=(0,d.gy)(),l=(0,d.Jm)(),i=[],o=[];return c.forEach(e=>{const s=n[e];let a,t,d;delete n[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:d}=s):a=s;const c=e!==l?`-${e}`:"";a&&i.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=d&&o.push(`order${c}-${d}`),null!=t&&o.push(`offset${c}-${t}`)}),[{...n,className:t()(a,...i,...o)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,c.jsx)(n,{...a,ref:s,className:t()(r,!i.length&&l)})});l.displayName="Col";const i=l},8628:(e,s,r)=>{r.d(s,{A:()=>C});var a=r(8139),t=r.n(a),n=r(5043),d=r(7852),c=r(579);const l=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...l}=e;return a=(0,d.oU)(a,"card-body"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});l.displayName="CardBody";const i=l,o=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...l}=e;return a=(0,d.oU)(a,"card-footer"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});o.displayName="CardFooter";const f=o;var h=r(1778);const x=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...i}=e;const o=(0,d.oU)(r,"card-header"),f=(0,n.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,c.jsx)(h.A.Provider,{value:f,children:(0,c.jsx)(l,{ref:s,...i,className:t()(a,o)})})});x.displayName="CardHeader";const m=x,u=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:n,as:l="img",...i}=e;const o=(0,d.oU)(r,"card-img");return(0,c.jsx)(l,{ref:s,className:t()(n?`${o}-${n}`:o,a),...i})});u.displayName="CardImg";const j=u,v=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...l}=e;return a=(0,d.oU)(a,"card-img-overlay"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});v.displayName="CardImgOverlay";const b=v,N=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="a",...l}=e;return a=(0,d.oU)(a,"card-link"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});N.displayName="CardLink";const p=N;var w=r(4488);const y=(0,w.A)("h6"),$=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=y,...l}=e;return a=(0,d.oU)(a,"card-subtitle"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});$.displayName="CardSubtitle";const g=$,A=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="p",...l}=e;return a=(0,d.oU)(a,"card-text"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});A.displayName="CardText";const _=A,P=(0,w.A)("h5"),R=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=P,...l}=e;return a=(0,d.oU)(a,"card-title"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...l})});R.displayName="CardTitle";const U=R,k=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:n,text:l,border:o,body:f=!1,children:h,as:x="div",...m}=e;const u=(0,d.oU)(r,"card");return(0,c.jsx)(x,{ref:s,...m,className:t()(a,u,n&&`bg-${n}`,l&&`text-${l}`,o&&`border-${o}`),children:f?(0,c.jsx)(i,{children:h}):h})});k.displayName="Card";const C=Object.assign(k,{Img:j,Title:U,Subtitle:g,Body:i,Link:p,Text:_,Header:m,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=880.17ebb583.chunk.js.map