{"version": 3, "file": "static/js/285.419abf5e.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sJCjCA,MAkGA,EAlGoBC,KAChB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GA4CvC,OA1CAG,EAAAA,EAAAA,WAAU,KACgBC,WAClB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAMf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,uBACLC,OAAO,sTAWPC,GAAG,cAAeN,EAAKO,IACvBC,MAAM,aAAc,CAAEC,WAAW,IAElCN,EACAO,QAAQP,MAAM,2BAA4BA,GAE1CZ,EAAYQ,GAEhBL,GAAW,IAGfiB,IACD,IAEClB,GACOT,EAAAA,EAAAA,KAAA,OAAA4B,SAAMxB,EAAE,uBAIfyB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACN5B,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM8C,SAAExB,EAAE,eACxBJ,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAmD,UACA5B,EAAAA,EAAAA,KAAC+B,EAAAA,EAAG,CAAAH,UACA5B,EAAAA,EAAAA,KAACgC,EAAAA,EAAI,CAAAJ,UACD5B,EAAAA,EAAAA,KAACgC,EAAAA,EAAKC,KAAI,CAAAL,UACNC,EAAAA,EAAAA,MAACK,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAV,SAAA,EACpC5B,EAAAA,EAAAA,KAAA,SAAA4B,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI5B,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,cACPJ,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,aACPJ,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,kBACPJ,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,UACPJ,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA4B,SAAKxB,EAAE,gBAGfJ,EAAAA,EAAAA,KAAA,SAAA4B,SACyB,IAApBtB,EAASiC,QACNvC,EAAAA,EAAAA,KAAA,MAAA4B,UACI5B,EAAAA,EAAAA,KAAA,MAAIwC,QAAQ,IAAI1D,UAAU,cAAa8C,SAAExB,EAAE,uBAG/CE,EAASmC,IAAIC,IACTb,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKc,EAAQnB,GAAGoB,UAAU,EAAG,GAAG,UAChC3C,EAAAA,EAAAA,KAAA,MAAA4B,SAAKc,EAAQE,SAAWF,EAAQE,SAASD,UAAU,EAAG,GAAK,MAAQ,SACnE3C,EAAAA,EAAAA,KAAA,MAAA4B,SAAKc,EAAQG,gBACb7C,EAAAA,EAAAA,KAAA,MAAA4B,SAAKc,EAAQI,iBACb9C,EAAAA,EAAAA,KAAA,MAAA4B,SAAKc,EAAQK,cACb/C,EAAAA,EAAAA,KAAA,MAAA4B,UAAIC,EAAAA,EAAAA,MAACmB,EAAAA,EAAK,CAACC,GAAyB,IAArBP,EAAQQ,SAAiB,UAAY,OAAOtB,SAAA,CAAqB,IAAnBc,EAAQQ,SAAe,UACpFlD,EAAAA,EAAAA,KAAA,MAAA4B,SAAK,IAAIuB,KAAKT,EAAQU,YAAYC,qBAP7BX,EAAQnB,qB,sFC7E7D,MAAMyB,EAAqBtE,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRoE,EAAK,UAAS,KACdK,GAAO,EAAK,KACZC,EAAI,UACJzE,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAM6E,GAASrE,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW0E,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQN,GAAM,MAAMA,SAGzGD,EAAM9C,YAAc,QACpB,S,sFCjBA,MAAMgC,EAAqBxD,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTqD,EAAO,SACPC,EAAQ,WACRqB,EAAU,MACVpB,EAAK,KACLqB,EAAI,QACJC,EAAO,WACPrB,KACGrD,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmByE,GAAW,GAAGzE,KAAqByE,IAAWD,GAAQ,GAAGxE,KAAqBwE,IAAQvB,GAAW,GAAGjD,KAAwC,kBAAZiD,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGlD,aAA8BuE,GAAc,GAAGvE,eAAgCmD,GAAS,GAAGnD,WACxV0E,GAAqB5D,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAI0D,EAAY,CACd,IAAIuB,EAAkB,GAAG3E,eAIzB,MAH0B,kBAAfoD,IACTuB,EAAkB,GAAGA,KAAmBvB,MAEtBtC,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAW+E,EACXjC,SAAUgC,GAEd,CACA,OAAOA,IAET1B,EAAMhC,YAAc,QACpB,S,sFCQA,MAAM6B,EAAmBrD,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGgF,IAEH/E,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRkF,IAjDG,SAAepF,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBwE,EAAQ,GACRtE,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIqE,EACAC,EACAzC,SAHGvC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCoE,OACAC,SACAzC,SACE5B,GAEJoE,EAAOpE,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDqE,GAAMD,EAAMhE,MAAc,IAATiE,EAAgB,GAAGnF,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASkE,KACvE,MAATxC,GAAe/B,EAAQM,KAAK,QAAQD,KAAS0B,KACnC,MAAVyC,GAAgBxE,EAAQM,KAAK,SAASD,KAASmE,OAE9C,CAAC,IACHhF,EACHH,UAAWmB,IAAWnB,KAAciF,KAAUtE,IAC7C,CACDV,KACAF,WACAkF,SAEJ,CAWOG,CAAOjF,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/B8E,EACHlF,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYiF,EAAMxB,QAAU1D,OAGtDkD,EAAI7B,YAAc,MAClB,S,sFC1DA,MAAMiE,EAAwBzF,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPkF,EAASjE,YAAc,WACvB,UCdMkE,EAA0B1F,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPmF,EAAWlE,YAAc,aACzB,U,cCZA,MAAMmE,EAA0B3F,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM6E,GAASrE,EAAAA,EAAAA,IAAmBN,EAAU,eACtCyF,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBhB,IAClB,CAACA,IACL,OAAoBxD,EAAAA,EAAAA,KAAKyE,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP1C,UAAuB5B,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW0E,SAIvCa,EAAWnE,YAAc,aACzB,UCvBM0E,EAAuBlG,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACT6E,EACA5E,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM6E,GAASrE,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAW0D,EAAU,GAAGH,KAAUG,IAAYH,EAAQ1E,MAC9DG,MAGP2F,EAAQ1E,YAAc,UACtB,UCjBM2E,EAA8BnG,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP4F,EAAe3E,YAAc,iBAC7B,UCdM4E,EAAwBpG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP6F,EAAS5E,YAAc,WACvB,U,cCbA,MAAM6E,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BvG,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAY+F,KACb9F,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPgG,EAAa/E,YAAc,eAC3B,UChBMgF,EAAwBxG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPiG,EAAShF,YAAc,WACvB,UCbMiF,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB1G,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYmG,KACblG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPmG,EAAUlF,YAAc,YACxB,UCPM8B,EAAoBtD,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACTmE,EAAE,KACFM,EAAI,OACJ8B,EAAM,KACNC,GAAO,EAAK,SACZ1D,EAEA7C,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM6E,GAASrE,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW0E,EAAQP,GAAM,MAAMA,IAAMM,GAAQ,QAAQA,IAAQ8B,GAAU,UAAUA,KACvGzD,SAAU0D,GAAoBtF,EAAAA,EAAAA,KAAKmE,EAAU,CAC3CvC,SAAUA,IACPA,MAGTI,EAAK9B,YAAc,OACnB,QAAeqF,OAAOC,OAAOxD,EAAM,CACjCyD,IAAKb,EACLc,MAAON,EACPO,SAAUV,EACVhD,KAAMkC,EACNyB,KAAMd,EACNe,KAAMX,EACNY,OAAQzB,EACR0B,OAAQ3B,EACR4B,WAAYnB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "pages/customer/MyGainsPage.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MyGainsPage = () => {\n    const { t } = useTranslation();\n    const [earnings, setEarnings] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchEarnings = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // This is a simplified fetch. In a real app, you'd join with orders/products\n            // to get more context for each earning.\n            const { data, error } = await supabase\n                .from('order_distributions') // Assuming this table holds customer earnings\n                .select(`\n                    id,\n                    batch_id,\n                    order_id,\n                    customer_id,\n                    share_amount,\n                    reward_amount,\n                    fee_amount,\n                    progress,\n                    created_at\n                `)\n                .eq('customer_id', user.id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching earnings:', error);\n            } else {\n                setEarnings(data);\n            }\n            setLoading(false);\n        };\n\n        fetchEarnings();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_earnings')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_gains')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('gain_id')}</th>\n                                        <th>{t('order_id')}</th>\n                                        <th>{t('shares')}</th>\n                                        <th>{t('gain_amount')}</th>\n                                        <th>{t('fee')}</th>\n                                        <th>{t('progress')}</th>\n                                        <th>{t('time')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {earnings.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"7\" className=\"text-center\">{t('no_gains_record')}</td>\n                                        </tr>\n                                    ) : (\n                                        earnings.map(earning => (\n                                            <tr key={earning.id}>\n                                                <td>{earning.id.substring(0, 8)}...</td>\n                                                <td>{earning.order_id ? earning.order_id.substring(0, 8) + '...' : 'N/A'}</td>\n                                                <td>{earning.share_amount}</td>\n                                                <td>{earning.reward_amount}</td>\n                                                <td>{earning.fee_amount}</td>\n                                                <td><Badge bg={earning.progress === 1 ? 'success' : 'info'}>{earning.progress * 100}%</Badge></td>\n                                                <td>{new Date(earning.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MyGainsPage;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "MyGainsPage", "t", "useTranslation", "earnings", "setEarnings", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "order", "ascending", "console", "fetchEarnings", "children", "_jsxs", "Container", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "earning", "substring", "order_id", "share_amount", "reward_amount", "fee_amount", "Badge", "bg", "progress", "Date", "created_at", "toLocaleString", "pill", "text", "prefix", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}