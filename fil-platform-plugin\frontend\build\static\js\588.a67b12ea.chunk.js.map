{"version": 3, "file": "static/js/588.a67b12ea.chunk.js", "mappings": "wSAOA,MAAMA,EAAY,CAChB,CAAEC,KAAM,MAAOC,IAAK,GAAKC,IAAK,KAC9B,CAAEF,KAAM,MAAOC,IAAK,IAAMC,IAAK,KAC/B,CAAEF,KAAM,MAAOC,IAAK,IAAMC,IAAK,KAC/B,CAAEF,KAAM,MAAOC,IAAK,GAAKC,IAAK,KAC9B,CAAEF,KAAM,MAAOC,IAAK,IAAMC,IAAK,KAC/B,CAAEF,KAAM,MAAOC,IAAK,IAAMC,IAAK,KAC/B,CAAEF,KAAM,MAAOC,IAAK,GAAKC,IAAK,IAG1BC,EAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAQ,QAAEC,GAASJ,EAAA,OACjDK,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,UAAW,MAAMH,oBAA0BI,UAC7CC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEP,KACbI,EAAAA,EAAAA,KAAA,MAAAG,SAAKN,KACLG,EAAAA,EAAAA,KAAA,KAAAG,SAAIL,UA+FhB,EA1F0BS,KACtB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACRC,GAAWC,EAAAA,EAAAA,MAEXC,EAAoBC,IACtBC,QAAQC,IAAI,iBAAkBF,GAC9BC,QAAQC,IAAI,eAAgBC,OAAOC,SAASC,MAC5CR,EAASG,IAGb,OACIT,EAAAA,EAAAA,MAACe,EAAAA,EAAS,CAACC,OAAK,EAAAjB,SAAA,EACZH,EAAAA,EAAAA,KAACqB,EAAAA,EAAG,CAACnB,UAAU,OAAMC,UACjBH,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAAAnB,UACAH,EAAAA,EAAAA,KAAA,MAAAG,SAAKK,EAAE,oBAIfJ,EAAAA,EAAAA,MAACiB,EAAAA,EAAG,CAAAlB,SAAA,EACAH,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAACC,GAAI,EAAEpB,UACPH,EAAAA,EAAAA,KAACN,EAAQ,CAACE,MAAOY,EAAE,kBAAmBX,MAAM,gBAAgBC,SAAS,wBAAmBC,QAAQ,eAEpGC,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAACC,GAAI,EAAEpB,UACPH,EAAAA,EAAAA,KAACN,EAAQ,CAACE,MAAOY,EAAE,sBAAuBX,MAAM,aAAaC,SAAS,qBAAgBC,QAAQ,eAElGC,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAACC,GAAI,EAAEpB,UACPH,EAAAA,EAAAA,KAACN,EAAQ,CAACE,MAAOY,EAAE,qBAAsBX,MAAM,eAAeC,SAAS,uBAAkBC,QAAQ,YAErGC,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAACC,GAAI,EAAEpB,UACPH,EAAAA,EAAAA,KAACN,EAAQ,CAACE,MAAOY,EAAE,gBAAiBX,MAAM,gBAAgBC,SAAS,wBAAmBC,QAAQ,kBAItGC,EAAAA,EAAAA,KAACqB,EAAAA,EAAG,CAAAlB,UACAH,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAAAnB,UACAH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAACC,EAAAA,EAAKK,MAAK,CAAAH,SAAEK,EAAE,qBACfR,EAAAA,EAAAA,KAACwB,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIvB,UAC1CC,EAAAA,EAAAA,MAACuB,EAAAA,EAAS,CAACC,KAAMtC,EAAUa,SAAA,EACvBH,EAAAA,EAAAA,KAAC6B,EAAAA,EAAa,CAACC,gBAAgB,SAC/B9B,EAAAA,EAAAA,KAAC+B,EAAAA,EAAK,CAACC,QAAQ,UACfhC,EAAAA,EAAAA,KAACiC,EAAAA,EAAK,CAACC,QAAQ,OAAOC,MAAO,CAAEtC,MAAOW,EAAE,OAAQ4B,OAAQ,GAAIC,SAAU,iBACtErC,EAAAA,EAAAA,KAACiC,EAAAA,EAAK,CAACC,QAAQ,QAAQI,YAAY,QAAQH,MAAO,CAAEtC,MAAOW,EAAE,OAAQ4B,OAAQ,GAAIC,SAAU,kBAC3FrC,EAAAA,EAAAA,KAACuC,EAAAA,EAAO,CAACC,UAAWA,CAAC3C,EAAON,IAAS,CAACM,EAAOW,EAAEjB,OAC/CS,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,KACPzC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAI,CAACR,QAAQ,OAAOS,KAAK,WAAWX,QAAQ,MAAMY,OAAO,UAAUrD,KAAMiB,EAAE,mBAC5ER,EAAAA,EAAAA,KAAC0C,EAAAA,EAAI,CAACR,QAAQ,QAAQS,KAAK,WAAWX,QAAQ,MAAMY,OAAO,UAAUrD,KAAMiB,EAAE,iCAQpGJ,EAAAA,EAAAA,MAACiB,EAAAA,EAAG,CAACnB,UAAU,OAAMC,SAAA,EAClBH,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAACC,GAAI,EAAGrB,UAAU,cAAaC,UAC/BH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAAA,MAAAG,SAAKK,EAAE,wBACPR,EAAAA,EAAAA,KAAA,KAAAG,SAAIK,EAAE,iCACNR,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CACH9C,QAAQ,UACR+C,QAASA,IAAMlC,EAAiB,WAAWT,SAE1CK,EAAE,0BAKnBR,EAAAA,EAAAA,KAACsB,EAAAA,EAAG,CAACC,GAAI,EAAGrB,UAAU,cAAaC,UAC/BH,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAAAE,UACDC,EAAAA,EAAAA,MAACH,EAAAA,EAAKI,KAAI,CAAAF,SAAA,EACNH,EAAAA,EAAAA,KAAA,MAAAG,SAAKK,EAAE,gBACPR,EAAAA,EAAAA,KAAA,KAAAG,SAAIK,EAAE,2CACNR,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CACH9C,QAAQ,UACR+C,QAASA,IAAMlC,EAAiB,aAAaT,SAE5CK,EAAE,kC", "sources": ["pages/customer/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useNavigate } from 'react-router-dom';\n\n// Mock data, we will fetch this from Supabase later\nconst chartData = [\n  { name: '7/1', fil: 0.2, usd: 1.1 },\n  { name: '7/2', fil: 0.25, usd: 1.3 },\n  { name: '7/3', fil: 0.22, usd: 1.2 },\n  { name: '7/4', fil: 0.3, usd: 1.5 },\n  { name: '7/5', fil: 0.28, usd: 1.4 },\n  { name: '7/6', fil: 0.35, usd: 1.8 },\n  { name: '7/7', fil: 0.4, usd: 2.0 },\n];\n\nconst StatCard = ({ title, value, subValue, variant }) => (\n    <Card className={`bg-${variant} text-white mb-3`}>\n        <Card.Body>\n            <Card.Title>{title}</Card.Title>\n            <h3>{value}</h3>\n            <p>{subValue}</p>\n        </Card.Body>\n    </Card>\n);\n\nconst CustomerDashboard = () => {\n    const { t } = useTranslation();\n    const navigate = useNavigate();\n\n    const handleNavigation = (path) => {\n        console.log('Navigating to:', path);\n        console.log('Current URL:', window.location.href);\n        navigate(path);\n    };\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={3}>\n                    <StatCard title={t('total_earnings')} value=\"12,345.67 FIL\" subValue=\"≈ $50,123.45 USD\" variant=\"primary\" />\n                </Col>\n                <Col md={3}>\n                    <StatCard title={t('yesterday_earnings')} value=\"123.45 FIL\" subValue=\"≈ $501.23 USD\" variant=\"success\" />\n                </Col>\n                <Col md={3}>\n                    <StatCard title={t('available_balance')} value=\"1,234.56 FIL\" subValue=\"≈ $5,012.34 USD\" variant=\"info\" />\n                </Col>\n                <Col md={3}>\n                    <StatCard title={t('power_pledge')} value=\"10,000.00 FIL\" subValue=\"≈ $40,500.00 USD\" variant=\"warning\" />\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('earnings_trend')}</Card.Title>\n                            <ResponsiveContainer width=\"100%\" height={400}>\n                                <LineChart data={chartData}>\n                                    <CartesianGrid strokeDasharray=\"3 3\" />\n                                    <XAxis dataKey=\"name\" />\n                                    <YAxis yAxisId=\"left\" label={{ value: t('fil'), angle: -90, position: 'insideLeft' }} />\n                                    <YAxis yAxisId=\"right\" orientation=\"right\" label={{ value: t('usd'), angle: -90, position: 'insideRight' }} />\n                                    <Tooltip formatter={(value, name) => [value, t(name)]} />\n                                    <Legend />\n                                    <Line yAxisId=\"left\" type=\"monotone\" dataKey=\"fil\" stroke=\"#8884d8\" name={t('FIL_earnings')} />\n                                    <Line yAxisId=\"right\" type=\"monotone\" dataKey=\"usd\" stroke=\"#82ca9d\" name={t('USD_estimate')} />\n                                </LineChart>\n                            </ResponsiveContainer>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n             <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('wallet_management')}</h4>\n                            <p>{t('manage_your_digital_assets')}</p>\n                            <Button\n                                variant=\"primary\"\n                                onClick={() => handleNavigation('/wallet')}\n                            >\n                                {t('enter_wallet')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('buy_power')}</h4>\n                            <p>{t('view_and_purchase_new_power_products')}</p>\n                            <Button\n                                variant=\"success\"\n                                onClick={() => handleNavigation('/products')}\n                            >\n                                {t('browse_products')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n        </Container>\n    );\n};\n\nexport default CustomerDashboard;"], "names": ["chartData", "name", "fil", "usd", "StatCard", "_ref", "title", "value", "subValue", "variant", "_jsx", "Card", "className", "children", "_jsxs", "Body", "Title", "CustomerDashboard", "t", "useTranslation", "navigate", "useNavigate", "handleNavigation", "path", "console", "log", "window", "location", "href", "Container", "fluid", "Row", "Col", "md", "ResponsiveContainer", "width", "height", "Line<PERSON>hart", "data", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "YA<PERSON>s", "yAxisId", "label", "angle", "position", "orientation", "<PERSON><PERSON><PERSON>", "formatter", "Legend", "Line", "type", "stroke", "<PERSON><PERSON>", "onClick"], "sourceRoot": ""}