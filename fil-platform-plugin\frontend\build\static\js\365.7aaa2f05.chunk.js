"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[365],{746:(e,s,r)=>{r.r(s),r.d(s,{default:()=>m});var a=r(5043),t=r(3519),d=r(1072),n=r(8602),l=r(8628),o=r(4196),c=r(4063),i=r(4312),f=r(4117),x=r(579);const m=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[m,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("orders").select("\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    products ( name, maker_id ),\n                    customer_profiles ( real_name ),\n                    agent_profiles ( brand_name )\n                ").filter("products.maker_id","eq",s.id).order("created_at",{ascending:!1});t?console.error("Error fetching orders:",t):r(a),h(!1)})()},[]),m?(0,x.jsx)("div",{children:e("loading_orders_text")}):(0,x.jsxs)(t.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("all_orders")}),(0,x.jsx)(d.A,{children:(0,x.jsx)(n.A,{children:(0,x.jsx)(l.A,{children:(0,x.jsx)(l.A.Body,{children:(0,x.jsxs)(o.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("order_id")}),(0,x.jsx)("th",{children:e("product_name")}),(0,x.jsx)("th",{children:e("customer")}),(0,x.jsx)("th",{children:e("agent")}),(0,x.jsx)("th",{children:e("shares")}),(0,x.jsx)("th",{children:e("storage_cost")}),(0,x.jsx)("th",{children:e("pledge_cost")}),(0,x.jsx)("th",{children:e("status")}),(0,x.jsx)("th",{children:e("created_at")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_orders_available")})}):s.map(e=>{var s,r,a;return(0,x.jsxs)("tr",{children:[(0,x.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,x.jsx)("td",{children:(null===(s=e.products)||void 0===s?void 0:s.name)||"N/A"}),(0,x.jsx)("td",{children:(null===(r=e.customer_profiles)||void 0===r?void 0:r.real_name)||"N/A"}),(0,x.jsx)("td",{children:(null===(a=e.agent_profiles)||void 0===a?void 0:a.brand_name)||"N/A"}),(0,x.jsx)("td",{children:e.shares}),(0,x.jsx)("td",{children:e.storage_cost}),(0,x.jsx)("td",{children:e.pledge_cost}),(0,x.jsx)("td",{children:(0,x.jsx)(c.A,{bg:"approved"===e.review_status?"success":"warning",children:e.review_status})}),(0,x.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id)})})]})})})})})]})}},1072:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...o}=e;const c=(0,n.oU)(r,"row"),i=(0,n.gy)(),f=(0,n.Jm)(),x=`${c}-cols`,m=[];return i.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&m.push(`${x}${a}-${r}`)}),(0,l.jsx)(d,{ref:s,...o,className:t()(a,c,...m)})});o.displayName="Row";const c=o},4063:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:o,className:c,as:i="span",...f}=e;const x=(0,n.oU)(r,"badge");return(0,l.jsx)(i,{ref:s,...f,className:t()(c,x,d&&"rounded-pill",o&&`text-${o}`,a&&`bg-${a}`)})});o.displayName="Badge";const c=o},4196:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:o,borderless:c,hover:i,size:f,variant:x,responsive:m,...h}=e;const u=(0,n.oU)(r,"table"),p=t()(a,u,x&&`${u}-${x}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,o&&`${u}-bordered`,c&&`${u}-borderless`,i&&`${u}-hover`),j=(0,l.jsx)("table",{...h,className:p,ref:s});if(m){let e=`${u}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,l.jsx)("div",{className:e,children:j})}return j});o.displayName="Table";const c=o},8602:(e,s,r)=>{r.d(s,{A:()=>c});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:o,spans:c}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,n.oU)(r,"col");const l=(0,n.gy)(),o=(0,n.Jm)(),c=[],i=[];return l.forEach(e=>{const s=d[e];let a,t,n;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const l=e!==o?`-${e}`:"";a&&c.push(!0===a?`${r}${l}`:`${r}${l}-${a}`),null!=n&&i.push(`order${l}-${n}`),null!=t&&i.push(`offset${l}-${t}`)}),[{...d,className:t()(a,...c,...i)},{as:s,bsPrefix:r,spans:c}]}(e);return(0,l.jsx)(d,{...a,ref:s,className:t()(r,!c.length&&o)})});o.displayName="Col";const c=o},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const o=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,n.oU)(a,"card-body"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const c=o,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,n.oU)(a,"card-footer"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});i.displayName="CardFooter";const f=i;var x=r(1778);const m=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...c}=e;const i=(0,n.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,l.jsx)(x.A.Provider,{value:f,children:(0,l.jsx)(o,{ref:s,...c,className:t()(a,i)})})});m.displayName="CardHeader";const h=m,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:o="img",...c}=e;const i=(0,n.oU)(r,"card-img");return(0,l.jsx)(o,{ref:s,className:t()(d?`${i}-${d}`:i,a),...c})});u.displayName="CardImg";const p=u,j=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...o}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});j.displayName="CardImgOverlay";const N=j,b=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...o}=e;return a=(0,n.oU)(a,"card-link"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});b.displayName="CardLink";const v=b;var g=r(4488);const $=(0,g.A)("h6"),_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=$,...o}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});_.displayName="CardSubtitle";const y=_,w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...o}=e;return a=(0,n.oU)(a,"card-text"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});w.displayName="CardText";const A=w,P=(0,g.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=P,...o}=e;return a=(0,n.oU)(a,"card-title"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...o})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:o,border:i,body:f=!1,children:x,as:m="div",...h}=e;const u=(0,n.oU)(r,"card");return(0,l.jsx)(m,{ref:s,...h,className:t()(a,u,d&&`bg-${d}`,o&&`text-${o}`,i&&`border-${i}`),children:f?(0,l.jsx)(c,{children:x}):x})});C.displayName="Card";const k=Object.assign(C,{Img:p,Title:U,Subtitle:y,Body:c,Link:v,Text:A,Header:h,Footer:f,ImgOverlay:N})}}]);
//# sourceMappingURL=365.7aaa2f05.chunk.js.map