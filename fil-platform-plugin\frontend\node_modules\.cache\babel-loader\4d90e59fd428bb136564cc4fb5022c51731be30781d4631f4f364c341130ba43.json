{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentMemberList=()=>{const{t}=useTranslation();const[members,setMembers]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchMembers=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch customers referred by this agent\nconst{data,error}=await supabase.from('customer_profiles').select(`\n                    user_id,\n                    real_name,\n                    verify_status,\n                    users ( email, created_at )\n                `).eq('agent_id',user.id).order('created_at',{ascending:false});if(error){console.error('Error fetching members:',error);}else{setMembers(data);}setLoading(false);};fetchMembers();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_members')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('my_subordinates')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('user_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('email')}),/*#__PURE__*/_jsx(\"th\",{children:t('real_name')}),/*#__PURE__*/_jsx(\"th\",{children:t('kyc_status')}),/*#__PURE__*/_jsx(\"th\",{children:t('registration_time')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:members.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"5\",className:\"text-center\",children:t('no_subordinates')})}):members.map(member=>{var _member$users,_member$users2;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[member.user_id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:((_member$users=member.users)===null||_member$users===void 0?void 0:_member$users.email)||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:member.real_name||'N/A'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:member.verify_status==='approved'?'success':'warning',children:member.verify_status})}),/*#__PURE__*/_jsx(\"td\",{children:new Date((_member$users2=member.users)===null||_member$users2===void 0?void 0:_member$users2.created_at).toLocaleString()})]},member.user_id);})})]})})})})})]});};export default AgentMemberList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "AgentMemberList", "t", "members", "setMembers", "loading", "setLoading", "fetchMembers", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "member", "_member$users", "_member$users2", "user_id", "substring", "users", "email", "real_name", "bg", "verify_status", "Date", "created_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/agent/AgentMemberList.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst AgentMemberList = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchMembers = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch customers referred by this agent\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .select(`\n                    user_id,\n                    real_name,\n                    verify_status,\n                    users ( email, created_at )\n                `)\n                .eq('agent_id', user.id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching members:', error);\n            } else {\n                setMembers(data);\n            }\n            setLoading(false);\n        };\n\n        fetchMembers();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_subordinates')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('user_id')}</th>\n                                        <th>{t('email')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('kyc_status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {members.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"5\" className=\"text-center\">{t('no_subordinates')}</td>\n                                        </tr>\n                                    ) : (\n                                        members.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.user_id.substring(0, 8)}...</td>\n                                                <td>{member.users?.email || 'N/A'}</td>\n                                                <td>{member.real_name || 'N/A'}</td>\n                                                <td><Badge bg={member.verify_status === 'approved' ? 'success' : 'warning'}>{member.verify_status}</Badge></td>\n                                                <td>{new Date(member.users?.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentMemberList;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,EAAE,CAAC,UAAU,CAAEN,IAAI,CAACO,EAAE,CAAC,CACvBC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIN,KAAK,CAAE,CACPO,OAAO,CAACP,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACnD,CAAC,IAAM,CACHT,UAAU,CAACK,IAAI,CAAC,CACpB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,iBAAiB,CAAC,CAAM,CAAC,CAC5C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAgC,QAAA,eACNvB,IAAA,OAAIwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAChDJ,IAAA,CAACR,GAAG,EAAA+B,QAAA,cACAvB,IAAA,CAACP,GAAG,EAAA8B,QAAA,cACAvB,IAAA,CAACN,IAAI,EAAA6B,QAAA,cACDvB,IAAA,CAACN,IAAI,CAAC+B,IAAI,EAAAF,QAAA,cACNrB,KAAA,CAACP,KAAK,EAAC+B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,cACvBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,EACjC,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,OAAO,CAACyB,MAAM,GAAK,CAAC,cACjB9B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAI+B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEnB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,CACnE,CAAC,CAELC,OAAO,CAAC2B,GAAG,CAACC,MAAM,OAAAC,aAAA,CAAAC,cAAA,oBACdjC,KAAA,OAAAqB,QAAA,eACIrB,KAAA,OAAAqB,QAAA,EAAKU,MAAM,CAACG,OAAO,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cAC5CrC,IAAA,OAAAuB,QAAA,CAAK,EAAAW,aAAA,CAAAD,MAAM,CAACK,KAAK,UAAAJ,aAAA,iBAAZA,aAAA,CAAcK,KAAK,GAAI,KAAK,CAAK,CAAC,cACvCvC,IAAA,OAAAuB,QAAA,CAAKU,MAAM,CAACO,SAAS,EAAI,KAAK,CAAK,CAAC,cACpCxC,IAAA,OAAAuB,QAAA,cAAIvB,IAAA,CAACJ,KAAK,EAAC6C,EAAE,CAAER,MAAM,CAACS,aAAa,GAAK,UAAU,CAAG,SAAS,CAAG,SAAU,CAAAnB,QAAA,CAAEU,MAAM,CAACS,aAAa,CAAQ,CAAC,CAAI,CAAC,cAC/G1C,IAAA,OAAAuB,QAAA,CAAK,GAAI,CAAAoB,IAAI,EAAAR,cAAA,CAACF,MAAM,CAACK,KAAK,UAAAH,cAAA,iBAAZA,cAAA,CAAcS,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GALzDZ,MAAM,CAACG,OAMZ,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAjC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}