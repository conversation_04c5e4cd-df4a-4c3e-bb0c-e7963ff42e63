
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';

const ProductListPage = () => {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchProducts = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data, error } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    effective_delay_days,
                    min_purchase,
                    is_disabled,
                    maker_profiles ( brand_name: user_id, maker_brand: domain ) 
                `)
                .eq('is_disabled', false)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching products:', error);
            } else {
                setProducts(data);
            }
            setLoading(false);
        };

        fetchProducts();
    }, []);

    if (loading) {
        return <div>Loading products...</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">算力产品</h2>
            <Row>
                {products.map(product => (
                    <Col md={4} key={product.id} className="mb-4">
                        <Card className={product.is_disabled ? 'bg-light' : ''}>
                            <Card.Body>
                                <Card.Title className="d-flex justify-content-between">
                                    {product.name}
                                    {product.category === 'spot' ? 
                                        <Badge bg="success">现货</Badge> : 
                                        <Badge bg="primary">期货</Badge>}
                                </Card.Title>
                                <Card.Subtitle className="mb-2 text-muted">由 {product.maker_profiles?.maker_brand || '官方'} 提供</Card.Subtitle>
                                <Card.Text>
                                    <strong>价格:</strong> {product.price} USDT / 份 <br />
                                    <strong>总份额:</strong> {product.total_shares} <br />
                                    <strong>剩余份额:</strong> {product.total_shares - product.sold_shares} <br />
                                    <strong>起购:</strong> {product.min_purchase} 份 <br />
                                    <strong>等待期:</strong> {product.effective_delay_days} 天
                                </Card.Text>
                                <Button variant="primary" disabled={product.is_disabled || (product.total_shares - product.sold_shares === 0)}>
                                    {(product.total_shares - product.sold_shares === 0) ? '已售罄' : '立即购买'}
                                </Button>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>
        </Container>
    );
};

export default ProductListPage;
