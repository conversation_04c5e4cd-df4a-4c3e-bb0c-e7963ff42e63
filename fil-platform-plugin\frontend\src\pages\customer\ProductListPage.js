
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';

const ProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchProducts = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data, error } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    effective_delay_days,
                    min_purchase,
                    is_disabled,
                    maker_profiles ( brand_name: user_id, maker_brand: domain ) 
                `)
                .eq('is_disabled', false)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching products:', error);
            } else {
                setProducts(data);
            }
            setLoading(false);
        };

        fetchProducts();
    }, []);

    if (loading) {
        return <div>{t('loading_products')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('power_products')}</h2>
            <Row>
                {products.map(product => (
                    <Col md={4} key={product.id} className="mb-4">
                        <Card className={product.is_disabled ? 'bg-light' : ''}>
                            <Card.Body>
                                <Card.Title className="d-flex justify-content-between">
                                    {product.name}
                                    {product.category === 'spot' ?
                                        <Badge bg="success">{t('spot_category')}</Badge> :
                                        <Badge bg="primary">{t('futures_category')}</Badge>}
                                </Card.Title>
                                <Card.Subtitle className="mb-2 text-muted">{t('provided_by')} {product.maker_profiles?.maker_brand || t('official')}</Card.Subtitle>
                                <Card.Text>
                                    <strong>{t('price_label')}:</strong> {product.price} {t('usdt_per_share')} <br />
                                    <strong>{t('total_shares_label')}:</strong> {product.total_shares} <br />
                                    <strong>{t('remaining_shares_label')}:</strong> {product.total_shares - product.sold_shares} <br />
                                    <strong>{t('min_purchase_label')}:</strong> {product.min_purchase} {t('shares_unit')} <br />
                                    <strong>{t('waiting_period_label')}:</strong> {product.effective_delay_days} {t('days_unit')}
                                </Card.Text>
                                <Button variant="primary" disabled={product.is_disabled || (product.total_shares - product.sold_shares === 0)}>
                                    {(product.total_shares - product.sold_shares === 0) ? t('sold_out') : t('buy_now')}
                                </Button>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>
        </Container>
    );
};

export default ProductListPage;
