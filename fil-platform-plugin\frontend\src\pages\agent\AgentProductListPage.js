
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const AgentProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchProducts = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data, error } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    is_disabled,
                    maker_profiles ( brand_name )
                `)
                .eq('is_disabled', false) // Only show active products
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching products:', error);
            } else {
                setProducts(data);
            }
            setLoading(false);
        };

        fetchProducts();
    }, []);

    if (loading) {
        return <div>{t('loading_products')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('products_on_sale')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('product_id')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('category')}</th>
                                        <th>{t('price')}</th>
                                        <th>{t('total_shares')}</th>
                                        <th>{t('sold_shares')}</th>
                                        <th>{t('remaining_shares')}</th>
                                        <th>{t('maker')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {products.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_products')}</td>
                                        </tr>
                                    ) : (
                                        products.map(product => (
                                            <tr key={product.id}>
                                                <td>{product.id.substring(0, 8)}...</td>
                                                <td>{product.name}</td>
                                                <td><Badge bg={product.category === 'spot' ? 'success' : 'primary'}>{t(product.category === 'spot' ? 'spot' : 'futures')}</Badge></td>
                                                <td>{product.price}</td>
                                                <td>{product.total_shares}</td>
                                                <td>{product.sold_shares}</td>
                                                <td>{product.total_shares - product.sold_shares}</td>
                                                <td>{product.maker_profiles?.brand_name || 'N/A'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AgentProductListPage;
