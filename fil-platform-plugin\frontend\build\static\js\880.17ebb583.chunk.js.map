{"version": 3, "file": "static/js/880.17ebb583.chunk.js", "mappings": "uOAMA,MAmHA,EAnHmBA,KACf,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAWC,IAAgBJ,EAAAA,EAAAA,UAAS,YA+B3C,IA7BAK,EAAAA,EAAAA,WAAU,KACcC,WAChB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfL,GAAW,GACX,MAAQO,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAR,GAAW,GAIf,MAAM,KAAEO,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,eACLC,OAAO,KACPC,GAAG,UAAWN,EAAKO,IAEpBJ,EACAK,QAAQL,MAAM,yBAA0BA,GAExCd,EAAUU,GAEdP,GAAW,IAGfiB,IACD,IAEClB,EACA,OAAOmB,EAAAA,EAAAA,KAAA,OAAAC,SAAMzB,EAAE,oBA+CnB,OACI0B,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACND,EAAAA,EAAAA,KAAA,MAAII,UAAU,OAAMH,SAAEzB,EAAE,gBACxBwB,EAAAA,EAAAA,KAACK,EAAAA,EAAG,CAAAJ,UACAD,EAAAA,EAAAA,KAACM,EAAAA,EAAG,CAAAL,UACAC,EAAAA,EAAAA,MAACK,EAAAA,EAAI,CAAAN,SAAA,EACDD,EAAAA,EAAAA,KAACO,EAAAA,EAAKC,OAAM,CAAAP,UACRC,EAAAA,EAAAA,MAACO,EAAAA,EAAG,CAACC,QAAQ,OAAOC,iBAAiB,WAAWC,SAAWC,GAAgB7B,EAAa6B,GAAaZ,SAAA,EACjGD,EAAAA,EAAAA,KAACS,EAAAA,EAAIK,KAAI,CAAAb,UACLD,EAAAA,EAAAA,KAACS,EAAAA,EAAIM,KAAI,CAACC,SAAS,WAAUf,SAAEzB,EAAE,iBAErCwB,EAAAA,EAAAA,KAACS,EAAAA,EAAIK,KAAI,CAAAb,UACLD,EAAAA,EAAAA,KAACS,EAAAA,EAAIM,KAAI,CAACC,SAAS,UAASf,SAAEzB,EAAE,gBAEpCwB,EAAAA,EAAAA,KAACS,EAAAA,EAAIK,KAAI,CAAAb,UACLD,EAAAA,EAAAA,KAACS,EAAAA,EAAIM,KAAI,CAACC,SAAS,WAAUf,SAAEzB,EAAE,iBAErCwB,EAAAA,EAAAA,KAACS,EAAAA,EAAIK,KAAI,CAAAb,UACLD,EAAAA,EAAAA,KAACS,EAAAA,EAAIM,KAAI,CAACC,SAAS,WAAUf,SAAEzB,EAAE,sBAI7CwB,EAAAA,EAAAA,KAACO,EAAAA,EAAKU,KAAI,CAAAhB,SAlERiB,MAClB,OAAQnC,GACJ,IAAK,WACD,OACImB,EAAAA,EAAAA,MAACiB,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAtB,SAAA,EACpCD,EAAAA,EAAAA,KAAA,SAAAC,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACID,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,eACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,wBACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,qBACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,oBACPwB,EAAAA,EAAAA,KAAA,MAAAC,SAAKzB,EAAE,qBAGfwB,EAAAA,EAAAA,KAAA,SAAAC,SACuB,IAAlBvB,EAAO8C,QACJxB,EAAAA,EAAAA,KAAA,MAAAC,UACID,EAAAA,EAAAA,KAAA,MAAIyB,QAAQ,IAAIrB,UAAU,cAAaH,SAAEzB,EAAE,iBAG/CE,EAAOgD,IAAIC,IACPzB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACID,EAAAA,EAAAA,KAAA,MAAAC,SAAK0B,EAAMC,iBACX5B,EAAAA,EAAAA,KAAA,MAAAC,SAAK0B,EAAME,qBACX7B,EAAAA,EAAAA,KAAA,MAAAC,SAAK0B,EAAMG,kBACX9B,EAAAA,EAAAA,KAAA,MAAAC,SAAK0B,EAAMI,iBACX/B,EAAAA,EAAAA,KAAA,MAAAC,SAAK0B,EAAMK,oBALNL,EAAMC,qBAYvC,IAAK,UACD,OAAO5B,EAAAA,EAAAA,KAAA,OAAAC,SAAMzB,EAAE,yBACnB,IAAK,WACD,OAAOwB,EAAAA,EAAAA,KAAA,OAAAC,SAAMzB,EAAE,0BACnB,IAAK,WACD,OAAOwB,EAAAA,EAAAA,KAAA,OAAAC,SAAMzB,EAAE,0BACnB,QACI,OAAO,OA2BM0C,e,sFC1G7B,MAAMb,EAAmB4B,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRhC,EAEAiC,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,OACjDM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCnD,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,KACFI,EACHnC,UAAWkD,IAAWlD,EAAWoC,KAAsBO,OAG3D1C,EAAIkD,YAAc,MAClB,S,sFCjCA,MAAMpC,EAAqBc,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRhC,EAAS,QACTgB,EAAO,SACPC,EAAQ,WACRmC,EAAU,MACVlC,EAAK,KACLmC,EAAI,QACJ/C,EAAO,WACPa,KACGgB,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,SACjDW,EAAUO,IAAWlD,EAAWoC,EAAmB9B,GAAW,GAAG8B,KAAqB9B,IAAW+C,GAAQ,GAAGjB,KAAqBiB,IAAQrC,GAAW,GAAGoB,KAAwC,kBAAZpB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGmB,aAA8BgB,GAAc,GAAGhB,eAAgClB,GAAS,GAAGkB,WACxVkB,GAAqB1D,EAAAA,EAAAA,KAAK,QAAS,IACpCuC,EACHnC,UAAW2C,EACXZ,IAAKA,IAEP,GAAIZ,EAAY,CACd,IAAIoC,EAAkB,GAAGnB,eAIzB,MAH0B,kBAAfjB,IACToC,EAAkB,GAAGA,KAAmBpC,MAEtBvB,EAAAA,EAAAA,KAAK,MAAO,CAC9BI,UAAWuD,EACX1D,SAAUyD,GAEd,CACA,OAAOA,IAETvC,EAAMoC,YAAc,QACpB,S,sFCQA,MAAMjD,EAAmB2B,EAAAA,WAEzB,CAACM,EAAOJ,KACN,OAAO,UACL/B,KACGwD,IAEHvB,GAAIC,EAAY,MAAK,SACrBF,EAAQ,MACRyB,IAjDG,SAAe3B,GAKnB,IALoB,GACrBG,EAAE,SACFD,EAAQ,UACRhC,KACGmC,GACJL,EACCE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,OACxC,MAAMM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBgB,EAAQ,GACRd,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIa,EACAC,EACAC,SAHGzB,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCY,OACAC,SACAC,SACEd,GAEJY,EAAOZ,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDa,GAAMD,EAAMR,MAAc,IAATS,EAAgB,GAAG1B,IAAWgB,IAAU,GAAGhB,IAAWgB,KAASU,KACvE,MAATE,GAAejB,EAAQM,KAAK,QAAQD,KAASY,KACnC,MAAVD,GAAgBhB,EAAQM,KAAK,SAASD,KAASW,OAE9C,CAAC,IACHxB,EACHnC,UAAWkD,IAAWlD,KAAcyD,KAAUd,IAC7C,CACDV,KACAD,WACAyB,SAEJ,CAWOI,CAAO1B,GACZ,OAAoBvC,EAAAA,EAAAA,KAAKsC,EAAW,IAC/BsB,EACHzB,IAAKA,EACL/B,UAAWkD,IAAWlD,GAAYyD,EAAMrC,QAAUY,OAGtD9B,EAAIiD,YAAc,MAClB,S,sFC1DA,MAAMW,EAAwBjC,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9C/B,EAAS,SACTgC,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGP2B,EAASX,YAAc,WACvB,UCdMY,EAA0BlC,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChD/B,EAAS,SACTgC,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,gBACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGP4B,EAAWZ,YAAc,aACzB,U,cCZA,MAAMa,EAA0BnC,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRhC,EAEAiC,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMmC,GAAS5B,EAAAA,EAAAA,IAAmBL,EAAU,eACtCkC,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoBrE,EAAAA,EAAAA,KAAKyE,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPrE,UAAuBD,EAAAA,EAAAA,KAAKsC,EAAW,CACrCH,IAAKA,KACFI,EACHnC,UAAWkD,IAAWlD,EAAWiE,SAIvCD,EAAWb,YAAc,aACzB,UCvBMqB,EAAuB3C,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRhC,EAAS,QACTM,EACA2B,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMmC,GAAS5B,EAAAA,EAAAA,IAAmBL,EAAU,YAC5C,OAAoBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAW5C,EAAU,GAAG2D,KAAU3D,IAAY2D,EAAQjE,MAC9DmC,MAGPqC,EAAQrB,YAAc,UACtB,UCjBMsB,EAA8B5C,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpD/B,EAAS,SACTgC,EACAC,GAAIC,EAAY,SACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,qBACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGPsC,EAAetB,YAAc,iBAC7B,UCdMuB,EAAwB7C,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9C/B,EAAS,SACTgC,EACAC,GAAIC,EAAY,OACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGPuC,EAASvB,YAAc,WACvB,U,cCbA,MAAMwB,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BhD,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClD/B,EAAS,SACTgC,EACAC,GAAIC,EAAYyC,KACbxC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,kBACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGP0C,EAAa1B,YAAc,eAC3B,UChBM2B,EAAwBjD,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9C/B,EAAS,SACTgC,EACAC,GAAIC,EAAY,OACbC,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,cACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGP2C,EAAS3B,YAAc,WACvB,UCbM4B,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBnD,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/C/B,EAAS,SACTgC,EACAC,GAAIC,EAAY6C,KACb5C,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,eACpBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,EACL/B,UAAWkD,IAAWlD,EAAWgC,MAC9BG,MAGP6C,EAAU7B,YAAc,YACxB,UCPMhD,EAAoB0B,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRhC,EAAS,GACTiF,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZvF,EAEAoC,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMmC,GAAS5B,EAAAA,EAAAA,IAAmBL,EAAU,QAC5C,OAAoBpC,EAAAA,EAAAA,KAAKsC,EAAW,CAClCH,IAAKA,KACFI,EACHnC,UAAWkD,IAAWlD,EAAWiE,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvGtF,SAAUuF,GAAoBxF,EAAAA,EAAAA,KAAKkE,EAAU,CAC3CjE,SAAUA,IACPA,MAGTM,EAAKgD,YAAc,OACnB,QAAekC,OAAOC,OAAOnF,EAAM,CACjCoF,IAAKf,EACLgB,MAAOR,EACPS,SAAUZ,EACVhE,KAAMiD,EACNnD,KAAM+D,EACNgB,KAAMZ,EACN1E,OAAQ4D,EACR2B,OAAQ5B,EACR6B,WAAYnB,G", "sources": ["pages/customer/WalletPage.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Nav } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst WalletPage = () => {\n    const { t } = useTranslation();\n    const [assets, setAssets] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [activeTab, setActiveTab] = useState('overview'); // overview, deposit, withdraw, exchange\n\n    useEffect(() => {\n        const fetchAssets = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            const { data, error } = await supabase\n                .from('user_assets')\n                .select('*')\n                .eq('user_id', user.id);\n\n            if (error) {\n                console.error('Error fetching assets:', error);\n            } else {\n                setAssets(data);\n            }\n            setLoading(false);\n        };\n\n        fetchAssets();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_wallet')}</div>;\n    }\n\n    const renderContent = () => {\n        switch (activeTab) {\n            case 'overview':\n                return (\n                    <Table striped bordered hover responsive>\n                        <thead>\n                            <tr>\n                                <th>{t('currency')}</th>\n                                <th>{t('available_balance')}</th>\n                                <th>{t('locked_balance')}</th>\n                                <th>{t('total_balance')}</th>\n                                <th>{t('withdrawn')}</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {assets.length === 0 ? (\n                                <tr>\n                                    <td colSpan=\"5\" className=\"text-center\">{t('no_assets')}</td>\n                                </tr>\n                            ) : (\n                                assets.map(asset => (\n                                    <tr key={asset.currency_code}>\n                                        <td>{asset.currency_code}</td>\n                                        <td>{asset.balance_available}</td>\n                                        <td>{asset.balance_locked}</td>\n                                        <td>{asset.balance_total}</td>\n                                        <td>{asset.withdrawn_total}</td>\n                                    </tr>\n                                ))\n                            )}\n                        </tbody>\n                    </Table>\n                );\n            case 'deposit':\n                return <div>{t('deposit_coming_soon')}</div>; // Placeholder for deposit UI\n            case 'withdraw':\n                return <div>{t('withdraw_coming_soon')}</div>; // Placeholder for withdraw UI\n            case 'exchange':\n                return <div>{t('exchange_coming_soon')}</div>; // Placeholder for exchange UI\n            default:\n                return null;\n        }\n    };\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_wallet')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <Nav variant=\"tabs\" defaultActiveKey=\"overview\" onSelect={(selectedKey) => setActiveTab(selectedKey)}>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"overview\">{t('overview')}</Nav.Link>\n                                </Nav.Item>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"deposit\">{t('deposit')}</Nav.Link>\n                                </Nav.Item>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"withdraw\">{t('withdraw')}</Nav.Link>\n                                </Nav.Item>\n                                <Nav.Item>\n                                    <Nav.Link eventKey=\"exchange\">{t('exchange')}</Nav.Link>\n                                </Nav.Item>\n                            </Nav>\n                        </Card.Header>\n                        <Card.Body>\n                            {renderContent()}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default WalletPage;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["WalletPage", "t", "useTranslation", "assets", "setAssets", "useState", "loading", "setLoading", "activeTab", "setActiveTab", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "console", "fetchAssets", "_jsx", "children", "_jsxs", "Container", "className", "Row", "Col", "Card", "Header", "Nav", "variant", "defaultActiveKey", "onSelect", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Link", "eventKey", "Body", "renderContent", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "asset", "currency_code", "balance_available", "balance_locked", "balance_total", "withdrawn_total", "React", "_ref", "ref", "bsPrefix", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "classNames", "displayName", "borderless", "size", "table", "responsiveClass", "colProps", "spans", "span", "offset", "order", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Text", "Footer", "ImgOverlay"], "sourceRoot": ""}