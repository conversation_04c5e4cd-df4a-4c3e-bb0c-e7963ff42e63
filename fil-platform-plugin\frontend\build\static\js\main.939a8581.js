/*! For license information please see main.939a8581.js.LICENSE.txt */
(()=>{var e={182:(e,t,n)=>{"use strict";function r(e){return e&&e.ownerDocument||document}n.d(t,{A:()=>r})},400:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},579:(e,t,n)=>{"use strict";e.exports=n(2799)},865:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(5043),a=n(8062),o=n(8293),i=n(7950);var s=n(579);const l=r.forwardRef((e,t)=>{let{onEnter:n,onEntering:l,onEntered:u,onExit:c,onExiting:d,onExited:f,addEndListener:h,children:p,childRef:g,...m}=e;const v=(0,r.useRef)(null),y=(0,o.A)(v,g),b=e=>{var t;y((t=e)&&"setState"in t?i.findDOMNode(t):null!=t?t:null)},w=e=>t=>{e&&v.current&&e(v.current,t)},k=(0,r.useCallback)(w(n),[n]),_=(0,r.useCallback)(w(l),[l]),S=(0,r.useCallback)(w(u),[u]),x=(0,r.useCallback)(w(c),[c]),E=(0,r.useCallback)(w(d),[d]),C=(0,r.useCallback)(w(f),[f]),O=(0,r.useCallback)(w(h),[h]);return(0,s.jsx)(a.Ay,{ref:t,...m,onEnter:k,onEntered:S,onEntering:_,onExit:x,onExited:C,onExiting:E,addEndListener:O,nodeRef:v,children:"function"===typeof p?(e,t)=>p(e,{...t,ref:b}):r.cloneElement(p,{ref:b})})});l.displayName="TransitionWrapper";const u=l},927:(e,t,n)=>{"use strict";n.d(t,{A:()=>v});var r=n(3818),a=n(5043),o=n(8843),i=n(3539),s=n(9048),l=n(5901),u=n(8466),c=n(5425),d=n(2644),f=n(579);const h=["as","onSelect","activeKey","role","onKeyDown"];const p=()=>{},g=(0,c.sE)("event-key"),m=a.forwardRef((e,t)=>{let{as:n="div",onSelect:d,activeKey:m,role:v,onKeyDown:y}=e,b=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,h);const w=(0,o.A)(),k=(0,a.useRef)(!1),_=(0,a.useContext)(l.A),S=(0,a.useContext)(u.A);let x,E;S&&(v=v||"tablist",m=S.activeKey,x=S.getControlledId,E=S.getControllerId);const C=(0,a.useRef)(null),O=e=>{const t=C.current;if(!t)return null;const n=(0,r.A)(t,`[${g}]:not([aria-disabled=true])`),a=t.querySelector("[aria-selected=true]");if(!a||a!==document.activeElement)return null;const o=n.indexOf(a);if(-1===o)return null;let i=o+e;return i>=n.length&&(i=0),i<0&&(i=n.length-1),n[i]},P=(e,t)=>{null!=e&&(null==d||d(e,t),null==_||_(e,t))};(0,a.useEffect)(()=>{if(C.current&&k.current){const e=C.current.querySelector(`[${g}][aria-selected=true]`);null==e||e.focus()}k.current=!1});const T=(0,i.A)(t,C);return(0,f.jsx)(l.A.Provider,{value:P,children:(0,f.jsx)(s.A.Provider,{value:{role:v,activeKey:(0,l.u)(m),getControlledId:x||p,getControllerId:E||p},children:(0,f.jsx)(n,Object.assign({},b,{onKeyDown:e=>{if(null==y||y(e),!S)return;let t;switch(e.key){case"ArrowLeft":case"ArrowUp":t=O(-1);break;case"ArrowRight":case"ArrowDown":t=O(1);break;default:return}t&&(e.preventDefault(),P(t.dataset[(0,c.y)("EventKey")]||null,e),k.current=!0,w())},ref:T,role:v}))})})});m.displayName="Nav";const v=Object.assign(m,{Item:d.A})},1094:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(3043);const a=function(e,t,n,r){var a=r&&"boolean"!==typeof r?r.capture:r;e.removeEventListener(t,n,a),n.__once&&e.removeEventListener(t,n.__once,a)};const o=function(e,t,n,o){return(0,r.Ay)(e,t,n,o),function(){a(e,t,n,o)}}},1210:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(8829)),o=r(n(5736)),i=n(5745);class s{constructor(e){let{headers:t={},schema:n,fetch:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=e,this.headers=Object.assign(Object.assign({},i.DEFAULT_HEADERS),t),this.schemaName=n,this.fetch=r}from(e){const t=new URL(`${this.url}/${e}`);return new a.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new s(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{head:r=!1,get:a=!1,count:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=new URL(`${this.url}/rpc/${e}`);let l;r||a?(t=r?"HEAD":"GET",Object.entries(n).filter(e=>{let[t,n]=e;return void 0!==n}).map(e=>{let[t,n]=e;return[t,Array.isArray(n)?`{${n.join(",")}}`:`${n}`]}).forEach(e=>{let[t,n]=e;s.searchParams.append(t,n)})):(t="POST",l=n);const u=Object.assign({},this.headers);return i&&(u.Prefer=`count=${i}`),new o.default({method:t,url:s,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}}t.default=s},1283:(e,t,n)=>{"use strict";n.d(t,{BV:()=>be,I9:()=>et,N_:()=>nt,Zp:()=>ne,qh:()=>ve,zy:()=>Z});var r=n(5043),a=(n(4358),"popstate");function o(){return f(function(e,t){let{pathname:n="/",search:r="",hash:a=""}=d(e.location.hash.substring(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),u("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let n=e.document.querySelector("base"),r="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");r=-1===n?t:t.slice(0,n)}return r+"#"+("string"===typeof t?t:c(t))},function(e,t){s("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function i(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function s(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function l(e,t){return{usr:e.state,key:e.key,idx:t}}function u(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return{pathname:"string"===typeof e?e:e.pathname,search:"",hash:"",..."string"===typeof t?d(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function c(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function d(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function f(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,c="POP",d=null,f=p();function p(){return(s.state||{idx:null}).idx}function g(){c="POP";let e=p(),t=null==e?null:e-f;f=e,d&&d({action:c,location:v.location,delta:t})}function m(e){return h(e)}null==f&&(f=0,s.replaceState({...s.state,idx:f},""));let v={get action(){return c},get location(){return e(o,s)},listen(e){if(d)throw new Error("A history only accepts one active listener");return o.addEventListener(a,g),d=e,()=>{o.removeEventListener(a,g),d=null}},createHref:e=>t(o,e),createURL:m,encodeLocation(e){let t=m(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){c="PUSH";let r=u(v.location,e,t);n&&n(r,e),f=p()+1;let a=l(r,f),h=v.createHref(r);try{s.pushState(a,"",h)}catch(g){if(g instanceof DOMException&&"DataCloneError"===g.name)throw g;o.location.assign(h)}i&&d&&d({action:c,location:v.location,delta:1})},replace:function(e,t){c="REPLACE";let r=u(v.location,e,t);n&&n(r,e),f=p();let a=l(r,f),o=v.createHref(r);s.replaceState(a,"",o),i&&d&&d({action:c,location:v.location,delta:0})},go:e=>s.go(e)};return v}function h(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),i(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:c(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function p(e,t){return g(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function g(e,t,n,r){let a=A(("string"===typeof t?d(t):t).pathname||"/",n);if(null==a)return null;let o=m(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(o);let i=null;for(let s=0;null==i&&s<o.length;++s){let e=T(a);i=C(o[s],e,r)}return i}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,o)=>{let s={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};s.relativePath.startsWith("/")&&(i(s.relativePath.startsWith(r),`Absolute route path "${s.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),s.relativePath=s.relativePath.slice(r.length));let l=$([r,s.relativePath]),u=n.concat(s);e.children&&e.children.length>0&&(i(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),m(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:E(l,e.index),routesMeta:u})};return e.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of v(e.path))a(e,t,n);else a(e,t)}),t}function v(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=v(r.join("/")),s=[];return s.push(...i.map(e=>""===e?o:[o,e].join("/"))),a&&s.push(...i),s.map(t=>e.startsWith("/")&&""===t?"/":t)}var y=/^:[\w-]+$/,b=3,w=2,k=1,_=10,S=-2,x=e=>"*"===e;function E(e,t){let n=e.split("/"),r=n.length;return n.some(x)&&(r+=S),t&&(r+=w),n.filter(e=>!x(e)).reduce((e,t)=>e+(y.test(t)?b:""===t?k:_),r)}function C(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},o="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:$([o,c.pathname]),pathnameBase:D($([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=$([o,c.pathnameBase]))}return i}function O(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=P(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];s("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function T(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return s(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function A(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function j(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function R(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function N(e){let t=R(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function L(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=d(e):(r={...e},i(!r.pathname||!r.pathname.includes("?"),j("?","pathname","search",r)),i(!r.pathname||!r.pathname.includes("#"),j("#","pathname","hash",r)),i(!r.search||!r.search.includes("#"),j("#","search","hash",r)));let o,s=""===e||""===r.pathname,l=s?"/":r.pathname;if(null==l)o=n;else{let e=t.length-1;if(!a&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}o=e>=0?t[e]:"/"}let u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?d(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:I(r),hash:z(a)}}(r,o),c=l&&"/"!==l&&l.endsWith("/"),f=(s||"."===l)&&n.endsWith("/");return u.pathname.endsWith("/")||!c&&!f||(u.pathname+="/"),u}var $=e=>e.join("/").replace(/\/\/+/g,"/"),D=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",z=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function M(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var F=["POST","PUT","PATCH","DELETE"],U=(new Set(F),["GET",...F]);new Set(U),Symbol("ResetLoaderData");var B=r.createContext(null);B.displayName="DataRouter";var H=r.createContext(null);H.displayName="DataRouterState";var W=r.createContext({isTransitioning:!1});W.displayName="ViewTransition";var q=r.createContext(new Map);q.displayName="Fetchers";var V=r.createContext(null);V.displayName="Await";var K=r.createContext(null);K.displayName="Navigation";var J=r.createContext(null);J.displayName="Location";var Y=r.createContext({outlet:null,matches:[],isDataRoute:!1});Y.displayName="Route";var G=r.createContext(null);G.displayName="RouteError";var Q=!0;function X(){return null!=r.useContext(J)}function Z(){return i(X(),"useLocation() may be used only in the context of a <Router> component."),r.useContext(J).location}var ee="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function te(e){r.useContext(K).static||r.useLayoutEffect(e)}function ne(){let{isDataRoute:e}=r.useContext(Y);return e?function(){let{router:e}=de("useNavigate"),t=he("useNavigate"),n=r.useRef(!1);te(()=>{n.current=!0});let a=r.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(n.current,ee),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...a}))},[e,t]);return a}():function(){i(X(),"useNavigate() may be used only in the context of a <Router> component.");let e=r.useContext(B),{basename:t,navigator:n}=r.useContext(K),{matches:a}=r.useContext(Y),{pathname:o}=Z(),l=JSON.stringify(N(a)),u=r.useRef(!1);te(()=>{u.current=!0});let c=r.useCallback(function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s(u.current,ee),!u.current)return;if("number"===typeof r)return void n.go(r);let i=L(r,JSON.parse(l),o,"path"===a.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:$([t,i.pathname])),(a.replace?n.replace:n.push)(i,a.state,a)},[t,n,l,o,e]);return c}()}r.createContext(null);function re(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=r.useContext(Y),{pathname:a}=Z(),o=JSON.stringify(N(n));return r.useMemo(()=>L(e,JSON.parse(o),a,"path"===t),[e,o,a,t])}function ae(e,t,n,a){i(X(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=r.useContext(K),{matches:l}=r.useContext(Y),u=l[l.length-1],c=u?u.params:{},f=u?u.pathname:"/",h=u?u.pathnameBase:"/",g=u&&u.route;if(Q){let e=g&&g.path||"";me(f,!g||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${f}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let m,v=Z();if(t){let e="string"===typeof t?d(t):t;i("/"===h||e.pathname?.startsWith(h),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${h}" but pathname "${e.pathname}" was given in the \`location\` prop.`),m=e}else m=v;let y=m.pathname||"/",b=y;if("/"!==h){let e=h.replace(/^\//,"").split("/");b="/"+y.replace(/^\//,"").split("/").slice(e.length).join("/")}let w=p(e,{pathname:b});Q&&(s(g||null!=w,`No routes matched location "${m.pathname}${m.search}${m.hash}" `),s(null==w||void 0!==w[w.length-1].route.element||void 0!==w[w.length-1].route.Component||void 0!==w[w.length-1].route.lazy,`Matched leaf route at location "${m.pathname}${m.search}${m.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`));let k=ue(w&&w.map(e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:$([h,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:$([h,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),l,n,a);return t&&k?r.createElement(J.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...m},navigationType:"POP"}},k):k}function oe(){let e=pe(),t=M(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:a},i={padding:"2px 4px",backgroundColor:a},s=null;return Q&&(console.error("Error handled by React Router default ErrorBoundary:",e),s=r.createElement(r.Fragment,null,r.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),r.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",r.createElement("code",{style:i},"ErrorBoundary")," or"," ",r.createElement("code",{style:i},"errorElement")," prop on your route."))),r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:o},n):null,s)}var ie=r.createElement(oe,null),se=class extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(Y.Provider,{value:this.props.routeContext},r.createElement(G.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function le(e){let{routeContext:t,match:n,children:a}=e,o=r.useContext(B);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(Y.Provider,{value:t},a)}function ue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let a=e,o=n?.errors;if(null!=o){let e=a.findIndex(e=>e.route.id&&void 0!==o?.[e.route.id]);i(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,e+1))}let s=!1,l=-1;if(n)for(let r=0;r<a.length;r++){let e=a[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=r),e.route.id){let{loaderData:t,errors:r}=n,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!r||void 0===r[e.route.id]);if(e.route.lazy||o){s=!0,a=l>=0?a.slice(0,l+1):[a[0]];break}}}return a.reduceRight((e,i,u)=>{let c,d=!1,f=null,h=null;n&&(c=o&&i.route.id?o[i.route.id]:void 0,f=i.route.errorElement||ie,s&&(l<0&&0===u?(me("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,h=null):l===u&&(d=!0,h=i.route.hydrateFallbackElement||null)));let p=t.concat(a.slice(0,u+1)),g=()=>{let t;return t=c?f:d?h:i.route.Component?r.createElement(i.route.Component,null):i.route.element?i.route.element:e,r.createElement(le,{match:i,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(i.route.ErrorBoundary||i.route.errorElement||0===u)?r.createElement(se,{location:n.location,revalidation:n.revalidation,component:f,error:c,children:g(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):g()},null)}function ce(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function de(e){let t=r.useContext(B);return i(t,ce(e)),t}function fe(e){let t=r.useContext(H);return i(t,ce(e)),t}function he(e){let t=function(e){let t=r.useContext(Y);return i(t,ce(e)),t}(e),n=t.matches[t.matches.length-1];return i(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function pe(){let e=r.useContext(G),t=fe("useRouteError"),n=he("useRouteError");return void 0!==e?e:t.errors?.[n]}var ge={};function me(e,t,n){t||ge[e]||(ge[e]=!0,s(!1,n))}r.memo(function(e){let{routes:t,future:n,state:r}=e;return ae(t,void 0,r,n)});function ve(e){i(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ye(e){let{basename:t="/",children:n=null,location:a,navigationType:o="POP",navigator:l,static:u=!1}=e;i(!X(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let c=t.replace(/^\/*/,"/"),f=r.useMemo(()=>({basename:c,navigator:l,static:u,future:{}}),[c,l,u]);"string"===typeof a&&(a=d(a));let{pathname:h="/",search:p="",hash:g="",state:m=null,key:v="default"}=a,y=r.useMemo(()=>{let e=A(h,c);return null==e?null:{location:{pathname:e,search:p,hash:g,state:m,key:v},navigationType:o}},[c,h,p,g,m,v,o]);return s(null!=y,`<Router basename="${c}"> is not able to match the URL "${h}${p}${g}" because it does not start with the basename, so the <Router> won't render anything.`),null==y?null:r.createElement(K.Provider,{value:f},r.createElement(J.Provider,{children:n,value:y}))}function be(e){let{children:t,location:n}=e;return ae(we(t),n)}r.Component;function we(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return r.Children.forEach(e,(e,a)=>{if(!r.isValidElement(e))return;let o=[...t,a];if(e.type===r.Fragment)return void n.push.apply(n,we(e.props.children,o));i(e.type===ve,`[${"string"===typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),i(!e.props.index||!e.props.children,"An index route cannot have child routes.");let s={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(s.children=we(e.props.children,o)),n.push(s)}),n}var ke="get",_e="application/x-www-form-urlencoded";function Se(e){return null!=e&&"string"===typeof e.tagName}var xe=null;var Ee=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ce(e){return null==e||Ee.has(e)?e:(s(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${_e}"`),null)}function Oe(e,t){let n,r,a,o,i;if(Se(s=e)&&"form"===s.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?A(i,t):null,n=e.getAttribute("method")||ke,a=Ce(e.getAttribute("enctype"))||_e,o=new FormData(e)}else if(function(e){return Se(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Se(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(r=s?A(s,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||ke,a=Ce(e.getAttribute("formenctype"))||Ce(i.getAttribute("enctype"))||_e,o=new FormData(i,e),!function(){if(null===xe)try{new FormData(document.createElement("form"),0),xe=!1}catch(e){xe=!0}return xe}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(Se(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ke,r=null,a=_e,i=e}var s;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}function Pe(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Te(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ae(e){return null!=e&&"string"===typeof e.page}function je(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Re(e,t,n,r,a,o){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,s=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===o?t.filter((e,t)=>i(e,t)||s(e,t)):"data"===o?t.filter((t,o)=>{let l=r.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(i(t,o)||s(t,o))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0}):[]}function Ne(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}function Le(e,t){let n=new Set,r=new Set(t);return e.reduce((e,a)=>{if(t&&!Ae(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let o=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(o)||(n.add(o),e.push({key:o,link:a})),e},[])}function $e(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var De=new Set([100,101,204,205]);function Ie(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===A(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}r.Component;function ze(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let a,o=r.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(M(t))return r.createElement(Me,{title:"Unhandled Thrown Response!"},r.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),Q?o:null);if(t instanceof Error)a=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);a=new Error(e)}return r.createElement(Me,{title:"Application Error!",isOutsideRemixApp:n},r.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),o)}function Me(e){let{title:t,renderScripts:n,isOutsideRemixApp:a,children:o}=e,{routeModules:i}=We();return i.root?.Layout&&!a?o:r.createElement("html",{lang:"en"},r.createElement("head",null,r.createElement("meta",{charSet:"utf-8"}),r.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r.createElement("title",null,t)),r.createElement("body",null,r.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},o,n?r.createElement(Qe,null):null)))}function Fe(e,t){return"lazy"===e.mode&&!0===t}function Ue(){let e=r.useContext(B);return Pe(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Be(){let e=r.useContext(H);return Pe(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var He=r.createContext(void 0);function We(){let e=r.useContext(He);return Pe(e,"You must render this element inside a <HydratedRouter> element"),e}function qe(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Ve(e,t,n){if(n&&!Ge)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}function Ke(e){let{page:t,...n}=e,{router:a}=Ue(),o=r.useMemo(()=>p(a.routes,t,a.basename),[a.routes,t,a.basename]);return o?r.createElement(Ye,{page:t,matches:o,...n}):null}function Je(e){let{manifest:t,routeModules:n}=We(),[a,o]=r.useState([]);return r.useEffect(()=>{let r=!1;return async function(e,t,n){return Le((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await Te(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(je).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}(e,t,n).then(e=>{r||o(e)}),()=>{r=!0}},[e,t,n]),a}function Ye(e){let{page:t,matches:n,...a}=e,o=Z(),{manifest:i,routeModules:s}=We(),{basename:l}=Ue(),{loaderData:u,matches:c}=Be(),d=r.useMemo(()=>Re(t,n,c,i,o,"data"),[t,n,c,i,o]),f=r.useMemo(()=>Re(t,n,c,i,o,"assets"),[t,n,c,i,o]),h=r.useMemo(()=>{if(t===o.pathname+o.search+o.hash)return[];let e=new Set,r=!1;if(n.forEach(t=>{let n=i.routes[t.route.id];n&&n.hasLoader&&(!d.some(e=>e.route.id===t.route.id)&&t.route.id in u&&s[t.route.id]?.shouldRevalidate||n.hasClientLoader?r=!0:e.add(t.route.id))}),0===e.size)return[];let a=Ie(t,l);return r&&e.size>0&&a.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[a.pathname+a.search]},[l,u,o,i,d,n,t,s]),p=r.useMemo(()=>Ne(f,i),[f,i]),g=Je(f);return r.createElement(r.Fragment,null,h.map(e=>r.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...a})),p.map(e=>r.createElement("link",{key:e,rel:"modulepreload",href:e,...a})),g.map(e=>{let{key:t,link:n}=e;return r.createElement("link",{key:t,...n})}))}He.displayName="FrameworkContext";var Ge=!1;function Qe(e){let{manifest:t,serverHandoffString:n,isSpaMode:a,renderMeta:o,routeDiscovery:i,ssr:s}=We(),{router:l,static:u,staticContext:c}=Ue(),{matches:d}=Be(),f=Fe(i,s);o&&(o.didRenderScripts=!0);let h=Ve(d,null,a);r.useEffect(()=>{Ge=!0},[]);let g=r.useMemo(()=>{let a=c?`window.__reactRouterContext = ${n};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",o=u?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${f?"":`import ${JSON.stringify(t.url)}`};\n${h.map((e,n)=>{let r=`route${n}`,a=t.routes[e.route.id];Pe(a,`Route ${e.route.id} not found in manifest`);let{clientActionModule:o,clientLoaderModule:i,clientMiddlewareModule:s,hydrateFallbackModule:l,module:u}=a,c=[...o?[{module:o,varName:`${r}_clientAction`}]:[],...i?[{module:i,varName:`${r}_clientLoader`}]:[],...s?[{module:s,varName:`${r}_clientMiddleware`}]:[],...l?[{module:l,varName:`${r}_HydrateFallback`}]:[],{module:u,varName:`${r}_main`}];return 1===c.length?`import * as ${r} from ${JSON.stringify(u)};`:[c.map(e=>`import * as ${e.varName} from "${e.module}";`).join("\n"),`const ${r} = {${c.map(e=>`...${e.varName}`).join(",")}};`].join("\n")}).join("\n")}\n  ${f?`window.__reactRouterManifest = ${JSON.stringify(function(e,t){let{sri:n,...r}=e,a=new Set(t.state.matches.map(e=>e.route.id)),o=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(o.pop();o.length>0;)i.push(`/${o.join("/")}`),o.pop();i.forEach(e=>{let n=p(t.routes,e,t.basename);n&&n.forEach(e=>a.add(e.route.id))});let s=[...a].reduce((e,t)=>Object.assign(e,{[t]:r.routes[t]}),{});return{...r,routes:s,sri:!!n||void 0}}(t,l),null,2)};`:""}\n  window.__reactRouterRouteModules = {${h.map((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`).join(",")}};\n\nimport(${JSON.stringify(t.entry.module)});`:" ";return r.createElement(r.Fragment,null,r.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:$e(a),type:void 0}),r.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:$e(o),type:"module",async:!0}))},[]),m=Ge?[]:(v=t.entry.imports.concat(Ne(h,t,{includeHydrateFallback:!0})),[...new Set(v)]);var v;let y="object"===typeof t.sri?t.sri:{};return Ge?null:r.createElement(r.Fragment,null,"object"===typeof t.sri?r.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:y})}}):null,f?null:r.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:y[t.url],suppressHydrationWarning:!0}),r.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:y[t.entry.module],suppressHydrationWarning:!0}),m.map(t=>r.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:y[t],suppressHydrationWarning:!0})),g)}function Xe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}var Ze="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{Ze&&(window.__reactRouterVersion="7.6.3")}catch(ut){}function et(e){let{basename:t,children:n,window:a}=e,i=r.useRef();null==i.current&&(i.current=o({window:a,v5Compat:!0}));let s=i.current,[l,u]=r.useState({action:s.action,location:s.location}),c=r.useCallback(e=>{r.startTransition(()=>u(e))},[u]);return r.useLayoutEffect(()=>s.listen(c),[s,c]),r.createElement(ye,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s})}var tt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,nt=r.forwardRef(function(e,t){let n,{onClick:a,discover:o="render",prefetch:l="none",relative:u,reloadDocument:d,replace:f,state:h,target:p,to:g,preventScrollReset:m,viewTransition:v,...y}=e,{basename:b}=r.useContext(K),w="string"===typeof g&&tt.test(g),k=!1;if("string"===typeof g&&w&&(n=g,Ze))try{let e=new URL(window.location.href),t=g.startsWith("//")?new URL(e.protocol+g):new URL(g),n=A(t.pathname,b);t.origin===e.origin&&null!=n?g=n+t.search+t.hash:k=!0}catch(ut){s(!1,`<Link to="${g}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let _=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};i(X(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:a}=r.useContext(K),{hash:o,pathname:s,search:l}=re(e,{relative:t}),u=s;return"/"!==n&&(u="/"===s?n:$([n,s])),a.createHref({pathname:u,search:l,hash:o})}(g,{relative:u}),[S,x,E]=function(e,t){let n=r.useContext(He),[a,o]=r.useState(!1),[i,s]=r.useState(!1),{onFocus:l,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,h=r.useRef(null);r.useEffect(()=>{if("render"===e&&s(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{s(e.isIntersecting)})},{threshold:.5});return h.current&&e.observe(h.current),()=>{e.disconnect()}}},[e]),r.useEffect(()=>{if(a){let e=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(e)}}},[a]);let p=()=>{o(!0)},g=()=>{o(!1),s(!1)};return n?"intent"!==e?[i,h,{}]:[i,h,{onFocus:qe(l,p),onBlur:qe(u,g),onMouseEnter:qe(c,p),onMouseLeave:qe(d,g),onTouchStart:qe(f,p)}]:[!1,h,{}]}(l,y),C=function(e){let{target:t,replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=ne(),u=Z(),d=re(e,{relative:i});return r.useCallback(r=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(r,t)){r.preventDefault();let t=void 0!==n?n:c(u)===c(d);l(e,{replace:t,state:a,preventScrollReset:o,relative:i,viewTransition:s})}},[u,l,d,n,a,t,e,o,i,s])}(g,{replace:f,state:h,target:p,preventScrollReset:m,relative:u,viewTransition:v});let O=r.createElement("a",{...y,...E,href:n||_,onClick:k||d?a:function(e){a&&a(e),e.defaultPrevented||C(e)},ref:Xe(t,x),target:p,"data-discover":w||"render"!==o?void 0:"true"});return S&&!w?r.createElement(r.Fragment,null,O,r.createElement(Ke,{page:_})):O});nt.displayName="Link",r.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:a=!1,className:o="",end:s=!1,style:l,to:u,viewTransition:c,children:d,...f}=e,h=re(u,{relative:f.relative}),p=Z(),g=r.useContext(H),{navigator:m,basename:v}=r.useContext(K),y=null!=g&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.useContext(W);i(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=ot("useViewTransitionState"),o=re(e,{relative:t.relative});if(!n.isTransitioning)return!1;let s=A(n.currentLocation.pathname,a)||n.currentLocation.pathname,l=A(n.nextLocation.pathname,a)||n.nextLocation.pathname;return null!=O(o.pathname,l)||null!=O(o.pathname,s)}(h)&&!0===c,b=m.encodeLocation?m.encodeLocation(h).pathname:h.pathname,w=p.pathname,k=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;a||(w=w.toLowerCase(),k=k?k.toLowerCase():null,b=b.toLowerCase()),k&&v&&(k=A(k,v)||k);const _="/"!==b&&b.endsWith("/")?b.length-1:b.length;let S,x=w===b||!s&&w.startsWith(b)&&"/"===w.charAt(_),E=null!=k&&(k===b||!s&&k.startsWith(b)&&"/"===k.charAt(b.length)),C={isActive:x,isPending:E,isTransitioning:y},P=x?n:void 0;S="function"===typeof o?o(C):[o,x?"active":null,E?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let T="function"===typeof l?l(C):l;return r.createElement(nt,{...f,"aria-current":P,className:S,ref:t,style:T,to:u,viewTransition:c},"function"===typeof d?d(C):d)}).displayName="NavLink";var rt=r.forwardRef((e,t)=>{let{discover:n="render",fetcherKey:a,navigate:o,reloadDocument:s,replace:l,state:u,method:d=ke,action:f,onSubmit:h,relative:p,preventScrollReset:g,viewTransition:m,...v}=e,y=lt(),b=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=r.useContext(K),a=r.useContext(Y);i(a,"useFormAction must be used inside a RouteContext");let[o]=a.matches.slice(-1),s={...re(e||".",{relative:t})},l=Z();if(null==e){s.search=l.search;let e=new URLSearchParams(s.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();s.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(s.pathname="/"===s.pathname?n:$([n,s.pathname]));return c(s)}(f,{relative:p}),w="get"===d.toLowerCase()?"get":"post",k="string"===typeof f&&tt.test(f);return r.createElement("form",{ref:t,method:w,action:b,onSubmit:s?h:e=>{if(h&&h(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=t?.getAttribute("formmethod")||d;y(t||e.currentTarget,{fetcherKey:a,method:n,navigate:o,replace:l,state:u,relative:p,preventScrollReset:g,viewTransition:m})},...v,"data-discover":k||"render"!==n?void 0:"true"})});function at(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ot(e){let t=r.useContext(B);return i(t,at(e)),t}rt.displayName="Form";var it=0,st=()=>`__${String(++it)}__`;function lt(){let{router:e}=ot("useSubmit"),{basename:t}=r.useContext(K),n=he("useRouteId");return r.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:o,method:i,encType:s,formData:l,body:u}=Oe(r,t);if(!1===a.navigate){let t=a.fetcherKey||st();await e.fetch(t,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:l,body:u,formMethod:a.method||i,formEncType:a.encType||s,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:l,body:u,formMethod:a.method||i,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}},1497:(e,t,n)=>{"use strict";var r=n(3218);function a(){}function o(){}o.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,o,i){if(i!==r){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:a};return n.PropTypes=n,n}},1778:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});const r=n(5043).createContext(null);r.displayName="CardHeaderContext";const a=r},1844:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},1969:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{Zw:()=>l});var a=n(8587),o=n(5043);n(2740);function i(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function s(e){var t=function(e,t){if("object"!==typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===typeof t?t:String(t)}function l(e,t){return Object.keys(t).reduce(function(n,l){var u,c=n,d=c[i(l)],f=c[l],h=(0,a.A)(c,[i(l),l].map(s)),p=t[l],g=function(e,t,n){var r=(0,o.useRef)(void 0!==e),a=(0,o.useState)(t),i=a[0],s=a[1],l=void 0!==e,u=r.current;return r.current=l,!l&&u&&i!==t&&s(t),[l?e:i,(0,o.useCallback)(function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];n&&n.apply(void 0,[e].concat(r)),s(e)},[n])]}(f,d,e[p]),m=g[0],v=g[1];return r({},h,((u={})[l]=m,u[p]=v,u))},e)}function u(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!==e&&void 0!==e&&this.setState(e)}function c(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!==n&&void 0!==n?n:null}.bind(this))}function d(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}u.__suppressDeprecationWarning=!0,c.__suppressDeprecationWarning=!0,d.__suppressDeprecationWarning=!0},2611:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class n extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=n},2643:(e,t,n)=>{"use strict";function r(e){e.offsetHeight}n.d(t,{A:()=>r})},2644:(e,t,n)=>{"use strict";n.d(t,{A:()=>p,M:()=>f});var r=n(5043),a=n(8894),o=n(9048),i=n(5901),s=n(4140),l=n(5425),u=n(8466),c=n(579);const d=["as","active","eventKey"];function f(e){let{key:t,onClick:n,active:s,id:c,role:d,disabled:f}=e;const h=(0,r.useContext)(i.A),p=(0,r.useContext)(o.A),g=(0,r.useContext)(u.A);let m=s;const v={role:d};if(p){d||"tablist"!==p.role||(v.role="tab");const e=p.getControllerId(null!=t?t:null),n=p.getControlledId(null!=t?t:null);v[(0,l.sE)("event-key")]=t,v.id=e||c,m=null==s&&null!=t?p.activeKey===t:s,!m&&(null!=g&&g.unmountOnExit||null!=g&&g.mountOnEnter)||(v["aria-controls"]=n)}return"tab"===v.role&&(v["aria-selected"]=m,m||(v.tabIndex=-1),f&&(v.tabIndex=-1,v["aria-disabled"]=!0)),v.onClick=(0,a.A)(e=>{f||(null==n||n(e),null!=t&&h&&!e.isPropagationStopped()&&h(t,e))}),[v,{isActive:m}]}const h=r.forwardRef((e,t)=>{let{as:n=s.Ay,active:r,eventKey:a}=e,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,d);const[u,h]=f(Object.assign({key:(0,i.u)(a,o.href),active:r},o));return u[(0,l.sE)("active")]=h.isActive,(0,c.jsx)(n,Object.assign({},o,u,{ref:t}))});h.displayName="NavItem";const p=h},2665:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043);function a(){const e=(0,r.useRef)(!0),t=(0,r.useRef)(()=>e.current);return(0,r.useEffect)(()=>(e.current=!0,()=>{e.current=!1}),[]),t.current}},2677:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const a="undefined"!==typeof n.g&&n.g.navigator&&"ReactNative"===n.g.navigator.product,o="undefined"!==typeof document||a?r.useLayoutEffect:r.useEffect},2740:e=>{"use strict";e.exports=function(e,t,n,r,a,o,i,s){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,r,a,o,i,s],c=0;(l=new Error(t.replace(/%s/g,function(){return u[c++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}}},2799:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var o in r={},t)"key"!==o&&(r[o]=t[o]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},3043:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>s});var r=n(8279),a=!1,o=!1;try{var i={get passive(){return a=!0},get once(){return o=a=!0}};r.A&&(window.addEventListener("test",i,i),window.removeEventListener("test",i,!0))}catch(l){}const s=function(e,t,n,r){if(r&&"boolean"!==typeof r&&!o){var i=r.once,s=r.capture,l=n;!o&&i&&(l=n.__once||function e(r){this.removeEventListener(t,e,s),n.call(this,r)},n.__once=l),e.addEventListener(t,l,a?r:s)}e.addEventListener(t,n,r)}},3218:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3519:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(8139),a=n.n(r),o=n(5043),i=n(7852),s=n(579);const l=o.forwardRef((e,t)=>{let{bsPrefix:n,fluid:r=!1,as:o="div",className:l,...u}=e;const c=(0,i.oU)(n,"container"),d="string"===typeof r?`-${r}`:"-fluid";return(0,s.jsx)(o,{ref:t,...u,className:a()(l,r?`${c}${d}`:c)})});l.displayName="Container";const u=l},3539:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const a=e=>e&&"function"!==typeof e?t=>{e.current=t}:e;const o=function(e,t){return(0,r.useMemo)(()=>function(e,t){const n=a(e),r=a(t);return e=>{n&&n(e),r&&r(e)}}(e,t),[e,t])}},3818:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=Function.prototype.bind.call(Function.prototype.call,[].slice);function a(e,t){return r(e.querySelectorAll(t))}},4117:(e,t,n)=>{"use strict";n.d(t,{r9:()=>g,Bd:()=>b});var r=n(5043);n(1844);Object.create(null);const a={},o=(e,t,n,r)=>{u(n)&&a[n]||(u(n)&&(a[n]=new Date),((e,t,n,r)=>{const a=[n,{code:t,...r||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(a,"warn","react-i18next::",!0);u(a[0])&&(a[0]=`react-i18next:: ${a[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...a):console?.warn&&console.warn(...a)})(e,t,n,r))},i=(e,t)=>()=>{if(e.isInitialized)t();else{const n=()=>{setTimeout(()=>{e.off("initialized",n)},0),t()};e.on("initialized",n)}},s=(e,t,n)=>{e.loadNamespaces(t,i(e,n))},l=(e,t,n,r)=>{if(u(n)&&(n=[n]),e.options.preload&&e.options.preload.indexOf(t)>-1)return s(e,n,r);n.forEach(t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)}),e.loadLanguages(t,i(e,r))},u=e=>"string"===typeof e,c=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,d={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},f=e=>d[e];let h={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(c,f)};let p;const g={type:"3rdParty",init(e){!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h={...h,...e}}(e.options.react),(e=>{p=e})(e)}},m=(0,r.createContext)();class v{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const y=(e,t,n,r)=>e.getFixedT(t,n,r),b=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:n}=t,{i18n:a,defaultNS:i}=(0,r.useContext)(m)||{},c=n||a||p;if(c&&!c.reportNamespaces&&(c.reportNamespaces=new v),!c){o(c,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>{return u(t)?t:"object"===typeof(n=t)&&null!==n&&u(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e;var n},t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}c.options.react?.wait&&o(c,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const d={...h,...c.options.react,...t},{useSuspense:f,keyPrefix:g}=d;let b=e||i||c.options?.defaultNS;b=u(b)?[b]:b||["translation"],c.reportNamespaces.addUsedNamespaces?.(b);const w=(c.isInitialized||c.initializedStoreOnce)&&b.every(e=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:n.lng,precheck:(t,r)=>{if(n.bindI18n?.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e))return!1}}):(o(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0)}(e,c,d)),k=((e,t,n,a)=>(0,r.useCallback)(y(e,t,n,a),[e,t,n,a]))(c,t.lng||null,"fallback"===d.nsMode?b:b[0],g),_=()=>k,S=()=>y(c,t.lng||null,"fallback"===d.nsMode?b:b[0],g),[x,E]=(0,r.useState)(_);let C=b.join();t.lng&&(C=`${t.lng}${C}`);const O=((e,t)=>{const n=(0,r.useRef)();return(0,r.useEffect)(()=>{n.current=t?n.current:e},[e,t]),n.current})(C),P=(0,r.useRef)(!0);(0,r.useEffect)(()=>{const{bindI18n:e,bindI18nStore:n}=d;P.current=!0,w||f||(t.lng?l(c,t.lng,b,()=>{P.current&&E(S)}):s(c,b,()=>{P.current&&E(S)})),w&&O&&O!==C&&P.current&&E(S);const r=()=>{P.current&&E(S)};return e&&c?.on(e,r),n&&c?.store.on(n,r),()=>{P.current=!1,c&&e?.split(" ").forEach(e=>c.off(e,r)),n&&c&&n.split(" ").forEach(e=>c.store.off(e,r))}},[c,C]),(0,r.useEffect)(()=>{P.current&&w&&E(_)},[c,g,w]);const T=[x,c,w];if(T.t=x,T.i18n=c,T.ready=w,w)return T;if(!w&&!f)return T;throw new Promise(e=>{t.lng?l(c,t.lng,b,()=>e()):s(c,b,()=>e())})}},4140:(e,t,n)=>{"use strict";n.d(t,{Am:()=>i,Ay:()=>l});var r=n(5043),a=n(579);const o=["as","disabled"];function i(e){let{tagName:t,disabled:n,href:r,target:a,rel:o,role:i,onClick:s,tabIndex:l=0,type:u}=e;t||(t=null!=r||null!=a||null!=o?"a":"button");const c={tagName:t};if("button"===t)return[{type:u||"button",disabled:n},c];const d=e=>{(n||"a"===t&&function(e){return!e||"#"===e.trim()}(r))&&e.preventDefault(),n?e.stopPropagation():null==s||s(e)};return"a"===t&&(r||(r="#"),n&&(r=void 0)),[{role:null!=i?i:"button",disabled:void 0,tabIndex:n?void 0:l,href:r,target:"a"===t?a:void 0,"aria-disabled":n||void 0,rel:"a"===t?o:void 0,onClick:d,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),d(e))}},c]}const s=r.forwardRef((e,t)=>{let{as:n,disabled:r}=e,s=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,o);const[l,{tagName:u}]=i(Object.assign({tagName:n,disabled:r},s));return(0,a.jsx)(u,Object.assign({},s,l,{ref:t}))});s.displayName="Button";const l=s},4282:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r=n(8139),a=n.n(r),o=n(5043),i=n(4140),s=n(7852),l=n(579);const u=o.forwardRef((e,t)=>{let{as:n,bsPrefix:r,variant:o="primary",size:u,active:c=!1,disabled:d=!1,className:f,...h}=e;const p=(0,s.oU)(r,"btn"),[g,{tagName:m}]=(0,i.Am)({tagName:n,disabled:d,...h}),v=m;return(0,l.jsx)(v,{...g,...h,ref:t,disabled:d,className:a()(f,p,c&&"active",o&&`${p}-${o}`,u&&`${p}-${u}`,h.href&&d&&"disabled")})});u.displayName="Button";const c=u},4288:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,m={};function v(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,g(w,v.prototype),w.isPureReactComponent=!0;var k=Array.isArray,_={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function x(e,t,r,a,o,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(){}function T(e,t,a,o,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,u,c=!1;if(null===e)c=!0;else switch(s){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return T((c=e._init)(e._payload),t,a,o,i)}}if(c)return i=i(e),c=""===o?"."+O(e,0):o,k(i)?(a="",null!=c&&(a=c.replace(C,"$&/")+"/"),T(i,t,a,"",function(e){return e})):null!=i&&(E(i)&&(l=i,u=a+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(C,"$&/")+"/")+c,i=x(l.type,u,void 0,0,0,l.props)),t.push(i)),1;c=0;var d,p=""===o?".":o+":";if(k(e))for(var g=0;g<e.length;g++)c+=T(o=e[g],t,a,s=p+O(o,g),i);else if("function"===typeof(g=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=h&&d[h]||d["@@iterator"])?d:null))for(e=g.call(e),g=0;!(o=e.next()).done;)c+=T(o=o.value,t,a,s=p+O(o,g++),i);else if("object"===s){if("function"===typeof e.then)return T(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,o,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function A(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",function(e){return t.call(n,e,a++)}),r}function j(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function N(){}t.Children={map:A,forEach:function(e,t,n){A(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return A(e,function(){t++}),t},toArray:function(e){return A(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return _.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=g({},e.props),a=e.key;if(null!=t)for(o in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,o)||"key"===o||"__self"===o||"__source"===o||"ref"===o&&void 0===t.ref||(r[o]=t[o]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var i=Array(o),s=0;s<o;s++)i[s]=arguments[s+2];r.children=i}return x(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,a={},o=null;if(null!=t)for(r in void 0!==t.key&&(o=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var i=arguments.length-2;if(1===i)a.children=n;else if(1<i){for(var s=Array(i),l=0;l<i;l++)s[l]=arguments[l+2];a.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===a[r]&&(a[r]=i[r]);return x(e,o,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:j}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=_.T,n={};_.T=n;try{var r=e(),a=_.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(N,R)}catch(o){R(o)}finally{_.T=t}},t.unstable_useCacheRefresh=function(){return _.H.useCacheRefresh()},t.use=function(e){return _.H.use(e)},t.useActionState=function(e,t,n){return _.H.useActionState(e,t,n)},t.useCallback=function(e,t){return _.H.useCallback(e,t)},t.useContext=function(e){return _.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return _.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=_.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return _.H.useId()},t.useImperativeHandle=function(e,t,n){return _.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return _.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return _.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return _.H.useMemo(e,t)},t.useOptimistic=function(e,t){return _.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return _.H.useReducer(e,t,n)},t.useRef=function(e){return _.H.useRef(e)},t.useState=function(e){return _.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return _.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return _.H.useTransition()},t.version="19.1.0"},4312:(e,t,n)=>{"use strict";n.d(t,{b:()=>Vt,F:()=>qt});class r extends Error{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"FunctionsError",n=arguments.length>2?arguments[2]:void 0;super(e),this.name=t,this.context=n}}class a extends r{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class o extends r{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class i extends r{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var s;!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(s||(s={}));var l=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};class u{constructor(e){let{headers:t={},customFetch:r,region:a=s.Any}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=e,this.headers=t,this.region=a,this.fetch=(e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,4630)).then(e=>{let{default:n}=e;return n(...t)})}:fetch),function(){return t(...arguments)}})(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n;return l(this,void 0,void 0,function*(){try{const{headers:r,method:s,body:l}=t;let u={},{region:c}=t;c||(c=this.region);const d=new URL(`${this.url}/${e}`);let f;c&&"any"!==c&&(u["x-region"]=c,d.searchParams.set("forceFunctionRegion",c)),l&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!==typeof Blob&&l instanceof Blob||l instanceof ArrayBuffer?(u["Content-Type"]="application/octet-stream",f=l):"string"===typeof l?(u["Content-Type"]="text/plain",f=l):"undefined"!==typeof FormData&&l instanceof FormData?f=l:(u["Content-Type"]="application/json",f=JSON.stringify(l)));const h=yield this.fetch(d.toString(),{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},u),this.headers),r),body:f}).catch(e=>{throw new a(e)}),p=h.headers.get("x-relay-error");if(p&&"true"===p)throw new o(h);if(!h.ok)throw new i(h);let g,m=(null!==(n=h.headers.get("Content-Type"))&&void 0!==n?n:"text/plain").split(";")[0].trim();return g="application/json"===m?yield h.json():"application/octet-stream"===m?yield h.blob():"text/event-stream"===m?h:"multipart/form-data"===m?yield h.formData():yield h.text(),{data:g,error:null,response:h}}catch(r){return{data:null,error:r,response:r instanceof i||r instanceof o?r.context:void 0}}})}}var c=n(7980);const{PostgrestClient:d,PostgrestQueryBuilder:f,PostgrestFilterBuilder:h,PostgrestTransformBuilder:p,PostgrestBuilder:g,PostgrestError:m}=c;const v=function(){if("undefined"!==typeof WebSocket)return WebSocket;if("undefined"!==typeof global.WebSocket)return global.WebSocket;if("undefined"!==typeof window.WebSocket)return window.WebSocket;if("undefined"!==typeof self.WebSocket)return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}();var y,b,w,k,_,S;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(y||(y={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(b||(b={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(w||(w={})),function(e){e.websocket="websocket"}(k||(k={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(_||(_={}));class x{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"===typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),n=new TextDecoder;return this._decodeBroadcast(e,t,n)}_decodeBroadcast(e,t,n){const r=t.getUint8(1),a=t.getUint8(2);let o=this.HEADER_LENGTH+2;const i=n.decode(e.slice(o,o+r));o+=r;const s=n.decode(e.slice(o,o+a));o+=a;return{ref:null,topic:i,event:s,payload:JSON.parse(n.decode(e.slice(o,e.byteLength)))}}}class E{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(S||(S={}));const C=function(e,t){var n;const r=null!==(n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipTypes)&&void 0!==n?n:[];return Object.keys(t).reduce((n,a)=>(n[a]=O(a,e,t,r),n),{})},O=(e,t,n,r)=>{const a=t.find(t=>t.name===e),o=null===a||void 0===a?void 0:a.type,i=n[e];return o&&!r.includes(o)?P(o,i):T(i)},P=(e,t)=>{if("_"===e.charAt(0)){const n=e.slice(1,e.length);return N(t,n)}switch(e){case S.bool:return A(t);case S.float4:case S.float8:case S.int2:case S.int4:case S.int8:case S.numeric:case S.oid:return j(t);case S.json:case S.jsonb:return R(t);case S.timestamp:return L(t);case S.abstime:case S.date:case S.daterange:case S.int4range:case S.int8range:case S.money:case S.reltime:case S.text:case S.time:case S.timestamptz:case S.timetz:case S.tsrange:case S.tstzrange:default:return T(t)}},T=e=>e,A=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},j=e=>{if("string"===typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},R=e=>{if("string"===typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},N=(e,t)=>{if("string"!==typeof e)return e;const n=e.length-1,r=e[n];if("{"===e[0]&&"}"===r){let r;const o=e.slice(1,n);try{r=JSON.parse("["+o+"]")}catch(a){r=o?o.split(","):[]}return r.map(e=>P(t,e))}return e},L=e=>"string"===typeof e?e.replace(" ","T"):e,$=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class D{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e4;this.channel=e,this.event=t,this.payload=n,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var n;return this._hasReceived(e)&&t(null===(n=this.receivedResp)||void 0===n?void 0:n.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive(e){let{status:t,response:n}=e;this.recHooks.filter(e=>e.status===t).forEach(e=>e.callback(n))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var I,z,M,F;!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(I||(I={}));class U{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const n=(null===t||void 0===t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(n.state,{},e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=U.syncState(this.state,e,t,n),this.pendingDiffs.forEach(e=>{this.state=U.syncDiff(this.state,e,t,n)}),this.pendingDiffs=[],r()}),this.channel._on(n.diff,{},e=>{const{onJoin:t,onLeave:n,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=U.syncDiff(this.state,e,t,n),r())}),this.onJoin((e,t,n)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:n})}),this.onLeave((e,t,n)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:n})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,n,r){const a=this.cloneDeep(e),o=this.transformState(t),i={},s={};return this.map(a,(e,t)=>{o[e]||(s[e]=t)}),this.map(o,(e,t)=>{const n=a[e];if(n){const r=t.map(e=>e.presence_ref),a=n.map(e=>e.presence_ref),o=t.filter(e=>a.indexOf(e.presence_ref)<0),l=n.filter(e=>r.indexOf(e.presence_ref)<0);o.length>0&&(i[e]=o),l.length>0&&(s[e]=l)}else i[e]=t}),this.syncDiff(a,{joins:i,leaves:s},n,r)}static syncDiff(e,t,n,r){const{joins:a,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return n||(n=()=>{}),r||(r=()=>{}),this.map(a,(t,r)=>{var a;const o=null!==(a=e[t])&&void 0!==a?a:[];if(e[t]=this.cloneDeep(r),o.length>0){const n=e[t].map(e=>e.presence_ref),r=o.filter(e=>n.indexOf(e.presence_ref)<0);e[t].unshift(...r)}n(t,o,r)}),this.map(o,(t,n)=>{let a=e[t];if(!a)return;const o=n.map(e=>e.presence_ref);a=a.filter(e=>o.indexOf(e.presence_ref)<0),e[t]=a,r(t,a,n),0===a.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(n=>t(n,e[n]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,n)=>{const r=e[n];return t[n]="metas"in r?r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(z||(z={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(M||(M={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(F||(F={}));class B{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}},n=arguments.length>2?arguments[2]:void 0;this.topic=e,this.params=t,this.socket=n,this.bindings={},this.state=b.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new D(this,w.join,this.params,this.timeout),this.rejoinTimer=new E(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=b.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=b.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=b.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=b.errored,this.rejoinTimer.scheduleTimeout())}),this._on(w.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new U(this),this.broadcastEndpointURL=$(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.timeout;var n,r;if(this.socket.isConnected()||this.socket.connect(),this.state==b.closed){const{config:{broadcast:a,presence:o,private:i}}=this.params;this._onError(t=>null===e||void 0===e?void 0:e(F.CHANNEL_ERROR,t)),this._onClose(()=>null===e||void 0===e?void 0:e(F.CLOSED));const s={},l={broadcast:a,presence:o,postgres_changes:null!==(r=null===(n=this.bindings.postgres_changes)||void 0===n?void 0:n.map(e=>e.filter))&&void 0!==r?r:[],private:i};this.socket.accessTokenValue&&(s.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},s)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async t=>{let{postgres_changes:n}=t;var r;if(this.socket.setAuth(),void 0!==n){const t=this.bindings.postgres_changes,a=null!==(r=null===t||void 0===t?void 0:t.length)&&void 0!==r?r:0,o=[];for(let r=0;r<a;r++){const a=t[r],{filter:{event:i,schema:s,table:l,filter:u}}=a,c=n&&n[r];if(!c||c.event!==i||c.schema!==s||c.table!==l||c.filter!==u)return this.unsubscribe(),this.state=b.errored,void(null===e||void 0===e||e(F.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));o.push(Object.assign(Object.assign({},a),{id:c.id}))}return this.bindings.postgres_changes=o,void(e&&e(F.SUBSCRIBED))}null===e||void 0===e||e(F.SUBSCRIBED)}).receive("error",t=>{this.state=b.errored,null===e||void 0===e||e(F.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null===e||void 0===e||e(F.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return await this.send({type:"presence",event:"untrack"},e)}on(e,t,n){return this._on(e,t,n)}async send(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(n=>{var r,a,o;const i=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(o=null===(a=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===a?void 0:a.broadcast)||void 0===o?void 0:o.ack)||n("ok"),i.receive("ok",()=>n("ok")),i.receive("error",()=>n("error")),i.receive("timeout",()=>n("timed out"))});{const{event:o,payload:i}=e,s={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:i,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,s,null!==(n=t.timeout)&&void 0!==n?n:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(a){return"AbortError"===a.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this.state=b.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(w.close,"leave",this._joinRef())};this.joinPush.destroy();let n=null;return new Promise(r=>{n=new D(this,w.leave,{},e),n.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),n.send(),this._canPush()||n.trigger("ok",{})}).finally(()=>{null===n||void 0===n||n.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,n){const r=new AbortController,a=setTimeout(()=>r.abort(),n),o=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(a),o}_push(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.timeout;if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new D(this,e,t,n);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,n){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,n){var r,a;const o=e.toLocaleLowerCase(),{close:i,error:s,leave:l,join:u}=w;if(n&&[i,s,l,u].indexOf(o)>=0&&n!==this._joinRef())return;let c=this._onMessage(o,t,n);if(t&&!c)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter(e=>{var t,n,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(n=e.filter)||void 0===n?void 0:n.event)||void 0===r?void 0:r.toLocaleLowerCase())===o}).map(e=>e.callback(c,n)):null===(a=this.bindings[o])||void 0===a||a.filter(e=>{var n,r,a,i,s,l;if(["broadcast","presence","postgres_changes"].includes(o)){if("id"in e){const o=e.id,i=null===(n=e.filter)||void 0===n?void 0:n.event;return o&&(null===(r=t.ids)||void 0===r?void 0:r.includes(o))&&("*"===i||(null===i||void 0===i?void 0:i.toLocaleLowerCase())===(null===(a=t.data)||void 0===a?void 0:a.type.toLocaleLowerCase()))}{const n=null===(s=null===(i=null===e||void 0===e?void 0:e.filter)||void 0===i?void 0:i.event)||void 0===s?void 0:s.toLocaleLowerCase();return"*"===n||n===(null===(l=null===t||void 0===t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===o}).map(e=>{if("object"===typeof c&&"ids"in c){const e=c.data,{schema:t,table:n,commit_timestamp:r,type:a,errors:o}=e,i={schema:t,table:n,commit_timestamp:r,eventType:a,new:{},old:{},errors:o};c=Object.assign(Object.assign({},i),this._getPayloadRecords(e))}e.callback(c,n)})}_isClosed(){return this.state===b.closed}_isJoined(){return this.state===b.joined}_isJoining(){return this.state===b.joining}_isLeaving(){return this.state===b.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,n){const r=e.toLocaleLowerCase(),a={type:r,filter:t,callback:n};return this.bindings[r]?this.bindings[r].push(a):this.bindings[r]=[a],this}_off(e,t){const n=e.toLocaleLowerCase();return this.bindings[n]=this.bindings[n].filter(e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===n&&B.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(w.close,{},e)}_onError(e){this._on(w.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=b.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=C(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=C(e.columns,e.old_record)),t}}const H=()=>{};class W{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=H,this.ref=0,this.logger=H,this.conn=null,this.sendBuffer=[],this.serializer=new x,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,4630)).then(e=>{let{default:n}=e;return n(...t)})}:fetch),function(){return t(...arguments)}},this.endPoint=`${e}/${k.websocket}`,this.httpEndpoint=$(e),(null===t||void 0===t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null===t||void 0===t?void 0:t.params)&&(this.params=t.params),(null===t||void 0===t?void 0:t.timeout)&&(this.timeout=t.timeout),(null===t||void 0===t?void 0:t.logger)&&(this.logger=t.logger),((null===t||void 0===t?void 0:t.logLevel)||(null===t||void 0===t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null===t||void 0===t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const a=null===(r=null===t||void 0===t?void 0:t.params)||void 0===r?void 0:r.apikey;if(a&&(this.accessTokenValue=a,this.apiKey=a),this.reconnectAfterMs=(null===t||void 0===t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null===t||void 0===t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null===t||void 0===t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new E(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null===t||void 0===t?void 0:t.fetch),null===t||void 0===t?void 0:t.worker){if("undefined"!==typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null===t||void 0===t?void 0:t.worker)||!1,this.workerUrl=null===t||void 0===t?void 0:t.workerUrl}this.accessToken=(null===t||void 0===t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=v),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!==t&&void 0!==t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,n){this.logger(e,t,n)}connectionState(){switch(this.conn&&this.conn.readyState){case y.connecting:return _.Connecting;case y.open:return _.Open;case y.closing:return _.Closing;default:return _.Closed}}isConnected(){return this.connectionState()===_.Open}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};const n=`realtime:${e}`,r=this.getChannels().find(e=>e.topic===n);if(r)return r;{const n=new B(`realtime:${e}`,t,this);return this.channels.push(n),n}}push(e){const{topic:t,event:n,payload:r,ref:a}=e,o=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${n} (${a})`,r),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(){let e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=e&&(this.accessTokenValue=e,this.channels.forEach(t=>{const n={access_token:e,version:"realtime-js/2.11.15"};e&&t.updateJoinPayload(n),t.joinedOnce&&t._isJoined()&&t._push(w.access_token,{access_token:e})}))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:n,payload:r,ref:a}=e;"phoenix"===t&&"phx_reply"===n&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${n} ${a&&"("+a+")"||""}`,r),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(n,r,a)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(w.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const n=e.match(/\?/)?"&":"?";return`${e}${n}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class q extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function V(e){return"object"===typeof e&&null!==e&&"__isStorageError"in e}class K extends q{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class J extends q{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Y=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};const G=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,4630)).then(e=>{let{default:n}=e;return n(...t)})}:fetch),function(){return t(...arguments)}},Q=e=>{if(Array.isArray(e))return e.map(e=>Q(e));if("function"===typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(e=>{let[n,r]=e;const a=n.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[a]=Q(r)}),t};var X=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};const Z=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ee=(e,t,r)=>X(void 0,void 0,void 0,function*(){const a=yield Y(void 0,void 0,void 0,function*(){return"undefined"===typeof Response?(yield Promise.resolve().then(n.bind(n,4630))).Response:Response});e instanceof a&&!(null===r||void 0===r?void 0:r.noResolveJson)?e.json().then(n=>{t(new K(Z(n),e.status||500))}).catch(e=>{t(new J(Z(e),e))}):t(new J(Z(e),e))});function te(e,t,n,r,a,o){return X(this,void 0,void 0,function*(){return new Promise((i,s)=>{e(n,((e,t,n,r)=>{const a={method:e,headers:(null===t||void 0===t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json"},null===t||void 0===t?void 0:t.headers),r&&(a.body=JSON.stringify(r)),Object.assign(Object.assign({},a),n))})(t,r,a,o)).then(e=>{if(!e.ok)throw e;return(null===r||void 0===r?void 0:r.noResolveJson)?e:e.json()}).then(e=>i(e)).catch(e=>ee(e,s,r))})})}function ne(e,t,n,r){return X(this,void 0,void 0,function*(){return te(e,"GET",t,n,r)})}function re(e,t,n,r,a){return X(this,void 0,void 0,function*(){return te(e,"POST",t,r,a,n)})}function ae(e,t,n,r,a){return X(this,void 0,void 0,function*(){return te(e,"DELETE",t,r,a,n)})}var oe=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};const ie={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},se={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;this.url=e,this.headers=t,this.bucketId=n,this.fetch=G(r)}uploadOrUpdate(e,t,n,r){return oe(this,void 0,void 0,function*(){try{let a;const o=Object.assign(Object.assign({},se),r);let i=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)});const s=o.metadata;"undefined"!==typeof Blob&&n instanceof Blob?(a=new FormData,a.append("cacheControl",o.cacheControl),s&&a.append("metadata",this.encodeMetadata(s)),a.append("",n)):"undefined"!==typeof FormData&&n instanceof FormData?(a=n,a.append("cacheControl",o.cacheControl),s&&a.append("metadata",this.encodeMetadata(s))):(a=n,i["cache-control"]=`max-age=${o.cacheControl}`,i["content-type"]=o.contentType,s&&(i["x-metadata"]=this.toBase64(this.encodeMetadata(s)))),(null===r||void 0===r?void 0:r.headers)&&(i=Object.assign(Object.assign({},i),r.headers));const l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:a,headers:i},(null===o||void 0===o?void 0:o.duplex)?{duplex:o.duplex}:{})),d=yield c.json();if(c.ok)return{data:{path:l,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(a){if(V(a))return{data:null,error:a};throw a}})}upload(e,t,n){return oe(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,n)})}uploadToSignedUrl(e,t,n,r){return oe(this,void 0,void 0,function*(){const a=this._removeEmptyFolders(e),o=this._getFinalPath(a),i=new URL(this.url+`/object/upload/sign/${o}`);i.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:se.upsert},r),o=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!==typeof Blob&&n instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",n)):"undefined"!==typeof FormData&&n instanceof FormData?(e=n,e.append("cacheControl",t.cacheControl)):(e=n,o["cache-control"]=`max-age=${t.cacheControl}`,o["content-type"]=t.contentType);const s=yield this.fetch(i.toString(),{method:"PUT",body:e,headers:o}),l=yield s.json();if(s.ok)return{data:{path:a,fullPath:l.Key},error:null};return{data:null,error:l}}catch(s){if(V(s))return{data:null,error:s};throw s}})}createSignedUploadUrl(e,t){return oe(this,void 0,void 0,function*(){try{let n=this._getFinalPath(e);const r=Object.assign({},this.headers);(null===t||void 0===t?void 0:t.upsert)&&(r["x-upsert"]="true");const a=yield re(this.fetch,`${this.url}/object/upload/sign/${n}`,{},{headers:r}),o=new URL(this.url+a.url),i=o.searchParams.get("token");if(!i)throw new q("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:i},error:null}}catch(n){if(V(n))return{data:null,error:n};throw n}})}update(e,t,n){return oe(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,n)})}move(e,t,n){return oe(this,void 0,void 0,function*(){try{return{data:yield re(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null===n||void 0===n?void 0:n.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(V(r))return{data:null,error:r};throw r}})}copy(e,t,n){return oe(this,void 0,void 0,function*(){try{return{data:{path:(yield re(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null===n||void 0===n?void 0:n.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(V(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,n){return oe(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),a=yield re(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null===n||void 0===n?void 0:n.transform)?{transform:n.transform}:{}),{headers:this.headers});const o=(null===n||void 0===n?void 0:n.download)?`&download=${!0===n.download?"":n.download}`:"";return a={signedUrl:encodeURI(`${this.url}${a.signedURL}${o}`)},{data:a,error:null}}catch(r){if(V(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,n){return oe(this,void 0,void 0,function*(){try{const r=yield re(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),a=(null===n||void 0===n?void 0:n.download)?`&download=${!0===n.download?"":n.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${a}`):null})),error:null}}catch(r){if(V(r))return{data:null,error:r};throw r}})}download(e,t){return oe(this,void 0,void 0,function*(){const n="undefined"!==typeof(null===t||void 0===t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null===t||void 0===t?void 0:t.transform)||{}),a=r?`?${r}`:"";try{const t=this._getFinalPath(e),r=yield ne(this.fetch,`${this.url}/${n}/${t}${a}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(o){if(V(o))return{data:null,error:o};throw o}})}info(e){return oe(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield ne(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Q(e),error:null}}catch(n){if(V(n))return{data:null,error:n};throw n}})}exists(e){return oe(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,n,r){return X(this,void 0,void 0,function*(){return te(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(n){if(V(n)&&n instanceof J){const e=n.originalError;if([400,404].includes(null===e||void 0===e?void 0:e.status))return{data:!1,error:n}}throw n}})}getPublicUrl(e,t){const n=this._getFinalPath(e),r=[],a=(null===t||void 0===t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==a&&r.push(a);const o="undefined"!==typeof(null===t||void 0===t?void 0:t.transform)?"render/image":"object",i=this.transformOptsToQueryString((null===t||void 0===t?void 0:t.transform)||{});""!==i&&r.push(i);let s=r.join("&");return""!==s&&(s=`?${s}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${n}${s}`)}}}remove(e){return oe(this,void 0,void 0,function*(){try{return{data:yield ae(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(V(t))return{data:null,error:t};throw t}})}list(e,t,n){return oe(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},ie),t),{prefix:e||""});return{data:yield re(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},n),error:null}}catch(r){if(V(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!==typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const ue={"X-Client-Info":"storage-js/2.7.1"};var ce=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};class de{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;this.url=e,this.headers=Object.assign(Object.assign({},ue),t),this.fetch=G(n)}listBuckets(){return ce(this,void 0,void 0,function*(){try{return{data:yield ne(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(V(e))return{data:null,error:e};throw e}})}getBucket(e){return ce(this,void 0,void 0,function*(){try{return{data:yield ne(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(V(t))return{data:null,error:t};throw t}})}createBucket(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{public:!1};return ce(this,void 0,void 0,function*(){try{return{data:yield re(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(V(n))return{data:null,error:n};throw n}})}updateBucket(e,t){return ce(this,void 0,void 0,function*(){try{const n=yield function(e,t,n,r,a){return X(this,void 0,void 0,function*(){return te(e,"PUT",t,r,a,n)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:n,error:null}}catch(n){if(V(n))return{data:null,error:n};throw n}})}emptyBucket(e){return ce(this,void 0,void 0,function*(){try{return{data:yield re(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(V(t))return{data:null,error:t};throw t}})}deleteBucket(e){return ce(this,void 0,void 0,function*(){try{return{data:yield ae(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(V(t))return{data:null,error:t};throw t}})}}class fe extends de{constructor(e){super(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},arguments.length>2?arguments[2]:void 0)}from(e){return new le(this.url,this.headers,e,this.fetch)}}let he="";he="undefined"!==typeof Deno?"deno":"undefined"!==typeof document?"web":"undefined"!==typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const pe={headers:{"X-Client-Info":`supabase-js-${he}/2.50.3`}},ge={schema:"public"},me={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ve={};var ye=n(4630),be=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};const we=e=>{let t;return t=e||("undefined"===typeof fetch?ye.default:fetch),function(){return t(...arguments)}},ke=(e,t,n)=>{const r=we(n),a="undefined"===typeof Headers?ye.Headers:Headers;return(n,o)=>be(void 0,void 0,void 0,function*(){var i;const s=null!==(i=yield t())&&void 0!==i?i:e;let l=new a(null===o||void 0===o?void 0:o.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${s}`),r(n,Object.assign(Object.assign({},o),{headers:l}))})};var _e=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};const Se="2.70.0",xe=3e4,Ee=9e4,Ce={"X-Client-Info":`gotrue-js/${Se}`},Oe="X-Supabase-Api-Version",Pe={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Te=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Ae extends Error{constructor(e,t,n){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=n}}function je(e){return"object"===typeof e&&null!==e&&"__isAuthError"in e}class Re extends Ae{constructor(e,t,n){super(e,t,n),this.name="AuthApiError",this.status=t,this.code=n}}class Ne extends Ae{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Le extends Ae{constructor(e,t,n,r){super(e,n,r),this.name=t,this.status=n}}class $e extends Le{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class De extends Le{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ie extends Le{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class ze extends Le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Me extends Le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Fe extends Le{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Ue(e){return je(e)&&"AuthRetryableFetchError"===e.name}class Be extends Le{constructor(e,t,n){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=n}}class He extends Le{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const We="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),qe=" \t\n\r=".split(""),Ve=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<qe.length;t+=1)e[qe[t].charCodeAt(0)]=-2;for(let t=0;t<We.length;t+=1)e[We[t].charCodeAt(0)]=t;return e})();function Ke(e,t,n){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;n(We[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;n(We[e]),t.queuedBits-=6}}function Je(e,t,n){const r=Ve[e];if(!(r>-1)){if(-2===r)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function Ye(e){const t=[],n=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},a={queue:0,queuedBits:0},o=e=>{!function(e,t,n){if(0===t.utf8seq){if(e<=127)return void n(e);for(let n=1;n<6;n+=1)if(0===(e>>7-n&1)){t.utf8seq=n;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&n(t.codepoint)}}(e,r,n)};for(let i=0;i<e.length;i+=1)Je(e.charCodeAt(i),a,o);return t.join("")}function Ge(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function Qe(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(n+1)-56320&65535|t),n+=1}Ge(r,t)}}function Xe(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let a=0;a<e.length;a+=1)Je(e.charCodeAt(a),n,r);return new Uint8Array(t)}function Ze(e){const t=[],n={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach(e=>Ke(e,n,r)),Ke(null,n,r),t.join("")}const et=()=>"undefined"!==typeof window&&"undefined"!==typeof document,tt={tested:!1,writable:!1},nt=()=>{if(!et())return!1;try{if("object"!==typeof globalThis.localStorage)return!1}catch(t){return!1}if(tt.tested)return tt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),tt.tested=!0,tt.writable=!0}catch(t){tt.tested=!0,tt.writable=!1}return tt.writable};const rt=e=>{let t;return t=e||("undefined"===typeof fetch?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Promise.resolve().then(n.bind(n,4630)).then(e=>{let{default:n}=e;return n(...t)})}:fetch),function(){return t(...arguments)}},at=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},ot=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch(r){return n}},it=async(e,t)=>{await e.removeItem(t)};class st{constructor(){this.promise=new st.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function lt(e){const t=e.split(".");if(3!==t.length)throw new He("Invalid JWT structure");for(let n=0;n<t.length;n++)if(!Te.test(t[n]))throw new He("JWT not in base64url format");return{header:JSON.parse(Ye(t[0])),payload:JSON.parse(Ye(t[1])),signature:Xe(t[2]),raw:{header:t[0],payload:t[1]}}}function ut(e){return("0"+e.toString(16)).substr(-2)}async function ct(e){if(!("undefined"!==typeof crypto&&"undefined"!==typeof crypto.subtle&&"undefined"!==typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),n=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(n);return Array.from(r).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function dt(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=function(){const e=new Uint32Array(56);if("undefined"===typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let n="";for(let r=0;r<56;r++)n+=e.charAt(Math.floor(Math.random()*t));return n}return crypto.getRandomValues(e),Array.from(e,ut).join("")}();let a=r;n&&(a+="/PASSWORD_RECOVERY"),await at(e,`${t}-code-verifier`,a);const o=await ct(r);return[o,r===o?"plain":"s256"]}st.promiseConstructor=Promise;const ft=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const ht=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function pt(e){if(!ht.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var gt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};const mt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),vt=[502,503,504];async function yt(e){var t,n;if(!("object"===typeof(n=e)&&null!==n&&"status"in n&&"ok"in n&&"json"in n&&"function"===typeof n.json))throw new Fe(mt(e),0);if(vt.includes(e.status))throw new Fe(mt(e),e.status);let r,a;try{r=await e.json()}catch(i){throw new Ne(mt(i),i)}const o=function(e){const t=e.headers.get(Oe);if(!t)return null;if(!t.match(ft))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(i){return null}}(e);if(o&&o.getTime()>=Pe.timestamp&&"object"===typeof r&&r&&"string"===typeof r.code?a=r.code:"object"===typeof r&&r&&"string"===typeof r.error_code&&(a=r.error_code),a){if("weak_password"===a)throw new Be(mt(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===a)throw new $e}else if("object"===typeof r&&r&&"object"===typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"===typeof t,!0))throw new Be(mt(r),e.status,r.weak_password.reasons);throw new Re(mt(r),e.status||500,a)}async function bt(e,t,n,r){var a;const o=Object.assign({},null===r||void 0===r?void 0:r.headers);o[Oe]||(o[Oe]=Pe.name),(null===r||void 0===r?void 0:r.jwt)&&(o.Authorization=`Bearer ${r.jwt}`);const i=null!==(a=null===r||void 0===r?void 0:r.query)&&void 0!==a?a:{};(null===r||void 0===r?void 0:r.redirectTo)&&(i.redirect_to=r.redirectTo);const s=Object.keys(i).length?"?"+new URLSearchParams(i).toString():"",l=await async function(e,t,n,r,a,o){const i=((e,t,n,r)=>{const a={method:e,headers:(null===t||void 0===t?void 0:t.headers)||{}};return"GET"===e?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null===t||void 0===t?void 0:t.headers),a.body=JSON.stringify(r),Object.assign(Object.assign({},a),n))})(t,r,a,o);let s;try{s=await e(n,Object.assign({},i))}catch(l){throw console.error(l),new Fe(mt(l),0)}s.ok||await yt(s);if(null===r||void 0===r?void 0:r.noResolveJson)return s;try{return await s.json()}catch(l){await yt(l)}}(e,t,n+s,{headers:o,noResolveJson:null===r||void 0===r?void 0:r.noResolveJson},{},null===r||void 0===r?void 0:r.body);return(null===r||void 0===r?void 0:r.xform)?null===r||void 0===r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function wt(e){var t;let n=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:n,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function kt(e){const t=wt(e);return!t.error&&e.weak_password&&"object"===typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"===typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"===typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function _t(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function St(e){return{data:e,error:null}}function xt(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:a,verification_type:o}=e,i=gt(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:n,hashed_token:r,redirect_to:a,verification_type:o},user:Object.assign({},i)},error:null}}function Et(e){return e}const Ct=["global","local","others"];var Ot=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n};class Pt{constructor(e){let{url:t="",headers:n={},fetch:r}=e;this.url=t,this.headers=n,this.fetch=rt(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ct[0];if(Ct.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Ct.join(", ")}`);try{return await bt(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(n){if(je(n))return{data:null,error:n};throw n}}async inviteUserByEmail(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await bt(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:_t})}catch(n){if(je(n))return{data:{user:null},error:n};throw n}}async generateLink(e){try{const{options:t}=e,n=Ot(e,["options"]),r=Object.assign(Object.assign({},n),t);return"newEmail"in n&&(r.new_email=null===n||void 0===n?void 0:n.newEmail,delete r.newEmail),await bt(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:xt,redirectTo:null===t||void 0===t?void 0:t.redirectTo})}catch(t){if(je(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await bt(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:_t})}catch(t){if(je(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,n,r,a,o,i,s;try{const l={nextPage:null,lastPage:0,total:0},u=await bt(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(n=null===(t=null===e||void 0===e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==n?n:"",per_page:null!==(a=null===(r=null===e||void 0===e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==a?a:""},xform:Et});if(u.error)throw u.error;const c=await u.json(),d=null!==(o=u.headers.get("x-total-count"))&&void 0!==o?o:0,f=null!==(s=null===(i=u.headers.get("link"))||void 0===i?void 0:i.split(","))&&void 0!==s?s:[];return f.length>0&&(f.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),n=JSON.parse(e.split(";")[1].split("=")[1]);l[`${n}Page`]=t}),l.total=parseInt(d)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(l){if(je(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){pt(e);try{return await bt(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:_t})}catch(t){if(je(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){pt(e);try{return await bt(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:_t})}catch(n){if(je(n))return{data:{user:null},error:n};throw n}}async deleteUser(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];pt(e);try{return await bt(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:_t})}catch(n){if(je(n))return{data:{user:null},error:n};throw n}}async _listFactors(e){pt(e.userId);try{const{data:t,error:n}=await bt(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:n}}catch(t){if(je(t))return{data:null,error:t};throw t}}async _deleteFactor(e){pt(e.userId),pt(e.id);try{return{data:await bt(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(je(t))return{data:null,error:t};throw t}}}const Tt={getItem:e=>nt()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{nt()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{nt()&&globalThis.localStorage.removeItem(e)}};function At(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}const jt=!!(globalThis&&nt()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class Rt extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class Nt extends Rt{}async function Lt(e,t,n){jt&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),jt&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async r=>{if(!r){if(0===t)throw jt&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Nt(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(jt)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(a){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",a)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}jt&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await n()}finally{jt&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}))}!function(){if("object"!==typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!==typeof self&&(self.globalThis=self)}}();const $t={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Ce,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Dt(e,t,n){return await n()}class It{constructor(e){var t,n;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=It.nextInstanceID,It.nextInstanceID+=1,this.instanceID>0&&et()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},$t),e);if(this.logDebugMessages=!!r.debug,"function"===typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new Pt({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=rt(r.fetch),this.lock=r.lock||Dt,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:et()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=Lt:this.lock=Dt,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:nt()?this.storage=Tt:(this.memoryStorage={},this.storage=At(this.memoryStorage)):(this.memoryStorage={},this.storage=At(this.memoryStorage)),et()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(a){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",a)}null===(n=this.broadcastChannel)||void 0===n||n.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(){if(this.logDebugMessages){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.logger(`GoTrueClient@${this.instanceID} (${Se}) ${(new Date).toISOString()}`,...t)}return this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},n=new URL(e);if(n.hash&&"#"===n.hash[0])try{new URLSearchParams(n.hash.substring(1)).forEach((e,n)=>{t[n]=e})}catch(r){}return n.searchParams.forEach((e,n)=>{t[n]=e}),t}(window.location.href);let n="none";if(this._isImplicitGrantCallback(t)?n="implicit":await this._isPKCECallback(t)&&(n="pkce"),et()&&this.detectSessionInUrl&&"none"!==n){const{data:r,error:a}=await this._getSessionFromURL(t,n);if(a){if(this._debug("#_initialize()","error detecting session from URL",a),function(e){return je(e)&&"AuthImplicitGrantRedirectError"===e.name}(a)){const t=null===(e=a.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:a}}return await this._removeSession(),{error:a}}const{session:o,redirectType:i}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",i),await this._saveSession(o),setTimeout(async()=>{"recovery"===i?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return je(t)?{error:t}:{error:new Ne("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,n,r;try{const a=await bt(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(n=null===(t=null===e||void 0===e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==n?n:{},gotrue_meta_security:{captcha_token:null===(r=null===e||void 0===e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:wt}),{data:o,error:i}=a;if(i||!o)return{data:{user:null,session:null},error:i};const s=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(a){if(je(a))return{data:{user:null,session:null},error:a};throw a}}async signUp(e){var t,n,r;try{let a;if("email"in e){const{email:n,password:r,options:o}=e;let i=null,s=null;"pkce"===this.flowType&&([i,s]=await dt(this.storage,this.storageKey)),a=await bt(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null===o||void 0===o?void 0:o.emailRedirectTo,body:{email:n,password:r,data:null!==(t=null===o||void 0===o?void 0:o.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null===o||void 0===o?void 0:o.captchaToken},code_challenge:i,code_challenge_method:s},xform:wt})}else{if(!("phone"in e))throw new Ie("You must provide either an email or phone number and a password");{const{phone:t,password:o,options:i}=e;a=await bt(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:o,data:null!==(n=null===i||void 0===i?void 0:i.data)&&void 0!==n?n:{},channel:null!==(r=null===i||void 0===i?void 0:i.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null===i||void 0===i?void 0:i.captchaToken}},xform:wt})}}const{data:o,error:i}=a;if(i||!o)return{data:{user:null,session:null},error:i};const s=o.session,l=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(a){if(je(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithPassword(e){try{let t;if("email"in e){const{email:n,password:r,options:a}=e;t=await bt(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:n,password:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}},xform:kt})}else{if(!("phone"in e))throw new Ie("You must provide either an email or phone number and a password");{const{phone:n,password:r,options:a}=e;t=await bt(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:n,password:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}},xform:kt})}}const{data:n,error:r}=t;return r?{data:{user:null,session:null},error:r}:n&&n.session&&n.user?(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:r}):{data:{user:null,session:null},error:new De}}catch(t){if(je(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,n,r,a;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(n=e.options)||void 0===n?void 0:n.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(a=e.options)||void 0===a?void 0:a.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,n,r,a,o,i,s,l,u,c,d,f;let h,p;if("message"in e)h=e.message,p=e.signature;else{const{chain:d,wallet:f,statement:g,options:m}=e;let v;if(et())if("object"===typeof f)v=f;else{const e=window;if(!("solana"in e)||"object"!==typeof e.solana||!("signIn"in e.solana&&"function"===typeof e.solana.signIn||"signMessage"in e.solana&&"function"===typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");v=e.solana}else{if("object"!==typeof f||!(null===m||void 0===m?void 0:m.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");v=f}const y=new URL(null!==(t=null===m||void 0===m?void 0:m.url)&&void 0!==t?t:window.location.href);if("signIn"in v&&v.signIn){const e=await v.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null===m||void 0===m?void 0:m.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"===typeof e[0])t=e[0];else{if(!(e&&"object"===typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"===typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");h="string"===typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in v)||"function"!==typeof v.signMessage||!("publicKey"in v)||"object"!==typeof v||!v.publicKey||!("toBase58"in v.publicKey)||"function"!==typeof v.publicKey.toBase58)throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");h=[`${y.host} wants you to sign in with your Solana account:`,v.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(r=null===(n=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===n?void 0:n.issuedAt)&&void 0!==r?r:(new Date).toISOString()}`,...(null===(a=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===a?void 0:a.notBefore)?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...(null===(o=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===o?void 0:o.expirationTime)?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...(null===(i=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===i?void 0:i.chainId)?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...(null===(s=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===s?void 0:s.nonce)?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...(null===(l=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...(null===(c=null===(u=null===m||void 0===m?void 0:m.signInWithSolana)||void 0===u?void 0:u.resources)||void 0===c?void 0:c.length)?["Resources",...m.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await v.signMessage((new TextEncoder).encode(h),"utf8");if(!e||!(e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:n}=await bt(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:h,signature:Ze(p)},(null===(d=e.options)||void 0===d?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:null===(f=e.options)||void 0===f?void 0:f.captchaToken}}:null),xform:wt});if(n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:n}):{data:{user:null,session:null},error:new De}}catch(g){if(je(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await ot(this.storage,`${this.storageKey}-code-verifier`),[n,r]=(null!==t&&void 0!==t?t:"").split("/");try{const{data:t,error:a}=await bt(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:n},xform:wt});if(await it(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!==r&&void 0!==r?r:null}),error:a}):{data:{user:null,session:null,redirectType:null},error:new De}}catch(a){if(je(a))return{data:{user:null,session:null,redirectType:null},error:a};throw a}}async signInWithIdToken(e){try{const{options:t,provider:n,token:r,access_token:a,nonce:o}=e,i=await bt(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:n,id_token:r,access_token:a,nonce:o,gotrue_meta_security:{captcha_token:null===t||void 0===t?void 0:t.captchaToken}},xform:wt}),{data:s,error:l}=i;return l?{data:{user:null,session:null},error:l}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:s,error:l}):{data:{user:null,session:null},error:new De}}catch(t){if(je(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,n,r,a,o;try{if("email"in e){const{email:r,options:a}=e;let o=null,i=null;"pkce"===this.flowType&&([o,i]=await dt(this.storage,this.storageKey));const{error:s}=await bt(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!==(t=null===a||void 0===a?void 0:a.data)&&void 0!==t?t:{},create_user:null===(n=null===a||void 0===a?void 0:a.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken},code_challenge:o,code_challenge_method:i},redirectTo:null===a||void 0===a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){const{phone:t,options:n}=e,{data:i,error:s}=await bt(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(r=null===n||void 0===n?void 0:n.data)&&void 0!==r?r:{},create_user:null===(a=null===n||void 0===n?void 0:n.shouldCreateUser)||void 0===a||a,gotrue_meta_security:{captcha_token:null===n||void 0===n?void 0:n.captchaToken},channel:null!==(o=null===n||void 0===n?void 0:n.channel)&&void 0!==o?o:"sms"}});return{data:{user:null,session:null,messageId:null===i||void 0===i?void 0:i.message_id},error:s}}throw new Ie("You must provide either an email or phone number.")}catch(i){if(je(i))return{data:{user:null,session:null},error:i};throw i}}async verifyOtp(e){var t,n;try{let r,a;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,a=null===(n=e.options)||void 0===n?void 0:n.captchaToken);const{data:o,error:i}=await bt(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:a}}),redirectTo:r,xform:wt});if(i)throw i;if(!o)throw new Error("An error occurred on token verification.");const s=o.session,l=o.user;return(null===s||void 0===s?void 0:s.access_token)&&(await this._saveSession(s),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",s)),{data:{user:l,session:s},error:null}}catch(r){if(je(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,n,r;try{let a=null,o=null;return"pkce"===this.flowType&&([a,o]=await dt(this.storage,this.storageKey)),await bt(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(n=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==n?n:void 0}),(null===(r=null===e||void 0===e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:a,code_challenge_method:o}),headers:this.headers,xform:St})}catch(a){if(je(a))return{data:null,error:a};throw a}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:n}=e;if(n)throw n;if(!t)throw new $e;const{error:r}=await bt(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(je(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:n,type:r,options:a}=e,{error:o}=await bt(this.fetch,"POST",t,{headers:this.headers,body:{email:n,type:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}},redirectTo:null===a||void 0===a?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:n,type:r,options:a}=e,{data:o,error:i}=await bt(this.fetch,"POST",t,{headers:this.headers,body:{phone:n,type:r,gotrue_meta_security:{captcha_token:null===a||void 0===a?void 0:a.captchaToken}}});return{data:{user:null,session:null,messageId:null===o||void 0===o?void 0:o.message_id},error:i}}throw new Ie("You must provide either an email or phone number and a type")}catch(t){if(je(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await n}catch(e){}})()),n}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await ot(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const n=!!e.expires_at&&1e3*e.expires_at-Date.now()<Ee;if(this._debug("#__loadSession()",`session has${n?"":" not"} expired`,"expires_at",e.expires_at),!n){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,n,r)=>(t||"user"!==n||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,n,r))})}return{data:{session:e},error:null}}const{session:r,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{session:null},error:a}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await bt(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:_t}):await this._useSession(async e=>{var t,n,r;const{data:a,error:o}=e;if(o)throw o;return(null===(t=a.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await bt(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(r=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0,xform:_t}):{data:{user:null},error:new $e}})}catch(t){if(je(t))return function(e){return je(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await it(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await this._useSession(async n=>{const{data:r,error:a}=n;if(a)throw a;if(!r.session)throw new $e;const o=r.session;let i=null,s=null;"pkce"===this.flowType&&null!=e.email&&([i,s]=await dt(this.storage,this.storageKey));const{data:l,error:u}=await bt(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null===t||void 0===t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:i,code_challenge_method:s}),jwt:o.access_token,xform:_t});if(u)throw u;return o.user=l.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(n){if(je(n))return{data:{user:null},error:n};throw n}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new $e;const t=Date.now()/1e3;let n=t,r=!0,a=null;const{payload:o}=lt(e.access_token);if(o.exp&&(n=o.exp,r=n<=t),r){const{session:t,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{user:null,session:null},error:n};if(!t)return{data:{user:null,session:null},error:null};a=t}else{const{data:r,error:o}=await this._getUser(e.access_token);if(o)throw o;a={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:n-t,expires_at:n},await this._saveSession(a),await this._notifyAllSubscribers("SIGNED_IN",a)}return{data:{user:a.user,session:a},error:null}}catch(t){if(je(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var n;if(!e){const{data:r,error:a}=t;if(a)throw a;e=null!==(n=r.session)&&void 0!==n?n:void 0}if(!(null===e||void 0===e?void 0:e.refresh_token))throw new $e;const{session:r,error:a}=await this._callRefreshToken(e.refresh_token);return a?{data:{user:null,session:null},error:a}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(je(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!et())throw new ze("No browser detected.");if(e.error||e.error_description||e.error_code)throw new ze(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Me("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new ze("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Me("No code detected.");const{data:t,error:n}=await this._exchangeCodeForSession(e.code);if(n)throw n;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:n,provider_refresh_token:r,access_token:a,refresh_token:o,expires_in:i,expires_at:s,token_type:l}=e;if(!a||!i||!o||!l)throw new ze("No session defined in URL");const u=Math.round(Date.now()/1e3),c=parseInt(i);let d=u+c;s&&(d=parseInt(s));const f=d-u;1e3*f<=xe&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${c}s`);const h=d-c;u-h>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",h,d,u):u-h<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",h,d,u);const{data:p,error:g}=await this._getUser(a);if(g)throw g;const m={provider_token:n,provider_refresh_token:r,access_token:a,expires_in:c,expires_at:d,refresh_token:o,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(n){if(je(n))return{data:{session:null,redirectType:null},error:n};throw n}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await ot(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scope:"global"};return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut(){let{scope:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scope:"global"};return await this._useSession(async t=>{var n;const{data:r,error:a}=t;if(a)return{error:a};const o=null===(n=r.session)||void 0===n?void 0:n.access_token;if(o){const{error:t}=await this.admin.signOut(o,e);if(t&&(!function(e){return je(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await it(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),n={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,n),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:n}}}async _emitInitialSession(e){return await this._useSession(async t=>{var n,r;try{const{data:{session:r},error:a}=t;if(a)throw a;await(null===(n=this.stateChangeEmitters.get(e))||void 0===n?void 0:n.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(a){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",a),console.error(a)}})}async resetPasswordForEmail(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null;"pkce"===this.flowType&&([n,r]=await dt(this.storage,this.storageKey,!0));try{return await bt(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:n,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(a){if(je(a))return{data:null,error:a};throw a}}async getUserIdentities(){var e;try{const{data:t,error:n}=await this.getUser();if(n)throw n;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(je(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:n,error:r}=await this._useSession(async t=>{var n,r,a,o,i;const{data:s,error:l}=t;if(l)throw l;const u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(n=e.options)||void 0===n?void 0:n.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(a=e.options)||void 0===a?void 0:a.queryParams,skipBrowserRedirect:!0});return await bt(this.fetch,"GET",u,{headers:this.headers,jwt:null!==(i=null===(o=s.session)||void 0===o?void 0:o.access_token)&&void 0!==i?i:void 0})});if(r)throw r;return et()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null===n||void 0===n?void 0:n.url),{data:{provider:e.provider,url:null===n||void 0===n?void 0:n.url},error:null}}catch(n){if(je(n))return{data:{provider:e.provider,url:null},error:n};throw n}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var n,r;const{data:a,error:o}=t;if(o)throw o;return await bt(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(r=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==r?r:void 0})})}catch(t){if(je(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const a=Date.now();return await(n=async n=>(n>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,n-1)),this._debug(t,"refreshing attempt",n),await bt(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:wt})),r=(e,t)=>{const n=200*Math.pow(2,e);return t&&Ue(t)&&Date.now()+n-a<xe},new Promise((e,t)=>{(async()=>{for(let o=0;o<1/0;o++)try{const t=await n(o);if(!r(o,null,t))return void e(t)}catch(a){if(!r(o,a))return void t(a)}})()}))}catch(a){if(this._debug(t,"error",a),je(a))return{data:{session:null,user:null},error:a};throw a}finally{this._debug(t,"end")}var n,r}_isValidSession(e){return"object"===typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const n=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",n),et()&&!t.skipBrowserRedirect&&window.location.assign(n),{data:{provider:e,url:n},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const n=await ot(this.storage,this.storageKey);if(this._debug(t,"session from storage",n),!this._isValidSession(n))return this._debug(t,"session is not valid"),void(null!==n&&await this._removeSession());const r=1e3*(null!==(e=n.expires_at)&&void 0!==e?e:1/0)-Date.now()<Ee;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&n.refresh_token){const{error:e}=await this._callRefreshToken(n.refresh_token);e&&(console.error(e),Ue(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){return this._debug(t,"error",n),void console.error(n)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,n;if(!e)throw new $e;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new st;const{data:t,error:n}=await this._refreshAccessToken(e);if(n)throw n;if(!t.session)throw new $e;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(a){if(this._debug(r,"error",a),je(a)){const e={session:null,error:a};return Ue(a)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(n=this.refreshingDeferred)||void 0===n||n.reject(a),a}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${n}`);try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],a=Array.from(this.stateChangeEmitters.values()).map(async n=>{try{await n.callback(e,t)}catch(a){r.push(a)}});if(await Promise.all(a),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await at(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await it(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&et()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),xe);this.autoRefreshTicker=e,e&&"object"===typeof e&&"function"===typeof e.unref?e.unref():"undefined"!==typeof Deno&&"function"===typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:n}}=e;if(!n||!n.refresh_token||!n.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*n.expires_at-t)/xe);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(n.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof Rt))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!et()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,n){const r=[`provider=${encodeURIComponent(t)}`];if((null===n||void 0===n?void 0:n.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(n.redirectTo)}`),(null===n||void 0===n?void 0:n.scopes)&&r.push(`scopes=${encodeURIComponent(n.scopes)}`),"pkce"===this.flowType){const[e,t]=await dt(this.storage,this.storageKey),n=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(n.toString())}if(null===n||void 0===n?void 0:n.queryParams){const e=new URLSearchParams(n.queryParams);r.push(e.toString())}return(null===n||void 0===n?void 0:n.skipBrowserRedirect)&&r.push(`skip_http_redirect=${n.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var n;const{data:r,error:a}=t;return a?{data:null,error:a}:await bt(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(n=null===r||void 0===r?void 0:r.session)||void 0===n?void 0:n.access_token})})}catch(t){if(je(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var n,r;const{data:a,error:o}=t;if(o)return{data:null,error:o};const i=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:s,error:l}=await bt(this.fetch,"POST",`${this.url}/factors`,{body:i,headers:this.headers,jwt:null===(n=null===a||void 0===a?void 0:a.session)||void 0===n?void 0:n.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null===s||void 0===s?void 0:s.totp)||void 0===r?void 0:r.qr_code)&&(s.totp.qr_code=`data:image/svg+xml;utf-8,${s.totp.qr_code}`),{data:s,error:null})})}catch(t){if(je(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var n;const{data:r,error:a}=t;if(a)return{data:null,error:a};const{data:o,error:i}=await bt(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(n=null===r||void 0===r?void 0:r.session)||void 0===n?void 0:n.access_token});return i?{data:null,error:i}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:i})})}catch(t){if(je(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var n;const{data:r,error:a}=t;return a?{data:null,error:a}:await bt(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(n=null===r||void 0===r?void 0:r.session)||void 0===n?void 0:n.access_token})})}catch(t){if(je(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:n}=await this._challenge({factorId:e.factorId});return n?{data:null,error:n}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const n=(null===e||void 0===e?void 0:e.factors)||[],r=n.filter(e=>"totp"===e.factor_type&&"verified"===e.status),a=n.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:n,totp:r,phone:a},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,n;const{data:{session:r},error:a}=e;if(a)return{data:null,error:a};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=lt(r.access_token);let i=null;o.aal&&(i=o.aal);let s=i;(null!==(n=null===(t=r.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==n?n:[]).length>0&&(s="aal2");return{data:{currentLevel:i,nextLevel:s,currentAuthenticationMethods:o.amr||[]},error:null}}))}async fetchJwk(e){let t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{keys:[]}).keys.find(t=>t.kid===e);if(t)return t;if(t=this.jwks.keys.find(t=>t.kid===e),t&&this.jwks_cached_at+6e5>Date.now())return t;const{data:n,error:r}=await bt(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(r)throw r;if(!n.keys||0===n.keys.length)throw new He("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),t=n.keys.find(t=>t.kid===e),!t)throw new He("No matching signing key found in JWKS");return t}async getClaims(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{keys:[]};try{let n=e;if(!n){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};n=e.session.access_token}const{header:r,payload:a,signature:o,raw:{header:i,payload:s}}=lt(n);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(a.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(n);if(e)throw e;return{data:{claims:a,header:r,signature:o},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),u=await this.fetchJwk(r.kid,t),c=await crypto.subtle.importKey("jwk",u,l,!0,["verify"]);if(!await crypto.subtle.verify(l,c,o,function(e){const t=[];return Qe(e,e=>t.push(e)),new Uint8Array(t)}(`${i}.${s}`)))throw new He("Invalid JWT signature");return{data:{claims:a,header:r,signature:o},error:null}}catch(n){if(je(n))return{data:null,error:n};throw n}}}It.nextInstanceID=0;const zt=It;class Mt extends zt{constructor(e){super(e)}}var Ft=function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function i(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(i,s)}l((r=r.apply(e,t||[])).next())})};class Ut{constructor(e,t,n){var r,a,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const i=(s=e).endsWith("/")?s:s+"/";var s;const l=new URL(i);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c=function(e,t){var n,r;const{db:a,auth:o,realtime:i,global:s}=e,{db:l,auth:u,realtime:c,global:d}=t,f={db:Object.assign(Object.assign({},l),a),auth:Object.assign(Object.assign({},u),o),realtime:Object.assign(Object.assign({},c),i),global:Object.assign(Object.assign(Object.assign({},d),s),{headers:Object.assign(Object.assign({},null!==(n=null===d||void 0===d?void 0:d.headers)&&void 0!==n?n:{}),null!==(r=null===s||void 0===s?void 0:s.headers)&&void 0!==r?r:{})}),accessToken:()=>_e(this,void 0,void 0,function*(){return""})};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}(null!==n&&void 0!==n?n:{},{db:ge,realtime:ve,auth:Object.assign(Object.assign({},me),{storageKey:u}),global:pe});this.storageKey=null!==(r=c.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(a=c.global.headers)&&void 0!==a?a:{},c.accessToken?(this.accessToken=c.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(o=c.auth)&&void 0!==o?o:{},this.headers,c.global.fetch),this.fetch=ke(t,this._getAccessToken.bind(this),c.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},c.realtime)),this.rest=new d(new URL("rest/v1",l).href,{headers:this.headers,schema:c.db.schema,fetch:this.fetch}),c.accessToken||this._listenForAuthEvents()}get functions(){return new u(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new fe(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.rest.rpc(e,t,n)}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Ft(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:n}=yield this.auth.getSession();return null!==(t=null===(e=n.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient(e,t,n){let{autoRefreshToken:r,persistSession:a,detectSessionInUrl:o,storage:i,storageKey:s,flowType:l,lock:u,debug:c}=e;const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Mt({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),t),storageKey:s,autoRefreshToken:r,persistSession:a,detectSessionInUrl:o,storage:i,flowType:l,lock:u,debug:c,fetch:n,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new W(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null===e||void 0===e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null===t||void 0===t?void 0:t.access_token)})}_handleTokenChanged(e,t,n){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===n?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=n}}let Bt=null,Ht=null,Wt=null;const qt=async()=>{if(Wt)return Wt;try{const e=await fetch(window.wpData.apiUrl+"config"),t=await e.json();if(!t.url||!t.anonKey)throw new Error("Supabase config not found.");return Bt=t.url,Ht=t.anonKey,Wt=((e,t,n)=>new Ut(e,t,n))(Bt,Ht),Wt}catch(e){return console.error("Failed to initialize Supabase client:",e),null}},Vt=()=>(Wt||console.error("Supabase has not been initialized. Call initSupabase() first."),Wt)},4358:(e,t)=>{"use strict";const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,s=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function u(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},4391:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(7004)},4488:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(5043),a=n(8139),o=n.n(a),i=n(579);const s=e=>r.forwardRef((t,n)=>(0,i.jsx)("div",{...t,ref:n,className:o()(t.className,e)}))},4541:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(8139),a=n.n(r),o=n(5043),i=n(7071),s=n(2644),l=n(5901),u=n(7852),c=n(579);const d=o.forwardRef((e,t)=>{let{bsPrefix:n,className:r,as:o=i.A,active:d,eventKey:f,disabled:h=!1,...p}=e;n=(0,u.oU)(n,"nav-link");const[g,m]=(0,s.M)({key:(0,l.u)(f,p.href),active:d,disabled:h,...p});return(0,c.jsx)(o,{...p,...g,ref:t,disabled:h,className:a()(r,n,h&&"disabled",m.isActive&&"active")})});d.displayName="NavLink";const f=d},4630:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Headers:()=>i,Request:()=>s,Response:()=>l,default:()=>o,fetch:()=>a});var r=function(){if("undefined"!==typeof self)return self;if("undefined"!==typeof window)return window;if("undefined"!==typeof n.g)return n.g;throw new Error("unable to locate global object")}();const a=r.fetch,o=r.fetch.bind(r),i=r.Headers,s=r.Request,l=r.Response},4696:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043);function a(e){const t=(0,r.useRef)(null);return(0,r.useEffect)(()=>{t.current=e}),t.current}},4737:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var r=n(8139),a=n.n(r),o=n(5043),i=n(1969),s=n(927),l=n(7852),u=n(9125),c=n(1778),d=n(579);const f=o.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:o="div",...i}=e;return r=(0,l.oU)(r,"nav-item"),(0,d.jsx)(o,{ref:t,className:a()(n,r),...i})});f.displayName="NavItem";const h=f;var p=n(4541);const g=o.forwardRef((e,t)=>{const{as:n="div",bsPrefix:r,variant:f,fill:h=!1,justify:p=!1,navbar:g,navbarScroll:m,className:v,activeKey:y,...b}=(0,i.Zw)(e,{activeKey:"onSelect"}),w=(0,l.oU)(r,"nav");let k,_,S=!1;const x=(0,o.useContext)(u.A),E=(0,o.useContext)(c.A);return x?(k=x.bsPrefix,S=null==g||g):E&&({cardHeaderBsPrefix:_}=E),(0,d.jsx)(s.A,{as:n,ref:t,activeKey:y,className:a()(v,{[w]:!S,[`${k}-nav`]:S,[`${k}-nav-scroll`]:S&&m,[`${_}-${f}`]:!!_,[`${w}-${f}`]:!!f,[`${w}-fill`]:h,[`${w}-justified`]:p}),...b})});g.displayName="Nav";const m=Object.assign(g,{Item:h,Link:p.A})},5043:(e,t,n)=>{"use strict";e.exports=n(4288)},5173:(e,t,n)=>{e.exports=n(1497)()},5425:(e,t,n)=>{"use strict";n.d(t,{sE:()=>o,y:()=>i});const r="data-rr-ui-",a="rrUi";function o(e){return`${r}${e}`}function i(e){return`${a}${e}`}},5632:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(5173),a=n.n(r),o=n(5043),i=n(8139),s=n.n(i),l=n(579);const u={"aria-label":a().string,onClick:a().func,variant:a().oneOf(["white"])},c=o.forwardRef((e,t)=>{let{className:n,variant:r,"aria-label":a="Close",...o}=e;return(0,l.jsx)("button",{ref:t,type:"button",className:s()("btn-close",r&&`btn-close-${r}`,n),"aria-label":a,...o})});c.displayName="CloseButton",c.propTypes=u;const d=c},5736:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(7374));class o extends a.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const n=Array.from(new Set(t)).map(e=>"string"===typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${n})`),this}contains(e,t){return"string"===typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"===typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"===typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t){let{config:n,type:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a="";"plain"===r?a="pl":"phrase"===r?a="ph":"websearch"===r&&(a="w");const o=void 0===n?"":`(${n})`;return this.url.searchParams.append(e,`${a}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(e=>{let[t,n]=e;this.url.searchParams.append(t,`eq.${n}`)}),this}not(e,t,n){return this.url.searchParams.append(e,`not.${t}.${n}`),this}or(e){let{foreignTable:t,referencedTable:n=t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=n?`${n}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,n){return this.url.searchParams.append(e,`${t}.${n}`),this}}t.default=o},5745:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;const r=n(400);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${r.version}`}},5896:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>o(l,n))u<a&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,g=!1,m=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function _(e){if(m=!1,k(e),!g)if(null!==r(u))g=!0,x||(x=!0,S());else{var t=r(c);null!==t&&R(_,t.startTime-e)}}var S,x=!1,E=-1,C=5,O=-1;function P(){return!!v||!(t.unstable_now()-O<C)}function T(){if(v=!1,x){var e=t.unstable_now();O=e;var n=!0;try{e:{g=!1,m&&(m=!1,b(E),E=-1),p=!0;var o=h;try{t:{for(k(e),f=r(u);null!==f&&!(f.expirationTime>e&&P());){var i=f.callback;if("function"===typeof i){f.callback=null,h=f.priorityLevel;var s=i(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof s){f.callback=s,k(e),n=!0;break t}f===r(u)&&a(u),k(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var l=r(c);null!==l&&R(_,l.startTime-e),n=!1}}break e}finally{f=null,h=o,p=!1}n=void 0}}finally{n?S():x=!1}}}if("function"===typeof w)S=function(){w(T)};else if("undefined"!==typeof MessageChannel){var A=new MessageChannel,j=A.port2;A.port1.onmessage=T,S=function(){j.postMessage(null)}}else S=function(){y(T,0)};function R(e,n){E=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_requestPaint=function(){v=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(m?(b(E),E=-1):m=!0,R(_,o-i))):(e.sortIndex=s,n(u,e),g||p||(g=!0,x||(x=!0,S()))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},5901:(e,t,n)=>{"use strict";n.d(t,{A:()=>o,u:()=>a});var r=n(5043);const a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return null!=e?String(e):t||null},o=r.createContext(null)},6218:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043);const a=function(e){const t=(0,r.useRef)(e);return(0,r.useEffect)(()=>{t.current=e},[e]),t}},6440:e=>{"use strict";var t=function(){};e.exports=t},6618:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const a=function(e){const t=(0,r.useRef)(e);return(0,r.useEffect)(()=>{t.current=e},[e]),t};function o(e){const t=a(e);return(0,r.useCallback)(function(){return t.current&&t.current(...arguments)},[t])}},6672:(e,t,n)=>{"use strict";var r=n(5043);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(a(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:s,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=i.p;try{if(l.T=null,i.p=2,e)return e()}finally{l.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,o="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:o}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:o,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},7004:(e,t,n)=>{"use strict";var r=n(8853),a=n(5043),o=n(7950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(l(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,h=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),g=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),k=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),x=Symbol.for("react.suspense_list"),E=Symbol.for("react.memo"),C=Symbol.for("react.lazy");Symbol.for("react.scope");var O=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var T=Symbol.iterator;function A(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=T&&e[T]||e["@@iterator"])?e:null}var j=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===j?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case m:return"Fragment";case y:return"Profiler";case v:return"StrictMode";case S:return"Suspense";case x:return"SuspenseList";case O:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case g:return"Portal";case k:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case _:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case E:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case C:t=e._payload,e=e._init;try{return R(e(t))}catch(n){}}return null}var N=Array.isArray,L=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},I=[],z=-1;function M(e){return{current:e}}function F(e){0>z||(e.current=I[z],I[z]=null,z--)}function U(e,t){z++,I[z]=e.current,e.current=t}var B=M(null),H=M(null),W=M(null),q=M(null);function V(e,t){switch(U(W,t),U(H,e),U(B,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=od(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(B),U(B,e)}function K(){F(B),F(H),F(W)}function J(e){null!==e.memoizedState&&U(q,e);var t=B.current,n=od(t,e.type);t!==n&&(U(H,e),U(B,n))}function Y(e){H.current===e&&(F(B),F(H)),q.current===e&&(F(q),Jd._currentValue=D)}var G=Object.prototype.hasOwnProperty,Q=r.unstable_scheduleCallback,X=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,oe=r.unstable_NormalPriority,ie=r.unstable_LowPriority,se=r.unstable_IdlePriority,le=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof le&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var he=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(pe(e)/ge|0)|0},pe=Math.log,ge=Math.LN2;var me=256,ve=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,o=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var s=134217727&r;return 0!==s?0!==(r=s&~o)?a=ye(r):0!==(i&=s)?a=ye(i):n||0!==(n=s&~e)&&(a=ye(n)):0!==(s=r&~o)?a=ye(s):0!==i?a=ye(i):n||0!==(n=r&~e)&&(a=ye(n)),0===a?0:0!==t&&t!==a&&0===(t&o)&&((o=a&-a)>=(n=t&-t)||32===o&&0!==(4194048&n))?t:a}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function ke(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function _e(){var e=me;return 0===(4194048&(me<<=1))&&(me=256),e}function Se(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function xe(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ee(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ce(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-he(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Oe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-he(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Te(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Ae(){var e=$.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var je=Math.random().toString(36).slice(2),Re="__reactFiber$"+je,Ne="__reactProps$"+je,Le="__reactContainer$"+je,$e="__reactEvents$"+je,De="__reactListeners$"+je,Ie="__reactHandles$"+je,ze="__reactResources$"+je,Me="__reactMarker$"+je;function Fe(e){delete e[Re],delete e[Ne],delete e[$e],delete e[De],delete e[Ie]}function Ue(e){var t=e[Re];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Le]||n[Re]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Re])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function Be(e){if(e=e[Re]||e[Le]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function We(e){var t=e[ze];return t||(t=e[ze]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function qe(e){e[Me]=!0}var Ve=new Set,Ke={};function Je(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for(Ke[e]=t,e=0;e<t.length;e++)Ve.add(t[e])}var Ge,Qe,Xe=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,G.call(et,a)||!G.call(Ze,a)&&(Xe.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ge)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ge=t&&t[1]||"",Qe=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ge+e+Qe}var ot=!1;function it(e,t){if(!e||ot)return"";ot=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(o){r=o}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(s){if(s&&r&&"string"===typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=r.DetermineComponentFrameRoot(),i=o[0],s=o[1];if(i&&s){var l=i.split("\n"),u=s.split("\n");for(a=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===l.length||a===u.length)for(r=l.length-1,a=u.length-1;1<=r&&0<=a&&l[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(l[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||l[r]!==u[a]){var c="\n"+l[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{ot=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function st(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return at("Activity");default:return""}}function lt(e){try{var t="";do{t+=st(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function ht(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function gt(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function mt(e,t,n,r,a,o,i,s){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?yt(e,i,ut(t)):null!=n?yt(e,i,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=o&&(e.defaultChecked=!!o),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.name=""+ut(s):e.removeAttribute("name")}function vt(e,t,n,r,a,o,i,s){if(null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.type=o),null!=t||null!=n){if(!("submit"!==o&&"reset"!==o||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,s||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function yt(e,t,n){"number"===t&&ht(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function kt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(N(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function _t(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function xt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Et(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&xt(e,a,r)}else for(var o in t)t.hasOwnProperty(o)&&xt(e,o,t[o])}function Ct(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ot=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Tt(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var At=null;function jt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Rt=null,Nt=null;function Lt(e){var t=Be(e);if(t&&(e=t.stateNode)){var n=e[Ne]||null;e:switch(e=t.stateNode,t.type){case"input":if(mt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+gt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Ne]||null;if(!a)throw Error(i(90));mt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var $t=!1;function Dt(e,t,n){if($t)return e(t,n);$t=!0;try{return e(t)}finally{if($t=!1,(null!==Rt||null!==Nt)&&(Uu(),Rt&&(t=Rt,e=Nt,Nt=Rt=null,Lt(t),e)))for(t=0;t<e.length;t++)Lt(e[t])}}function It(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Ne]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var zt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Mt=!1;if(zt)try{var Ft={};Object.defineProperty(Ft,"passive",{get:function(){Mt=!0}}),window.addEventListener("test",Ft,Ft),window.removeEventListener("test",Ft,Ft)}catch(Lf){Mt=!1}var Ut=null,Bt=null,Ht=null;function Wt(){if(Ht)return Ht;var e,t,n=Bt,r=n.length,a="value"in Ut?Ut.value:Ut.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return Ht=a.slice(e,1<t?1-t:void 0)}function qt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Vt(){return!0}function Kt(){return!1}function Jt(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Vt:Kt,this.isPropagationStopped=Kt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vt)},persist:function(){},isPersistent:Vt}),t}var Yt,Gt,Qt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Jt(Xt),en=f({},Xt,{view:0,detail:0}),tn=Jt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Qt&&(Qt&&"mousemove"===e.type?(Yt=e.screenX-Qt.screenX,Gt=e.screenY-Qt.screenY):Gt=Yt=0,Qt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Gt}}),rn=Jt(nn),an=Jt(f({},nn,{dataTransfer:0})),on=Jt(f({},en,{relatedTarget:0})),sn=Jt(f({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Jt(f({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Jt(f({},Xt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function pn(){return hn}var gn=Jt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=qt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?qt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?qt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),mn=Jt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vn=Jt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),yn=Jt(f({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Jt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Jt(f({},Xt,{newState:0,oldState:0})),kn=[9,13,27,32],_n=zt&&"CompositionEvent"in window,Sn=null;zt&&"documentMode"in document&&(Sn=document.documentMode);var xn=zt&&"TextEvent"in window&&!Sn,En=zt&&(!_n||Sn&&8<Sn&&11>=Sn),Cn=String.fromCharCode(32),On=!1;function Pn(e,t){switch(e){case"keyup":return-1!==kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var An=!1;var jn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!jn[e.type]:"textarea"===t}function Nn(e,t,n,r){Rt?Nt?Nt.push(r):Nt=[r]:Rt=r,0<(t=Wc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ln=null,$n=null;function Dn(e){Dc(e,0)}function In(e){if(ft(He(e)))return e}function zn(e,t){if("change"===e)return t}var Mn=!1;if(zt){var Fn;if(zt){var Un="oninput"in document;if(!Un){var Bn=document.createElement("div");Bn.setAttribute("oninput","return;"),Un="function"===typeof Bn.oninput}Fn=Un}else Fn=!1;Mn=Fn&&(!document.documentMode||9<document.documentMode)}function Hn(){Ln&&(Ln.detachEvent("onpropertychange",Wn),$n=Ln=null)}function Wn(e){if("value"===e.propertyName&&In($n)){var t=[];Nn(t,$n,e,jt(e)),Dt(Dn,t)}}function qn(e,t,n){"focusin"===e?(Hn(),$n=n,(Ln=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&Hn()}function Vn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return In($n)}function Kn(e,t){if("click"===e)return In(t)}function Jn(e,t){if("input"===e||"change"===e)return In(t)}var Yn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Gn(e,t){if(Yn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!G.call(t,a)||!Yn(e[a],t[a]))return!1}return!0}function Qn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Qn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Qn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=ht((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=ht((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=zt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,or=null,ir=!1;function sr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==ht(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},or&&Gn(or,r)||(or=r,0<(r=Wc(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}zt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var hr=fr("animationend"),pr=fr("animationiteration"),gr=fr("animationstart"),mr=fr("transitionrun"),vr=fr("transitionstart"),yr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _r(e,t){wr.set(e,t),Je(t,[e])}kr.push("scrollEnd");var Sr=new WeakMap;function xr(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var Er=[],Cr=0,Or=0;function Pr(){for(var e=Cr,t=Or=Cr=0;t<e;){var n=Er[t];Er[t++]=null;var r=Er[t];Er[t++]=null;var a=Er[t];Er[t++]=null;var o=Er[t];if(Er[t++]=null,null!==r&&null!==a){var i=r.pending;null===i?a.next=a:(a.next=i.next,i.next=a),r.pending=a}0!==o&&Rr(n,a,o)}}function Tr(e,t,n,r){Er[Cr++]=e,Er[Cr++]=t,Er[Cr++]=n,Er[Cr++]=r,Or|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Ar(e,t,n,r){return Tr(e,t,n,r),Nr(e)}function jr(e,t){return Tr(e,null,null,t),Nr(e)}function Rr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,o=e.return;null!==o;)o.childLanes|=n,null!==(r=o.alternate)&&(r.childLanes|=n),22===o.tag&&(null===(e=o.stateNode)||1&e._visibility||(a=!0)),e=o,o=o.return;return 3===e.tag?(o=e.stateNode,a&&null!==t&&(a=31-he(n),null===(r=(e=o.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),o):null}function Nr(e){if(50<Ru)throw Ru=0,Nu=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Lr={};function $r(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,t,n,r){return new $r(e,t,n,r)}function Ir(e){return!(!(e=e.prototype)||!e.isReactComponent)}function zr(e,t){var n=e.alternate;return null===n?((n=Dr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Mr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Fr(e,t,n,r,a,o){var s=0;if(r=e,"function"===typeof e)Ir(e)&&(s=1);else if("string"===typeof e)s=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,B.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case O:return(e=Dr(31,n,t,a)).elementType=O,e.lanes=o,e;case m:return Ur(n.children,a,o,t);case v:s=8,a|=24;break;case y:return(e=Dr(12,n,t,2|a)).elementType=y,e.lanes=o,e;case S:return(e=Dr(13,n,t,a)).elementType=S,e.lanes=o,e;case x:return(e=Dr(19,n,t,a)).elementType=x,e.lanes=o,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case k:s=10;break e;case w:s=9;break e;case _:s=11;break e;case E:s=14;break e;case C:s=16,r=null;break e}s=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=Dr(s,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Ur(e,t,n,r){return(e=Dr(7,e,r,t)).lanes=n,e}function Br(e,t,n){return(e=Dr(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=Dr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],qr=0,Vr=null,Kr=0,Jr=[],Yr=0,Gr=null,Qr=1,Xr="";function Zr(e,t){Wr[qr++]=Kr,Wr[qr++]=Vr,Vr=e,Kr=t}function ea(e,t,n){Jr[Yr++]=Qr,Jr[Yr++]=Xr,Jr[Yr++]=Gr,Gr=e;var r=Qr;e=Xr;var a=32-he(r)-1;r&=~(1<<a),n+=1;var o=32-he(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Qr=1<<32-he(t)+a|n<<a|r,Xr=o+e}else Qr=1<<o|n<<a|r,Xr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Vr;)Vr=Wr[--qr],Wr[qr]=null,Kr=Wr[--qr],Wr[qr]=null;for(;e===Gr;)Gr=Jr[--Yr],Jr[Yr]=null,Xr=Jr[--Yr],Jr[Yr]=null,Qr=Jr[--Yr],Jr[Yr]=null}var ra=null,aa=null,oa=!1,ia=null,sa=!1,la=Error(i(519));function ua(e){throw ga(xr(Error(i(418,"")),e)),la}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Re]=e,t[Ne]=r,n){case"dialog":Ic("cancel",t),Ic("close",t);break;case"iframe":case"object":case"embed":Ic("load",t);break;case"video":case"audio":for(n=0;n<Lc.length;n++)Ic(Lc[n],t);break;case"source":Ic("error",t);break;case"img":case"image":case"link":Ic("error",t),Ic("load",t);break;case"details":Ic("toggle",t);break;case"input":Ic("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Ic("invalid",t);break;case"textarea":Ic("invalid",t),kt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Gc(t.textContent,n)?(null!=r.popover&&(Ic("beforetoggle",t),Ic("toggle",t)),null!=r.onScroll&&Ic("scroll",t),null!=r.onScrollEnd&&Ic("scrollend",t),null!=r.onClick&&(t.onclick=Qc),t=!0):t=!1,t||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(sa=!1);case 27:case 3:return void(sa=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!oa)return da(e),oa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&aa&&ua(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=vd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,hd(e.type)?(e=yd,yd=null,aa=e):aa=n):aa=ra?vd(e.stateNode.nextSibling):null;return!0}function ha(){aa=ra=null,oa=!1}function pa(){var e=ia;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),ia=null),e}function ga(e){null===ia?ia=[e]:ia.push(e)}var ma=M(null),va=null,ya=null;function ba(e,t,n){U(ma,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=ma.current,F(ma)}function ka(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _a(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var s=a.child;o=o.firstContext;e:for(;null!==o;){var l=o;o=a;for(var u=0;u<t.length;u++)if(l.context===t[u]){o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),ka(o.return,n,e),r||(s=null);break e}o=l.next}}else if(18===a.tag){if(null===(s=a.return))throw Error(i(341));s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),ka(s,n,e),s=null}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===e){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}}function Sa(e,t,n,r){e=null;for(var a=t,o=!1;null!==a;){if(!o)if(0!==(524288&a.flags))o=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var s=a.alternate;if(null===s)throw Error(i(387));if(null!==(s=s.memoizedProps)){var l=a.type;Yn(a.pendingProps.value,s.value)||(null!==e?e.push(l):e=[l])}}else if(a===q.current){if(null===(s=a.alternate))throw Error(i(387));s.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Jd):e=[Jd])}a=a.return}null!==e&&_a(t,e,n,r),t.flags|=262144}function xa(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ea(e){va=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ca(e){return Pa(va,e)}function Oa(e,t){return null===va&&Ea(e),Pa(e,t)}function Pa(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ya){if(null===e)throw Error(i(308));ya=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ya=ya.next=t;return n}var Ta="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Aa=r.unstable_scheduleCallback,ja=r.unstable_NormalPriority,Ra={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Na(){return{controller:new Ta,data:new Map,refCount:0}}function La(e){e.refCount--,0===e.refCount&&Aa(ja,function(){e.controller.abort()})}var $a=null,Da=0,Ia=0,za=null;function Ma(){if(0===--Da&&null!==$a){null!==za&&(za.status="fulfilled");var e=$a;$a=null,Ia=0,za=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Fa=L.S;L.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===$a){var n=$a=[];Da=0,Ia=Tc(),za={status:"pending",value:void 0,then:function(e){n.push(e)}}}Da++,t.then(Ma,Ma)}(0,t),null!==Fa&&Fa(e,t)};var Ua=M(null);function Ba(){var e=Ua.current;return null!==e?e:ru.pooledCache}function Ha(e,t){U(Ua,null===t?Ua.current:t.pool)}function Wa(){var e=Ba();return null===e?null:{parent:Ra._currentValue,pool:e}}var qa=Error(i(460)),Va=Error(i(474)),Ka=Error(i(542)),Ja={then:function(){}};function Ya(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ga(){}function Qa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ga,Ga),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e;default:if("string"===typeof t.status)t.then(Ga,Ga);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e}throw Xa=t,qa}}var Xa=null;function Za(){if(null===Xa)throw Error(i(459));var e=Xa;return Xa=null,e}function eo(e){if(e===qa||e===Ka)throw Error(i(483))}var to=!1;function no(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ro(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ao(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function oo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nu)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Nr(e),Rr(e,null,n),t}return Tr(e,r,t,n),Nr(e)}function io(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}function so(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var lo=!1;function uo(){if(lo){if(null!==za)throw za}}function co(e,t,n,r){lo=!1;var a=e.updateQueue;to=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,u=l.next;l.next=null,null===i?o=u:i.next=u,i=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=a.baseState;for(i=0,c=u=l=null,s=o;;){var h=-536870913&s.lane,p=h!==s.lane;if(p?(ou&h)===h:(r&h)===h){0!==h&&h===Ia&&(lo=!0),null!==c&&(c=c.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});e:{var g=e,m=s;h=t;var v=n;switch(m.tag){case 1:if("function"===typeof(g=m.payload)){d=g.call(v,d,h);break e}d=g;break e;case 3:g.flags=-65537&g.flags|128;case 0:if(null===(h="function"===typeof(g=m.payload)?g.call(v,d,h):g)||void 0===h)break e;d=f({},d,h);break e;case 2:to=!0}}null!==(h=s.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=a.callbacks)?a.callbacks=[h]:p.push(h))}else p={lane:h,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=p,l=d):c=c.next=p,i|=h;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(p=s).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}null===c&&(l=d),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===o&&(a.shared.lanes=0),hu|=i,e.lanes=i,e.memoizedState=d}}function fo(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function ho(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fo(n[e],t)}var po=M(null),go=M(0);function mo(e,t){U(go,e=du),U(po,t),du=e|t.baseLanes}function vo(){U(go,du),U(po,po.current)}function yo(){du=go.current,F(po),F(go)}var bo=0,wo=null,ko=null,_o=null,So=!1,xo=!1,Eo=!1,Co=0,Oo=0,Po=null,To=0;function Ao(){throw Error(i(321))}function jo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function Ro(e,t,n,r,a,o){return bo=o,wo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=null===e||null===e.memoizedState?Vi:Ki,Eo=!1,o=n(r,a),Eo=!1,xo&&(o=Lo(t,n,r,a)),No(e),o}function No(e){L.H=qi;var t=null!==ko&&null!==ko.next;if(bo=0,_o=ko=wo=null,So=!1,Oo=0,Po=null,t)throw Error(i(300));null===e||Os||null!==(e=e.dependencies)&&xa(e)&&(Os=!0)}function Lo(e,t,n,r){wo=e;var a=0;do{if(xo&&(Po=null),Oo=0,xo=!1,25<=a)throw Error(i(301));if(a+=1,_o=ko=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}L.H=Ji,o=t(n,r)}while(xo);return o}function $o(){var e=L.H,t=e.useState()[0];return t="function"===typeof t.then?Uo(t):t,e=e.useState()[0],(null!==ko?ko.memoizedState:null)!==e&&(wo.flags|=1024),t}function Do(){var e=0!==Co;return Co=0,e}function Io(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function zo(e){if(So){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}So=!1}bo=0,_o=ko=wo=null,xo=!1,Oo=Co=0,Po=null}function Mo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===_o?wo.memoizedState=_o=e:_o=_o.next=e,_o}function Fo(){if(null===ko){var e=wo.alternate;e=null!==e?e.memoizedState:null}else e=ko.next;var t=null===_o?wo.memoizedState:_o.next;if(null!==t)_o=t,ko=e;else{if(null===e){if(null===wo.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(ko=e).memoizedState,baseState:ko.baseState,baseQueue:ko.baseQueue,queue:ko.queue,next:null},null===_o?wo.memoizedState=_o=e:_o=_o.next=e}return _o}function Uo(e){var t=Oo;return Oo+=1,null===Po&&(Po=[]),e=Qa(Po,e,t),t=wo,null===(null===_o?t.memoizedState:_o.next)&&(t=t.alternate,L.H=null===t||null===t.memoizedState?Vi:Ki),e}function Bo(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Uo(e);if(e.$$typeof===k)return Ca(e)}throw Error(i(438,String(e)))}function Ho(e){var t=null,n=wo.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wo.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=P;return t.index++,n}function Wo(e,t){return"function"===typeof t?t(e):t}function qo(e){return Vo(Fo(),ko,e)}function Vo(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,o=r.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}t.baseQueue=a=o,r.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var l=s=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(ou&f)===f:(bo&f)===f){var h=c.revertLane;if(0===h)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Ia&&(d=!0);else{if((bo&h)===h){c=c.next,h===Ia&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=f,s=o):u=u.next=f,wo.lanes|=h,hu|=h}f=c.action,Eo&&n(o,f),o=c.hasEagerState?c.eagerState:n(o,f)}else h={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=h,s=o):u=u.next=h,wo.lanes|=f,hu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?s=o:u.next=l,!Yn(o,e.memoizedState)&&(Os=!0,d&&null!==(n=za)))throw n;e.memoizedState=o,e.baseState=s,e.baseQueue=u,r.lastRenderedState=o}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ko(e){var t=Fo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=e(o,s.action),s=s.next}while(s!==a);Yn(o,t.memoizedState)||(Os=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Jo(e,t,n){var r=wo,a=Fo(),o=oa;if(o){if(void 0===n)throw Error(i(407));n=n()}else n=t();var s=!Yn((ko||a).memoizedState,n);if(s&&(a.memoizedState=n,Os=!0),a=a.queue,vi(2048,8,Qo.bind(null,r,a,e),[e]),a.getSnapshot!==t||s||null!==_o&&1&_o.memoizedState.tag){if(r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Go.bind(null,r,a,n,t),null),null===ru)throw Error(i(349));o||0!==(124&bo)||Yo(r,t,n)}return n}function Yo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wo.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Go(e,t,n,r){t.value=n,t.getSnapshot=r,Xo(t)&&Zo(e)}function Qo(e,t,n){return n(function(){Xo(t)&&Zo(e)})}function Xo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function Zo(e){var t=jr(e,2);null!==t&&Du(t,e,2)}function ei(e){var t=Mo();if("function"===typeof e){var n=e;if(e=n(),Eo){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:e},t}function ti(e,t,n,r){return e.baseState=n,Vo(e,ko,"function"===typeof r?r:Wo)}function ni(e,t,n,r,a){if(Bi(e))throw Error(i(485));if(null!==(e=t.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==L.T?n(!0):o.isTransition=!1,r(o),null===(n=t.pending)?(o.next=t.pending=o,ri(t,o)):(o.next=n.next,t.pending=n.next=o)}}function ri(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var o=L.T,i={};L.T=i;try{var s=n(a,r),l=L.S;null!==l&&l(i,s),ai(e,t,s)}catch(u){ii(e,t,u)}finally{L.T=o}}else try{ai(e,t,o=n(a,r))}catch(c){ii(e,t,c)}}function ai(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){oi(e,t,n)},function(n){return ii(e,t,n)}):oi(e,t,n)}function oi(e,t,n){t.status="fulfilled",t.value=n,si(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ri(e,n)))}function ii(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,si(t),t=t.next}while(t!==r)}e.action=null}function si(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function li(e,t){return t}function ui(e,t){if(oa){var n=ru.formState;if(null!==n){e:{var r=wo;if(oa){if(aa){t:{for(var a=aa,o=sa;8!==a.nodeType;){if(!o){a=null;break t}if(null===(a=vd(a.nextSibling))){a=null;break t}}a="F!"===(o=a.data)||"F"===o?a:null}if(a){aa=vd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Mo()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:li,lastRenderedState:t},n.queue=r,n=Mi.bind(null,wo,r),r.dispatch=n,r=ei(!1),o=Ui.bind(null,wo,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Mo()).queue=a,n=ni.bind(null,wo,a,o,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ci(e){return di(Fo(),ko,e)}function di(e,t,n){if(t=Vo(e,t,li)[0],e=qo(Wo)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Uo(t)}catch(i){if(i===qa)throw Ka;throw i}else r=t;var a=(t=Fo()).queue,o=a.dispatch;return n!==t.memoizedState&&(wo.flags|=2048,pi(9,{destroy:void 0,resource:void 0},fi.bind(null,a,n),null)),[r,o,e]}function fi(e,t){e.action=t}function hi(e){var t=Fo(),n=ko;if(null!==n)return di(t,n,e);Fo(),t=t.memoizedState;var r=(n=Fo()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function pi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wo.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function gi(){return Fo().memoizedState}function mi(e,t,n,r){var a=Mo();r=void 0===r?null:r,wo.flags|=e,a.memoizedState=pi(1|t,{destroy:void 0,resource:void 0},n,r)}function vi(e,t,n,r){var a=Fo();r=void 0===r?null:r;var o=a.memoizedState.inst;null!==ko&&null!==r&&jo(r,ko.memoizedState.deps)?a.memoizedState=pi(t,o,n,r):(wo.flags|=e,a.memoizedState=pi(1|t,o,n,r))}function yi(e,t){mi(8390656,8,e,t)}function bi(e,t){vi(2048,8,e,t)}function wi(e,t){return vi(4,2,e,t)}function ki(e,t){return vi(4,4,e,t)}function _i(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function Si(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,vi(4,4,_i.bind(null,t,e),n)}function xi(){}function Ei(e,t){var n=Fo();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&jo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ci(e,t){var n=Fo();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&jo(t,r[1]))return r[0];if(r=e(),Eo){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Oi(e,t,n){return void 0===n||0!==(1073741824&bo)?e.memoizedState=t:(e.memoizedState=n,e=$u(),wo.lanes|=e,hu|=e,n)}function Pi(e,t,n,r){return Yn(n,t)?n:null!==po.current?(e=Oi(e,n,r),Yn(e,t)||(Os=!0),e):0===(42&bo)?(Os=!0,e.memoizedState=n):(e=$u(),wo.lanes|=e,hu|=e,t)}function Ti(e,t,n,r,a){var o=$.p;$.p=0!==o&&8>o?o:8;var i=L.T,s={};L.T=s,Ui(e,!1,t,n);try{var l=a(),u=L.S;if(null!==u&&u(s,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Fi(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(l,r),Lu());else Fi(e,t,r,Lu())}catch(c){Fi(e,t,{then:function(){},status:"rejected",reason:c},Lu())}finally{$.p=o,L.T=i}}function Ai(){}function ji(e,t,n,r){if(5!==e.tag)throw Error(i(476));var a=Ri(e).queue;Ti(e,a,t,D,null===n?Ai:function(){return Ni(e),n(r)})}function Ri(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:D},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ni(e){Fi(e,Ri(e).next.queue,{},Lu())}function Li(){return Ca(Jd)}function $i(){return Fo().memoizedState}function Di(){return Fo().memoizedState}function Ii(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Lu(),r=oo(t,e=ao(n),n);return null!==r&&(Du(r,t,n),io(r,t,n)),t={cache:Na()},void(e.payload=t)}t=t.return}}function zi(e,t,n){var r=Lu();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Bi(e)?Hi(t,n):null!==(n=Ar(e,t,n,r))&&(Du(n,e,r),Wi(n,t,r))}function Mi(e,t,n){Fi(e,t,n,Lu())}function Fi(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Bi(e))Hi(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=o(i,n);if(a.hasEagerState=!0,a.eagerState=s,Yn(s,i))return Tr(e,t,a,0),null===ru&&Pr(),!1}catch(l){}if(null!==(n=Ar(e,t,a,r)))return Du(n,e,r),Wi(n,t,r),!0}return!1}function Ui(e,t,n,r){if(r={lane:2,revertLane:Tc(),action:r,hasEagerState:!1,eagerState:null,next:null},Bi(e)){if(t)throw Error(i(479))}else null!==(t=Ar(e,n,r,2))&&Du(t,e,2)}function Bi(e){var t=e.alternate;return e===wo||null!==t&&t===wo}function Hi(e,t){xo=So=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}var qi={readContext:Ca,use:Bo,useCallback:Ao,useContext:Ao,useEffect:Ao,useImperativeHandle:Ao,useLayoutEffect:Ao,useInsertionEffect:Ao,useMemo:Ao,useReducer:Ao,useRef:Ao,useState:Ao,useDebugValue:Ao,useDeferredValue:Ao,useTransition:Ao,useSyncExternalStore:Ao,useId:Ao,useHostTransitionStatus:Ao,useFormState:Ao,useActionState:Ao,useOptimistic:Ao,useMemoCache:Ao,useCacheRefresh:Ao},Vi={readContext:Ca,use:Bo,useCallback:function(e,t){return Mo().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:yi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,mi(4194308,4,_i.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){mi(4,2,e,t)},useMemo:function(e,t){var n=Mo();t=void 0===t?null:t;var r=e();if(Eo){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Mo();if(void 0!==n){var a=n(t);if(Eo){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=zi.bind(null,wo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Mo().memoizedState=e},useState:function(e){var t=(e=ei(e)).queue,n=Mi.bind(null,wo,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:xi,useDeferredValue:function(e,t){return Oi(Mo(),e,t)},useTransition:function(){var e=ei(!1);return e=Ti.bind(null,wo,e.queue,!0,!1),Mo().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=wo,a=Mo();if(oa){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===ru)throw Error(i(349));0!==(124&ou)||Yo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,yi(Qo.bind(null,r,o,e),[e]),r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Go.bind(null,r,o,n,t),null),n},useId:function(){var e=Mo(),t=ru.identifierPrefix;if(oa){var n=Xr;t="\xab"+t+"R"+(n=(Qr&~(1<<32-he(Qr)-1)).toString(32)+n),0<(n=Co++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=To++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Li,useFormState:ui,useActionState:ui,useOptimistic:function(e){var t=Mo();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Ui.bind(null,wo,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ho,useCacheRefresh:function(){return Mo().memoizedState=Ii.bind(null,wo)}},Ki={readContext:Ca,use:Bo,useCallback:Ei,useContext:Ca,useEffect:bi,useImperativeHandle:Si,useInsertionEffect:wi,useLayoutEffect:ki,useMemo:Ci,useReducer:qo,useRef:gi,useState:function(){return qo(Wo)},useDebugValue:xi,useDeferredValue:function(e,t){return Pi(Fo(),ko.memoizedState,e,t)},useTransition:function(){var e=qo(Wo)[0],t=Fo().memoizedState;return["boolean"===typeof e?e:Uo(e),t]},useSyncExternalStore:Jo,useId:$i,useHostTransitionStatus:Li,useFormState:ci,useActionState:ci,useOptimistic:function(e,t){return ti(Fo(),0,e,t)},useMemoCache:Ho,useCacheRefresh:Di},Ji={readContext:Ca,use:Bo,useCallback:Ei,useContext:Ca,useEffect:bi,useImperativeHandle:Si,useInsertionEffect:wi,useLayoutEffect:ki,useMemo:Ci,useReducer:Ko,useRef:gi,useState:function(){return Ko(Wo)},useDebugValue:xi,useDeferredValue:function(e,t){var n=Fo();return null===ko?Oi(n,e,t):Pi(n,ko.memoizedState,e,t)},useTransition:function(){var e=Ko(Wo)[0],t=Fo().memoizedState;return["boolean"===typeof e?e:Uo(e),t]},useSyncExternalStore:Jo,useId:$i,useHostTransitionStatus:Li,useFormState:hi,useActionState:hi,useOptimistic:function(e,t){var n=Fo();return null!==ko?ti(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ho,useCacheRefresh:Di},Yi=null,Gi=0;function Qi(e){var t=Gi;return Gi+=1,null===Yi&&(Yi=[]),Qa(Yi,e,t)}function Xi(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zi(e,t){if(t.$$typeof===h)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function es(e){return(0,e._init)(e._payload)}function ts(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=zr(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Br(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===m?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===C&&es(o)===t.type)?(Xi(t=a(t,n.props),n),t.return=e,t):(Xi(t=Fr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Ur(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Br(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case p:return Xi(n=Fr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case g:return(t=Hr(t,e.mode,n)).return=e,t;case C:return f(e,t=(0,t._init)(t._payload),n)}if(N(t)||A(t))return(t=Ur(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Qi(t),n);if(t.$$typeof===k)return f(e,Oa(e,t),n);Zi(e,t)}return null}function h(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?u(e,t,n,r):null;case g:return n.key===a?c(e,t,n,r):null;case C:return h(e,t,n=(a=n._init)(n._payload),r)}if(N(n)||A(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return h(e,t,Qi(n),r);if(n.$$typeof===k)return h(e,t,Oa(e,n),r);Zi(e,n)}return null}function v(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case p:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case g:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case C:return v(e,t,n,r=(0,r._init)(r._payload),a)}if(N(r)||A(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return v(e,t,n,Qi(r),a);if(r.$$typeof===k)return v(e,t,n,Oa(t,r),a);Zi(t,r)}return null}function y(l,u,c,d){if("object"===typeof c&&null!==c&&c.type===m&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===m){if(7===u.tag){n(l,u.sibling),(d=a(u,c.props.children)).return=l,l=d;break e}}else if(u.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===C&&es(b)===u.type){n(l,u.sibling),Xi(d=a(u,c.props),c),d.return=l,l=d;break e}n(l,u);break}t(l,u),u=u.sibling}c.type===m?((d=Ur(c.props.children,l.mode,d,c.key)).return=l,l=d):(Xi(d=Fr(c.type,c.key,c.props,null,l.mode,d),c),d.return=l,l=d)}return s(l);case g:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(l,u.sibling),(d=a(u,c.children||[])).return=l,l=d;break e}n(l,u);break}t(l,u),u=u.sibling}(d=Hr(c,l.mode,d)).return=l,l=d}return s(l);case C:return y(l,u,c=(b=c._init)(c._payload),d)}if(N(c))return function(a,i,s,l){for(var u=null,c=null,d=i,p=i=0,g=null;null!==d&&p<s.length;p++){d.index>p?(g=d,d=null):g=d.sibling;var m=h(a,d,s[p],l);if(null===m){null===d&&(d=g);break}e&&d&&null===m.alternate&&t(a,d),i=o(m,i,p),null===c?u=m:c.sibling=m,c=m,d=g}if(p===s.length)return n(a,d),oa&&Zr(a,p),u;if(null===d){for(;p<s.length;p++)null!==(d=f(a,s[p],l))&&(i=o(d,i,p),null===c?u=d:c.sibling=d,c=d);return oa&&Zr(a,p),u}for(d=r(d);p<s.length;p++)null!==(g=v(d,a,p,s[p],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?p:g.key),i=o(g,i,p),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),oa&&Zr(a,p),u}(l,u,c,d);if(A(c)){if("function"!==typeof(b=A(c)))throw Error(i(150));return function(a,s,l,u){if(null==l)throw Error(i(151));for(var c=null,d=null,p=s,g=s=0,m=null,y=l.next();null!==p&&!y.done;g++,y=l.next()){p.index>g?(m=p,p=null):m=p.sibling;var b=h(a,p,y.value,u);if(null===b){null===p&&(p=m);break}e&&p&&null===b.alternate&&t(a,p),s=o(b,s,g),null===d?c=b:d.sibling=b,d=b,p=m}if(y.done)return n(a,p),oa&&Zr(a,g),c;if(null===p){for(;!y.done;g++,y=l.next())null!==(y=f(a,y.value,u))&&(s=o(y,s,g),null===d?c=y:d.sibling=y,d=y);return oa&&Zr(a,g),c}for(p=r(p);!y.done;g++,y=l.next())null!==(y=v(p,a,g,y.value,u))&&(e&&null!==y.alternate&&p.delete(null===y.key?g:y.key),s=o(y,s,g),null===d?c=y:d.sibling=y,d=y);return e&&p.forEach(function(e){return t(a,e)}),oa&&Zr(a,g),c}(l,u,c=b.call(c),d)}if("function"===typeof c.then)return y(l,u,Qi(c),d);if(c.$$typeof===k)return y(l,u,Oa(l,c),d);Zi(l,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(l,u.sibling),(d=a(u,c)).return=l,l=d):(n(l,u),(d=Br(c,l.mode,d)).return=l,l=d),s(l)):n(l,u)}return function(e,t,n,r){try{Gi=0;var a=y(e,t,n,r);return Yi=null,a}catch(i){if(i===qa||i===Ka)throw i;var o=Dr(29,i,null,e.mode);return o.lanes=r,o.return=e,o}}}var ns=ts(!0),rs=ts(!1),as=M(null),os=null;function is(e){var t=e.alternate;U(cs,1&cs.current),U(as,e),null===os&&(null===t||null!==po.current||null!==t.memoizedState)&&(os=e)}function ss(e){if(22===e.tag){if(U(cs,cs.current),U(as,e),null===os){var t=e.alternate;null!==t&&null!==t.memoizedState&&(os=e)}}else ls()}function ls(){U(cs,cs.current),U(as,as.current)}function us(e){F(as),os===e&&(os=null),F(cs)}var cs=M(0);function ds(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||md(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var hs={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Lu(),a=ao(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Du(t,e,r),io(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Lu(),a=ao(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Du(t,e,r),io(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Lu(),r=ao(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=oo(e,r,n))&&(Du(t,e,n),io(t,e,n))}};function ps(e,t,n,r,a,o,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!Gn(n,r)||!Gn(a,o))}function gs(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&hs.enqueueReplaceState(t,t.state,null)}function ms(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var vs="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function ys(e){vs(e)}function bs(e){console.error(e)}function ws(e){vs(e)}function ks(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function _s(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ss(e,t,n){return(n=ao(n)).tag=3,n.payload={element:null},n.callback=function(){ks(e,t)},n}function xs(e){return(e=ao(e)).tag=3,e}function Es(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var o=r.value;e.payload=function(){return a(o)},e.callback=function(){_s(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){_s(t,n,r),"function"!==typeof a&&(null===xu?xu=new Set([this]):xu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Cs=Error(i(461)),Os=!1;function Ps(e,t,n,r){t.child=null===e?rs(t,null,n,r):ns(t,e.child,n,r)}function Ts(e,t,n,r,a){n=n.render;var o=t.ref;if("ref"in r){var i={};for(var s in r)"ref"!==s&&(i[s]=r[s])}else i=r;return Ea(t),r=Ro(e,t,n,i,o,a),s=Do(),null===e||Os?(oa&&s&&ta(t),t.flags|=1,Ps(e,t,r,a),t.child):(Io(e,t,a),Ys(e,t,a))}function As(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Ir(o)||void 0!==o.defaultProps||null!==n.compare?((e=Fr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,js(e,t,o,r,a))}if(o=e.child,!Gs(e,a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:Gn)(i,r)&&e.ref===t.ref)return Ys(e,t,a)}return t.flags|=1,(e=zr(o,r)).ref=t.ref,e.return=t,t.child=e}function js(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(Gn(o,r)&&e.ref===t.ref){if(Os=!1,t.pendingProps=r=o,!Gs(e,a))return t.lanes=e.lanes,Ys(e,t,a);0!==(131072&e.flags)&&(Os=!0)}}return $s(e,t,n,r,a)}function Rs(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==o?o.baseLanes|n:n,null!==e){for(a=t.child=e.child,o=0;null!==a;)o=o|a.lanes|a.childLanes,a=a.sibling;t.childLanes=o&~r}else t.childLanes=0,t.child=null;return Ns(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ns(e,t,null!==o?o.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ha(0,null!==o?o.cachePool:null),null!==o?mo(t,o):vo(),ss(t)}else null!==o?(Ha(0,o.cachePool),mo(t,o),ls(),t.memoizedState=null):(null!==e&&Ha(0,null),vo(),ls());return Ps(e,t,a,n),t.child}function Ns(e,t,n,r){var a=Ba();return a=null===a?null:{parent:Ra._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Ha(0,null),vo(),ss(t),null!==e&&Sa(e,t,r,!0),null}function Ls(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function $s(e,t,n,r,a){return Ea(t),n=Ro(e,t,n,r,void 0,a),r=Do(),null===e||Os?(oa&&r&&ta(t),t.flags|=1,Ps(e,t,n,a),t.child):(Io(e,t,a),Ys(e,t,a))}function Ds(e,t,n,r,a,o){return Ea(t),t.updateQueue=null,n=Lo(t,r,n,a),No(e),r=Do(),null===e||Os?(oa&&r&&ta(t),t.flags|=1,Ps(e,t,n,o),t.child):(Io(e,t,o),Ys(e,t,o))}function Is(e,t,n,r,a){if(Ea(t),null===t.stateNode){var o=Lr,i=n.contextType;"object"===typeof i&&null!==i&&(o=Ca(i)),o=new n(r,o),t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,o.updater=hs,t.stateNode=o,o._reactInternals=t,(o=t.stateNode).props=r,o.state=t.memoizedState,o.refs={},no(t),i=n.contextType,o.context="object"===typeof i&&null!==i?Ca(i):Lr,o.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(fs(t,n,i,r),o.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(i=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),i!==o.state&&hs.enqueueReplaceState(o,o.state,null),co(t,r,o,a),uo(),o.state=t.memoizedState),"function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){o=t.stateNode;var s=t.memoizedProps,l=ms(n,s);o.props=l;var u=o.context,c=n.contextType;i=Lr,"object"===typeof c&&null!==c&&(i=Ca(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof o.getSnapshotBeforeUpdate,s=t.pendingProps!==s,c||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s||u!==i)&&gs(t,o,r,i),to=!1;var f=t.memoizedState;o.state=f,co(t,r,o,a),uo(),u=t.memoizedState,s||f!==u||to?("function"===typeof d&&(fs(t,n,d,r),u=t.memoizedState),(l=to||ps(t,n,l,r,f,u,i))?(c||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=i,r=l):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ro(e,t),c=ms(n,i=t.memoizedProps),o.props=c,d=t.pendingProps,f=o.context,u=n.contextType,l=Lr,"object"===typeof u&&null!==u&&(l=Ca(u)),(u="function"===typeof(s=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==l)&&gs(t,o,r,l),to=!1,f=t.memoizedState,o.state=f,co(t,r,o,a),uo();var h=t.memoizedState;i!==d||f!==h||to||null!==e&&null!==e.dependencies&&xa(e.dependencies)?("function"===typeof s&&(fs(t,n,s,r),h=t.memoizedState),(c=to||ps(t,n,c,r,f,h,l)||null!==e&&null!==e.dependencies&&xa(e.dependencies))?(u||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,h,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,h,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),o.props=r,o.state=h,o.context=l,r=c):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return o=r,Ls(e,t),r=0!==(128&t.flags),o||r?(o=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:o.render(),t.flags|=1,null!==e&&r?(t.child=ns(t,e.child,null,a),t.child=ns(t,null,n,a)):Ps(e,t,n,a),t.memoizedState=o.state,e=t.child):e=Ys(e,t,a),e}function zs(e,t,n,r){return ha(),t.flags|=256,Ps(e,t,n,r),t.child}var Ms={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Fs(e){return{baseLanes:e,cachePool:Wa()}}function Us(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=mu),e}function Bs(e,t,n){var r,a=t.pendingProps,o=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&cs.current)),r&&(o=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(oa){if(o?is(t):ls(),oa){var l,u=aa;if(l=u){e:{for(l=u,u=sa;8!==l.nodeType;){if(!u){u=null;break e}if(null===(l=vd(l.nextSibling))){u=null;break e}}u=l}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Gr?{id:Qr,overflow:Xr}:null,retryLane:536870912,hydrationErrors:null},(l=Dr(18,null,null,0)).stateNode=u,l.return=t,t.child=l,ra=t,aa=null,l=!0):l=!1}l||ua(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return md(u)?t.lanes=32:t.lanes=536870912,null;us(t)}return u=a.children,a=a.fallback,o?(ls(),u=Ws({mode:"hidden",children:u},o=t.mode),a=Ur(a,o,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(o=t.child).memoizedState=Fs(n),o.childLanes=Us(e,r,n),t.memoizedState=Ms,a):(is(t),Hs(t,u))}if(null!==(l=e.memoizedState)&&null!==(u=l.dehydrated)){if(s)256&t.flags?(is(t),t.flags&=-257,t=qs(e,t,n)):null!==t.memoizedState?(ls(),t.child=e.child,t.flags|=128,t=null):(ls(),o=a.fallback,u=t.mode,a=Ws({mode:"visible",children:a.children},u),(o=Ur(o,u,n,null)).flags|=2,a.return=t,o.return=t,a.sibling=o,t.child=a,ns(t,e.child,null,n),(a=t.child).memoizedState=Fs(n),a.childLanes=Us(e,r,n),t.memoizedState=Ms,t=o);else if(is(t),md(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(i(419))).stack="",a.digest=r,ga({value:a,source:null,stack:null}),t=qs(e,t,n)}else if(Os||Sa(e,t,n,!1),r=0!==(n&e.childLanes),Os||r){if(null!==(r=ru)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Pe(a))&(r.suspendedLanes|n))?0:a)&&a!==l.retryLane))throw l.retryLane=a,jr(e,a),Du(r,e,a),Cs;"$?"===u.data||Ku(),t=qs(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,aa=vd(u.nextSibling),ra=t,oa=!0,ia=null,sa=!1,null!==e&&(Jr[Yr++]=Qr,Jr[Yr++]=Xr,Jr[Yr++]=Gr,Qr=e.id,Xr=e.overflow,Gr=t),(t=Hs(t,a.children)).flags|=4096);return t}return o?(ls(),o=a.fallback,u=t.mode,c=(l=e.child).sibling,(a=zr(l,{mode:"hidden",children:a.children})).subtreeFlags=65011712&l.subtreeFlags,null!==c?o=zr(c,o):(o=Ur(o,u,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,null===(u=e.child.memoizedState)?u=Fs(n):(null!==(l=u.cachePool)?(c=Ra._currentValue,l=l.parent!==c?{parent:c,pool:c}:l):l=Wa(),u={baseLanes:u.baseLanes|n,cachePool:l}),o.memoizedState=u,o.childLanes=Us(e,r,n),t.memoizedState=Ms,a):(is(t),e=(n=e.child).sibling,(n=zr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hs(e,t){return(t=Ws({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Ws(e,t){return(e=Dr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function qs(e,t,n){return ns(t,e.child,null,n),(e=Hs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ka(e.return,t,n)}function Ks(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Js(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(Ps(e,t,r.children,n),0!==(2&(r=cs.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vs(e,n,t);else if(19===e.tag)Vs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(U(cs,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ds(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Ks(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ds(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Ks(t,!0,n,null,o);break;case"together":Ks(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ys(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),hu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=zr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=zr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Gs(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!xa(e))}function Qs(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Os=!0;else{if(!Gs(e,n)&&0===(128&t.flags))return Os=!1,function(e,t,n){switch(t.tag){case 3:V(t,t.stateNode.containerInfo),ba(0,Ra,e.memoizedState.cache),ha();break;case 27:case 5:J(t);break;case 4:V(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(is(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Bs(e,t,n):(is(t),null!==(e=Ys(e,t,n))?e.sibling:null);is(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Js(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),U(cs,cs.current),r)break;return null;case 22:case 23:return t.lanes=0,Rs(e,t,n);case 24:ba(0,Ra,e.memoizedState.cache)}return Ys(e,t,n)}(e,t,n);Os=0!==(131072&e.flags)}else Os=!1,oa&&0!==(1048576&t.flags)&&ea(t,Kr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===_){t.tag=11,t=Ts(null,t,r,e,n);break e}if(a===E){t.tag=14,t=As(null,t,r,e,n);break e}}throw t=R(r)||r,Error(i(306,t,""))}Ir(r)?(e=ms(r,e),t.tag=1,t=Is(null,t,r,e,n)):(t.tag=0,t=$s(null,t,r,e,n))}return t;case 0:return $s(e,t,t.type,t.pendingProps,n);case 1:return Is(e,t,r=t.type,a=ms(r,t.pendingProps),n);case 3:e:{if(V(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var o=t.memoizedState;a=o.element,ro(e,t),co(t,r,null,n);var s=t.memoizedState;if(r=s.cache,ba(0,Ra,r),r!==o.cache&&_a(t,[Ra],n,!0),uo(),r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=zs(e,t,r,n);break e}if(r!==a){ga(a=xr(Error(i(424)),t)),t=zs(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=vd(e.firstChild),ra=t,oa=!0,ia=null,sa=!0,n=rs(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===a){t=Ys(e,t,n);break e}Ps(e,t,r,n)}t=t.child}return t;case 26:return Ls(e,t),null===e?(n=Pd(t.type,null,t.pendingProps,null))?t.memoizedState=n:oa||(n=t.type,e=t.pendingProps,(r=rd(W.current).createElement(n))[Re]=t,r[Ne]=e,ed(r,n,e),qe(r),t.stateNode=r):t.memoizedState=Pd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return J(t),null===e&&oa&&(r=t.stateNode=wd(t.type,t.pendingProps,W.current),ra=t,sa=!0,a=aa,hd(t.type)?(yd=a,aa=vd(r.firstChild)):aa=a),Ps(e,t,t.pendingProps.children,n),Ls(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&oa&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Me])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(o=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(o!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((o=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var o=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===o)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,sa))?(t.stateNode=r,ra=t,aa=vd(r.firstChild),sa=!1,a=!0):a=!1),a||ua(t)),J(t),a=t.type,o=t.pendingProps,s=null!==e?e.memoizedProps:null,r=o.children,id(a,o)?r=null:null!==s&&id(a,s)&&(t.flags|=32),null!==t.memoizedState&&(a=Ro(e,t,$o,null,null,n),Jd._currentValue=a),Ls(e,t),Ps(e,t,r,n),t.child;case 6:return null===e&&oa&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(n,t.pendingProps,sa))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ua(t)),null;case 13:return Bs(e,t,n);case 4:return V(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ns(t,null,r,n):Ps(e,t,r,n),t.child;case 11:return Ts(e,t,t.type,t.pendingProps,n);case 7:return Ps(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ps(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Ps(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ea(t),r=r(a=Ca(a)),t.flags|=1,Ps(e,t,r,n),t.child;case 14:return As(e,t,t.type,t.pendingProps,n);case 15:return js(e,t,t.type,t.pendingProps,n);case 19:return Js(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Ws(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=zr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Rs(e,t,n);case 24:return Ea(t),r=Ca(Ra),null===e?(null===(a=Ba())&&(a=ru,o=Na(),a.pooledCache=o,o.refCount++,null!==o&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:r,cache:a},no(t),ba(0,Ra,a)):(0!==(e.lanes&n)&&(ro(e,t),co(t,null,null,n),uo()),a=e.memoizedState,o=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Ra,r)):(r=o.cache,ba(0,Ra,r),r!==a.cache&&_a(t,[Ra],n,!0))),Ps(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Xs(e){e.flags|=4}function Zs(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Ud(t)){if(null!==(t=as.current)&&((4194048&ou)===ou?null!==os:(62914560&ou)!==ou&&0===(536870912&ou)||t!==os))throw Xa=Ja,Va;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,vu|=t)}function tl(e,t){if(!oa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(Ra),K(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Xs(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,pa())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Xs(t),null!==n?(nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Xs(t),nl(t),Zs(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Xs(t),nl(t),t.flags&=-16777217),null;case 27:Y(t),n=W.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}e=B.current,fa(t)?ca(t):(e=wd(a,r,n),t.stateNode=e,Xs(t))}return nl(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return nl(t),null}if(e=B.current,fa(t))ca(t);else{switch(a=rd(W.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Re]=t,e[Ne]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xs(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Xs(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=W.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Re]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Gc(e.nodeValue,n)))||ua(t)}else(e=rd(e).createTextNode(r))[Re]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[Re]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),a=!1}else a=pa(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(us(t),t):(us(t),null)}if(us(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return K(),null===e&&Fc(t.stateNode.containerInfo),nl(t),null;case 10:return wa(t.type),nl(t),null;case 19:if(F(cs),null===(a=t.memoizedState))return nl(t),null;if(r=0!==(128&t.flags),null===(o=a.rendering))if(r)tl(a,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(o=ds(e))){for(t.flags|=128,tl(a,!1),e=o.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Mr(n,e),n=n.sibling;return U(cs,1&cs.current|2),t.child}e=e.sibling}null!==a.tail&&te()>_u&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ds(o))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!oa)return nl(t),null}else 2*te()-a.renderingStartTime>_u&&536870912!==n&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=a.last)?e.sibling=o:t.child=o,a.last=o)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=cs.current,U(cs,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return us(t),yo(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&F(Ua),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(Ra),nl(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function al(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(Ra),K(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(us(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return F(cs),null;case 4:return K(),null;case 10:return wa(t.type),null;case 22:case 23:return us(t),yo(),null!==e&&F(Ua),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(Ra),null;default:return null}}function ol(e,t){switch(na(t),t.tag){case 3:wa(Ra),K();break;case 26:case 27:case 5:Y(t);break;case 4:K();break;case 13:us(t);break;case 19:F(cs);break;case 10:wa(t.type);break;case 22:case 23:us(t),yo(),null!==e&&F(Ua);break;case 24:wa(Ra)}}function il(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var o=n.create,i=n.inst;r=o(),i.destroy=r}n=n.next}while(n!==a)}}catch(s){cc(t,t.return,s)}}function sl(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var o=a.next;r=o;do{if((r.tag&e)===e){var i=r.inst,s=i.destroy;if(void 0!==s){i.destroy=void 0,a=t;var l=n,u=s;try{u()}catch(c){cc(a,l,c)}}}r=r.next}while(r!==o)}}catch(c){cc(t,t.return,c)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{ho(t,n)}catch(r){cc(e,e.return,r)}}}function ul(e,t,n){n.props=ms(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){cc(e,t,r)}}function cl(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){cc(e,t,a)}}function dl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){cc(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(o){cc(e,t,o)}else n.current=null}function fl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){cc(e,e.return,a)}}function hl(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,o=null,s=null,l=null,u=null,c=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(p)||Xc(e,t,p,null,r,f)}}for(var h in r){var p=r[h];if(f=n[h],r.hasOwnProperty(h)&&(null!=p||null!=f))switch(h){case"type":o=p;break;case"name":a=p;break;case"checked":c=p;break;case"defaultChecked":d=p;break;case"value":s=p;break;case"defaultValue":l=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:p!==f&&Xc(e,t,h,p,r,f)}}return void mt(e,s,l,u,c,d,o,a);case"select":for(o in p=s=l=h=null,n)if(u=n[o],n.hasOwnProperty(o)&&null!=u)switch(o){case"value":break;case"multiple":p=u;default:r.hasOwnProperty(o)||Xc(e,t,o,null,r,u)}for(a in r)if(o=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=o||null!=u))switch(a){case"value":h=o;break;case"defaultValue":l=o;break;case"multiple":s=o;default:o!==u&&Xc(e,t,a,o,r,u)}return t=l,n=s,r=p,void(null!=h?bt(e,!!n,h,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in p=h=null,n)if(a=n[l],n.hasOwnProperty(l)&&null!=a&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Xc(e,t,l,null,r,a)}for(s in r)if(a=r[s],o=n[s],r.hasOwnProperty(s)&&(null!=a||null!=o))switch(s){case"value":h=a;break;case"defaultValue":p=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(i(91));break;default:a!==o&&Xc(e,t,s,a,r,o)}return void wt(e,h,p);case"option":for(var g in n)if(h=n[g],n.hasOwnProperty(g)&&null!=h&&!r.hasOwnProperty(g))if("selected"===g)e.selected=!1;else Xc(e,t,g,null,r,h);for(u in r)if(h=r[u],p=n[u],r.hasOwnProperty(u)&&h!==p&&(null!=h||null!=p))if("selected"===u)e.selected=h&&"function"!==typeof h&&"symbol"!==typeof h;else Xc(e,t,u,h,r,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var m in n)h=n[m],n.hasOwnProperty(m)&&null!=h&&!r.hasOwnProperty(m)&&Xc(e,t,m,null,r,h);for(c in r)if(h=r[c],p=n[c],r.hasOwnProperty(c)&&h!==p&&(null!=h||null!=p))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(i(137,t));break;default:Xc(e,t,c,h,r,p)}return;default:if(Ct(t)){for(var v in n)h=n[v],n.hasOwnProperty(v)&&void 0!==h&&!r.hasOwnProperty(v)&&Zc(e,t,v,void 0,r,h);for(d in r)h=r[d],p=n[d],!r.hasOwnProperty(d)||h===p||void 0===h&&void 0===p||Zc(e,t,d,h,r,p);return}}for(var y in n)h=n[y],n.hasOwnProperty(y)&&null!=h&&!r.hasOwnProperty(y)&&Xc(e,t,y,null,r,h);for(f in r)h=r[f],p=n[f],!r.hasOwnProperty(f)||h===p||null==h&&null==p||Xc(e,t,f,h,r,p)}(r,e.type,n,t),r[Ne]=t}catch(a){cc(e,e.return,a)}}function pl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&hd(e.type)||4===e.tag}function gl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||pl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&hd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ml(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Qc));else if(4!==r&&(27===r&&hd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(ml(e,t,n),e=e.sibling;null!==e;)ml(e,t,n),e=e.sibling}function vl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&hd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(vl(e,t,n),e=e.sibling;null!==e;)vl(e,t,n),e=e.sibling}function yl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[Re]=e,t[Ne]=n}catch(o){cc(e,e.return,o)}}var bl=!1,wl=!1,kl=!1,_l="function"===typeof WeakSet?WeakSet:Set,Sl=null;function xl(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Il(e,n),4&r&&il(5,n);break;case 1:if(Il(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){cc(n,n.return,i)}else{var a=ms(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){cc(n,n.return,s)}}64&r&&ll(n),512&r&&cl(n,n.return);break;case 3:if(Il(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{ho(e,t)}catch(i){cc(n,n.return,i)}}break;case 27:null===t&&4&r&&yl(n);case 26:case 5:Il(e,n),null===t&&4&r&&fl(n),512&r&&cl(n,n.return);break;case 12:Il(e,n);break;case 13:Il(e,n),4&r&&Al(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=pc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||wl,a=bl;var o=wl;bl=r,(wl=t)&&!o?Ml(e,n,0!==(8772&n.subtreeFlags)):Il(e,n),bl=a,wl=o}break;case 30:break;default:Il(e,n)}}function El(e){var t=e.alternate;null!==t&&(e.alternate=null,El(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Fe(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Cl=null,Ol=!1;function Pl(e,t,n){for(n=n.child;null!==n;)Tl(e,t,n),n=n.sibling}function Tl(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(o){}switch(n.tag){case 26:wl||dl(n,t),Pl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||dl(n,t);var r=Cl,a=Ol;hd(n.type)&&(Cl=n.stateNode,Ol=!1),Pl(e,t,n),kd(n.stateNode),Cl=r,Ol=a;break;case 5:wl||dl(n,t);case 6:if(r=Cl,a=Ol,Cl=null,Pl(e,t,n),Ol=a,null!==(Cl=r))if(Ol)try{(9===Cl.nodeType?Cl.body:"HTML"===Cl.nodeName?Cl.ownerDocument.body:Cl).removeChild(n.stateNode)}catch(i){cc(n,t,i)}else try{Cl.removeChild(n.stateNode)}catch(i){cc(n,t,i)}break;case 18:null!==Cl&&(Ol?(pd(9===(e=Cl).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Pf(e)):pd(Cl,n.stateNode));break;case 4:r=Cl,a=Ol,Cl=n.stateNode.containerInfo,Ol=!0,Pl(e,t,n),Cl=r,Ol=a;break;case 0:case 11:case 14:case 15:wl||sl(2,n,t),wl||sl(4,n,t),Pl(e,t,n);break;case 1:wl||(dl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&ul(n,t,r)),Pl(e,t,n);break;case 21:Pl(e,t,n);break;case 22:wl=(r=wl)||null!==n.memoizedState,Pl(e,t,n),wl=r;break;default:Pl(e,t,n)}}function Al(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Pf(e)}catch(n){cc(t,t.return,n)}}function jl(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new _l),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new _l),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var r=gc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Rl(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 27:if(hd(l.type)){Cl=l.stateNode,Ol=!1;break e}break;case 5:Cl=l.stateNode,Ol=!1;break e;case 3:case 4:Cl=l.stateNode.containerInfo,Ol=!0;break e}l=l.return}if(null===Cl)throw Error(i(160));Tl(o,s,a),Cl=null,Ol=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ll(t,e),t=t.sibling}var Nl=null;function Ll(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rl(t,e),$l(e),4&r&&(sl(3,e,e.return),il(3,e),sl(5,e,e.return));break;case 1:Rl(t,e),$l(e),512&r&&(wl||null===n||dl(n,n.return)),64&r&&bl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Nl;if(Rl(t,e),$l(e),512&r&&(wl||null===n||dl(n,n.return)),4&r){var o=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(o=a.getElementsByTagName("title")[0])||o[Me]||o[Re]||"http://www.w3.org/2000/svg"===o.namespaceURI||o.hasAttribute("itemprop"))&&(o=a.createElement(r),a.head.insertBefore(o,a.querySelector("head > title"))),ed(o,r,n),o[Re]=e,qe(o),r=o;break e;case"link":var s=Md("link","href",a).get(r+(n.href||""));if(s)for(var l=0;l<s.length;l++)if((o=s[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&o.getAttribute("rel")===(null==n.rel?null:n.rel)&&o.getAttribute("title")===(null==n.title?null:n.title)&&o.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){s.splice(l,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;case"meta":if(s=Md("meta","content",a).get(r+(n.content||"")))for(l=0;l<s.length;l++)if((o=s[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&o.getAttribute("name")===(null==n.name?null:n.name)&&o.getAttribute("property")===(null==n.property?null:n.property)&&o.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&o.getAttribute("charset")===(null==n.charSet?null:n.charSet)){s.splice(l,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;default:throw Error(i(468,r))}o[Re]=e,qe(o),r=o}e.stateNode=r}else Fd(a,e.type,e.stateNode);else e.stateNode=Ld(a,r,e.memoizedProps);else o!==r?(null===o?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):o.count--,null===r?Fd(a,e.type,e.stateNode):Ld(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&hl(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rl(t,e),$l(e),512&r&&(wl||null===n||dl(n,n.return)),null!==n&&4&r&&hl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rl(t,e),$l(e),512&r&&(wl||null===n||dl(n,n.return)),32&e.flags){a=e.stateNode;try{_t(a,"")}catch(p){cc(e,e.return,p)}}4&r&&null!=e.stateNode&&hl(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(kl=!0);break;case 6:if(Rl(t,e),$l(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(p){cc(e,e.return,p)}}break;case 3:if(zd=null,a=Nl,Nl=xd(t.containerInfo),Rl(t,e),Nl=a,$l(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Pf(t.containerInfo)}catch(p){cc(e,e.return,p)}kl&&(kl=!1,Dl(e));break;case 4:r=Nl,Nl=xd(e.stateNode.containerInfo),Rl(t,e),$l(e),Nl=r;break;case 12:default:Rl(t,e),$l(e);break;case 13:Rl(t,e),$l(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(ku=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,jl(e,r)));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=bl,d=wl;if(bl=c||a,wl=d||u,Rl(t,e),wl=d,bl=c,$l(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||bl||wl||zl(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,a)"function"===typeof(s=o.style).setProperty?s.setProperty("display","none","important"):s.display="none";else{l=u.stateNode;var f=u.memoizedProps.style,h=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;l.style.display=null==h||"boolean"===typeof h?"":(""+h).trim()}}catch(p){cc(u,u.return,p)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(p){cc(u,u.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,jl(e,n))));break;case 19:Rl(t,e),$l(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,jl(e,r)));case 30:case 21:}}function $l(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(pl(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode;vl(e,gl(e),a);break;case 5:var o=n.stateNode;32&n.flags&&(_t(o,""),n.flags&=-33),vl(e,gl(e),o);break;case 3:case 4:var s=n.stateNode.containerInfo;ml(e,gl(e),s);break;default:throw Error(i(161))}}catch(l){cc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Dl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Dl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Il(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)xl(e,t.alternate,t),t=t.sibling}function zl(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:sl(4,t,t.return),zl(t);break;case 1:dl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&ul(t,t.return,n),zl(t);break;case 27:kd(t.stateNode);case 26:case 5:dl(t,t.return),zl(t);break;case 22:null===t.memoizedState&&zl(t);break;default:zl(t)}e=e.sibling}}function Ml(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,o=t,i=o.flags;switch(o.tag){case 0:case 11:case 15:Ml(a,o,n),il(4,o);break;case 1:if(Ml(a,o,n),"function"===typeof(a=(r=o).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(a=(r=o).updateQueue)){var s=r.stateNode;try{var l=a.shared.hiddenCallbacks;if(null!==l)for(a.shared.hiddenCallbacks=null,a=0;a<l.length;a++)fo(l[a],s)}catch(u){cc(r,r.return,u)}}n&&64&i&&ll(o),cl(o,o.return);break;case 27:yl(o);case 26:case 5:Ml(a,o,n),n&&null===r&&4&i&&fl(o),cl(o,o.return);break;case 12:Ml(a,o,n);break;case 13:Ml(a,o,n),n&&4&i&&Al(a,o);break;case 22:null===o.memoizedState&&Ml(a,o,n),cl(o,o.return);break;case 30:break;default:Ml(a,o,n)}t=t.sibling}}function Fl(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&La(n))}function Ul(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&La(e))}function Bl(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hl(e,t,n,r),t=t.sibling}function Hl(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Bl(e,t,n,r),2048&a&&il(9,t);break;case 1:case 13:default:Bl(e,t,n,r);break;case 3:Bl(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&La(e)));break;case 12:if(2048&a){Bl(e,t,n,r),e=t.stateNode;try{var o=t.memoizedProps,i=o.id,s=o.onPostCommit;"function"===typeof s&&s(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){cc(t,t.return,l)}}else Bl(e,t,n,r);break;case 23:break;case 22:o=t.stateNode,i=t.alternate,null!==t.memoizedState?2&o._visibility?Bl(e,t,n,r):ql(e,t):2&o._visibility?Bl(e,t,n,r):(o._visibility|=2,Wl(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Fl(i,t);break;case 24:Bl(e,t,n,r),2048&a&&Ul(t.alternate,t)}}function Wl(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var o=e,i=t,s=n,l=r,u=i.flags;switch(i.tag){case 0:case 11:case 15:Wl(o,i,s,l,a),il(8,i);break;case 23:break;case 22:var c=i.stateNode;null!==i.memoizedState?2&c._visibility?Wl(o,i,s,l,a):ql(o,i):(c._visibility|=2,Wl(o,i,s,l,a)),a&&2048&u&&Fl(i.alternate,i);break;case 24:Wl(o,i,s,l,a),a&&2048&u&&Ul(i.alternate,i);break;default:Wl(o,i,s,l,a)}t=t.sibling}}function ql(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:ql(n,r),2048&a&&Fl(r.alternate,r);break;case 24:ql(n,r),2048&a&&Ul(r.alternate,r);break;default:ql(n,r)}t=t.sibling}}var Vl=8192;function Kl(e){if(e.subtreeFlags&Vl)for(e=e.child;null!==e;)Jl(e),e=e.sibling}function Jl(e){switch(e.tag){case 26:Kl(e),e.flags&Vl&&null!==e.memoizedState&&function(e,t,n){if(null===Bd)throw Error(i(475));var r=Bd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Td(n.href),o=e.querySelector(Ad(a));if(o)return null!==(e=o._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Wd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=o,void qe(o);o=e.ownerDocument||e,n=jd(n),(a=_d.get(a))&&Dd(n,a),qe(o=o.createElement("link"));var s=o;s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(o,"link",n),t.instance=o}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Wd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Nl,e.memoizedState,e.memoizedProps);break;case 5:default:Kl(e);break;case 3:case 4:var t=Nl;Nl=xd(e.stateNode.containerInfo),Kl(e),Nl=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Vl,Vl=16777216,Kl(e),Vl=t):Kl(e))}}function Yl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Gl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Sl=r,Zl(r,e)}Yl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Ql(e),e=e.sibling}function Ql(e){switch(e.tag){case 0:case 11:case 15:Gl(e),2048&e.flags&&sl(9,e,e.return);break;case 3:case 12:default:Gl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Xl(e)):Gl(e)}}function Xl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Sl=r,Zl(r,e)}Yl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:sl(8,t,t.return),Xl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Xl(t));break;default:Xl(t)}e=e.sibling}}function Zl(e,t){for(;null!==Sl;){var n=Sl;switch(n.tag){case 0:case 11:case 15:sl(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:La(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Sl=r;else e:for(n=e;null!==Sl;){var a=(r=Sl).sibling,o=r.return;if(El(r),r===n){Sl=null;break e}if(null!==a){a.return=o,Sl=a;break e}Sl=o}}}var eu={getCacheForType:function(e){var t=Ca(Ra),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"===typeof WeakMap?WeakMap:Map,nu=0,ru=null,au=null,ou=0,iu=0,su=null,lu=!1,uu=!1,cu=!1,du=0,fu=0,hu=0,pu=0,gu=0,mu=0,vu=0,yu=null,bu=null,wu=!1,ku=0,_u=1/0,Su=null,xu=null,Eu=0,Cu=null,Ou=null,Pu=0,Tu=0,Au=null,ju=null,Ru=0,Nu=null;function Lu(){if(0!==(2&nu)&&0!==ou)return ou&-ou;if(null!==L.T){return 0!==Ia?Ia:Tc()}return Ae()}function $u(){0===mu&&(mu=0===(536870912&ou)||oa?_e():536870912);var e=as.current;return null!==e&&(e.flags|=32),mu}function Du(e,t,n){(e!==ru||2!==iu&&9!==iu)&&null===e.cancelPendingCommit||(Hu(e,0),Fu(e,ou,mu,!1)),Ee(e,n),0!==(2&nu)&&e===ru||(e===ru&&(0===(2&nu)&&(pu|=n),4===fu&&Fu(e,ou,mu,!1)),_c(e))}function Iu(e,t,n){if(0!==(6&nu))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=nu;nu|=2;var r=qu(),a=Vu();ru!==e||ou!==t?(Su=null,_u=te()+500,Hu(e,t)):uu=we(e,t);e:for(;;)try{if(0!==iu&&null!==au){t=au;var o=su;t:switch(iu){case 1:iu=0,su=null,Zu(e,t,o,1);break;case 2:case 9:if(Ya(o)){iu=0,su=null,Xu(t);break}t=function(){2!==iu&&9!==iu||ru!==e||(iu=7),_c(e)},o.then(t,t);break e;case 3:iu=7;break e;case 4:iu=5;break e;case 7:Ya(o)?(iu=0,su=null,Xu(t)):(iu=0,su=null,Zu(e,t,o,7));break;case 5:var s=null;switch(au.tag){case 26:s=au.memoizedState;case 5:case 27:var l=au;if(!s||Ud(s)){iu=0,su=null;var u=l.sibling;if(null!==u)au=u;else{var c=l.return;null!==c?(au=c,ec(c)):au=null}break t}}iu=0,su=null,Zu(e,t,o,5);break;case 6:iu=0,su=null,Zu(e,t,o,6);break;case 8:Bu(),fu=6;break e;default:throw Error(i(462))}}Gu();break}catch(d){Wu(e,d)}return ya=va=null,L.H=r,L.A=a,nu=n,null!==au?0:(ru=null,ou=0,Pr(),fu)}(e,t):Ju(e,t,!0),o=r;;){if(0===a){uu&&!r&&Fu(e,t,0,!1);break}if(n=e.current.alternate,!o||Mu(n)){if(2===a){if(o=t,e.errorRecoveryDisabledLanes&o)var s=0;else s=0!==(s=-536870913&e.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){t=s;e:{var l=e;a=yu;var u=l.current.memoizedState.isDehydrated;if(u&&(Hu(l,s).flags|=256),2!==(s=Ju(l,s,!1))){if(cu&&!u){l.errorRecoveryDisabledLanes|=o,pu|=o,a=4;break e}o=bu,bu=a,null!==o&&(null===bu?bu=o:bu.push.apply(bu,o))}a=s}if(o=!1,2!==a)continue}}if(1===a){Hu(e,0),Fu(e,t,0,!0);break}e:{switch(r=e,o=a){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Fu(r,t,mu,!lu);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(a=ku+300-te())){if(Fu(r,t,mu,!lu),0!==be(r,0,!0))break e;r.timeoutHandle=ld(zu.bind(null,r,n,bu,Su,wu,t,mu,pu,vu,lu,o,2,-0,0),a)}else zu(r,n,bu,Su,wu,t,mu,pu,vu,lu,o,0,-0,0)}break}a=Ju(e,t,!1),o=!1}_c(e)}function zu(e,t,n,r,a,o,s,l,u,c,d,f,h,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Bd={stylesheets:null,count:0,unsuspend:Hd},Jl(t),null!==(f=function(){if(null===Bd)throw Error(i(475));var e=Bd;return e.stylesheets&&0===e.count&&Vd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Vd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,o,n,r,a,s,l,u,d,1,h,p)),void Fu(e,o,s,!c);nc(e,t,o,n,r,a,s,l,u)}function Mu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!Yn(o(),a))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Fu(e,t,n,r){t&=~gu,t&=~pu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var o=31-he(a),i=1<<o;r[o]=-1,a&=~i}0!==n&&Ce(e,n,t)}function Uu(){return 0!==(6&nu)||(Sc(0,!1),!1)}function Bu(){if(null!==au){if(0===iu)var e=au.return;else ya=va=null,zo(e=au),Yi=null,Gi=0,e=au;for(;null!==e;)ol(e.alternate,e),e=e.return;au=null}}function Hu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Bu(),ru=e,au=n=zr(e.current,null),ou=t,iu=0,su=null,lu=!1,uu=we(e,t),cu=!1,vu=mu=gu=pu=hu=fu=0,bu=yu=null,wu=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-he(r),o=1<<a;t|=e[a],r&=~o}return du=t,Pr(),n}function Wu(e,t){wo=null,L.H=qi,t===qa||t===Ka?(t=Za(),iu=3):t===Va?(t=Za(),iu=4):iu=t===Cs?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,su=t,null===au&&(fu=1,ks(e,xr(t,e.current)))}function qu(){var e=L.H;return L.H=qi,null===e?qi:e}function Vu(){var e=L.A;return L.A=eu,e}function Ku(){fu=4,lu||(4194048&ou)!==ou&&null!==as.current||(uu=!0),0===(134217727&hu)&&0===(134217727&pu)||null===ru||Fu(ru,ou,mu,!1)}function Ju(e,t,n){var r=nu;nu|=2;var a=qu(),o=Vu();ru===e&&ou===t||(Su=null,Hu(e,t)),t=!1;var i=fu;e:for(;;)try{if(0!==iu&&null!==au){var s=au,l=su;switch(iu){case 8:Bu(),i=6;break e;case 3:case 2:case 9:case 6:null===as.current&&(t=!0);var u=iu;if(iu=0,su=null,Zu(e,s,l,u),n&&uu){i=0;break e}break;default:u=iu,iu=0,su=null,Zu(e,s,l,u)}}Yu(),i=fu;break}catch(c){Wu(e,c)}return t&&e.shellSuspendCounter++,ya=va=null,nu=r,L.H=a,L.A=o,null===au&&(ru=null,ou=0,Pr()),i}function Yu(){for(;null!==au;)Qu(au)}function Gu(){for(;null!==au&&!Z();)Qu(au)}function Qu(e){var t=Qs(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Xu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ds(n,t,t.pendingProps,t.type,void 0,ou);break;case 11:t=Ds(n,t,t.pendingProps,t.type.render,t.ref,ou);break;case 5:zo(t);default:ol(n,t),t=Qs(n,t=au=Mr(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Zu(e,t,n,r){ya=va=null,zo(t),Yi=null,Gi=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=as.current)){switch(n.tag){case 13:return null===os?Ku():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ja?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,a)),!1;case 22:return n.flags|=65536,r===Ja?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,a)),!1}throw Error(i(435,n.tag))}return dc(e,r,a),Ku(),!1}if(oa)return null!==(t=as.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==la&&ga(xr(e=Error(i(422),{cause:r}),n))):(r!==la&&ga(xr(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=xr(r,n),so(e,a=Ss(e.stateNode,r,a)),4!==fu&&(fu=2)),!1;var o=Error(i(520),{cause:r});if(o=xr(o,n),null===yu?yu=[o]:yu.push(o),4!==fu&&(fu=2),null===t)return!0;r=xr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,so(n,e=Ss(n.stateNode,r,e)),!1;case 1:if(t=n.type,o=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==o&&"function"===typeof o.componentDidCatch&&(null===xu||!xu.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,Es(a=xs(a),e,n,r),so(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,ou))return fu=1,ks(e,xr(n,e.current)),void(au=null)}catch(o){if(null!==a)throw au=a,o;return fu=1,ks(e,xr(n,e.current)),void(au=null)}32768&t.flags?(oa||1===r?e=!0:uu||0!==(536870912&ou)?e=!1:(lu=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=as.current)&&13===r.tag&&(r.flags|=16384))),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(0!==(32768&t.flags))return void tc(t,lu);e=t.return;var n=rl(t.alternate,t,du);if(null!==n)return void(au=n);if(null!==(t=t.sibling))return void(au=t);au=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=al(e.alternate,e);if(null!==n)return n.flags&=32767,void(au=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(au=e);au=e=n}while(null!==e);fu=6,au=null}function nc(e,t,n,r,a,o,s,l,u){e.cancelPendingCommit=null;do{sc()}while(0!==Eu);if(0!==(6&nu))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(o=t.lanes|t.childLanes,function(e,t,n,r,a,o){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var s=e.entanglements,l=e.expirationTimes,u=e.hiddenUpdates;for(n=i&~n;0<n;){var c=31-he(n),d=1<<c;s[c]=0,l[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var h=f[c];null!==h&&(h.lane&=-536870913)}n&=~d}0!==r&&Ce(e,r,0),0!==o&&0===a&&0!==e.tag&&(e.suspendedLanes|=o&~(i&~t))}(e,n,o|=Or,s,l,u),e===ru&&(au=ru=null,ou=0),Ou=t,Cu=e,Pu=n,Tu=o,Au=a,ju=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,Q(oe,function(){return lc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=L.T,L.T=null,a=$.p,$.p=2,s=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(m){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==o||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===a&&(l=s),h===o&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Sl=t;null!==Sl;)if(e=(t=Sl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Sl=e;else for(;null!==Sl;){switch(o=(t=Sl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==o){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,r=n.stateNode;try{var g=ms(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(g,o),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){cc(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))gd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":gd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,Sl=e;break}Sl=t.return}}(e,t)}finally{nu=s,$.p=a,L.T=r}}Eu=1,rc(),ac(),oc()}}function rc(){if(1===Eu){Eu=0;var e=Cu,t=Ou,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=L.T,L.T=null;var r=$.p;$.p=2;var a=nu;nu|=4;try{Ll(t,e);var o=nd,i=er(e.containerInfo),s=o.focusedElem,l=o.selectionRange;if(i!==s&&s&&s.ownerDocument&&Zn(s.ownerDocument.documentElement,s)){if(null!==l&&tr(s)){var u=l.start,c=l.end;if(void 0===c&&(c=u),"selectionStart"in s)s.selectionStart=u,s.selectionEnd=Math.min(c,s.value.length);else{var d=s.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var h=f.getSelection(),p=s.textContent.length,g=Math.min(l.start,p),m=void 0===l.end?g:Math.min(l.end,p);!h.extend&&g>m&&(i=m,m=g,g=i);var v=Xn(s,g),y=Xn(s,m);if(v&&y&&(1!==h.rangeCount||h.anchorNode!==v.node||h.anchorOffset!==v.offset||h.focusNode!==y.node||h.focusOffset!==y.offset)){var b=d.createRange();b.setStart(v.node,v.offset),h.removeAllRanges(),g>m?(h.addRange(b),h.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),h.addRange(b))}}}}for(d=[],h=s;h=h.parentNode;)1===h.nodeType&&d.push({element:h,left:h.scrollLeft,top:h.scrollTop});for("function"===typeof s.focus&&s.focus(),s=0;s<d.length;s++){var w=d[s];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nf=!!td,nd=td=null}finally{nu=a,$.p=r,L.T=n}}e.current=t,Eu=2}}function ac(){if(2===Eu){Eu=0;var e=Cu,t=Ou,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=L.T,L.T=null;var r=$.p;$.p=2;var a=nu;nu|=4;try{xl(e,t.alternate,t)}finally{nu=a,$.p=r,L.T=n}}Eu=3}}function oc(){if(4===Eu||3===Eu){Eu=0,ee();var e=Cu,t=Ou,n=Pu,r=ju;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Eu=5:(Eu=0,Ou=Cu=null,ic(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(xu=null),Te(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==r){t=L.T,a=$.p,$.p=2,L.T=null;try{for(var o=e.onRecoverableError,i=0;i<r.length;i++){var s=r[i];o(s.value,{componentStack:s.stack})}}finally{L.T=t,$.p=a}}0!==(3&Pu)&&sc(),_c(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Nu?Ru++:(Ru=0,Nu=e):Ru=0,Sc(0,!1)}}function ic(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,La(t)))}function sc(e){return rc(),ac(),oc(),lc()}function lc(){if(5!==Eu)return!1;var e=Cu,t=Tu;Tu=0;var n=Te(Pu),r=L.T,a=$.p;try{$.p=32>n?32:n,L.T=null,n=Au,Au=null;var o=Cu,s=Pu;if(Eu=0,Ou=Cu=null,Pu=0,0!==(6&nu))throw Error(i(331));var l=nu;if(nu|=4,Ql(o.current),Hl(o,o.current,s,n),nu=l,Sc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,o)}catch(u){}return!0}finally{$.p=a,L.T=r,ic(e,t)}}function uc(e,t,n){t=xr(n,t),null!==(e=oo(e,t=Ss(e.stateNode,t,2),2))&&(Ee(e,2),_c(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===xu||!xu.has(r))){e=xr(n,e),null!==(r=oo(t,n=xs(2),2))&&(Es(n,r,t,e),Ee(r,2),_c(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(cu=!0,a.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(ou&n)===n&&(4===fu||3===fu&&(62914560&ou)===ou&&300>te()-ku?0===(2&nu)&&Hu(e,0):gu|=n,vu===ou&&(vu=0)),_c(e)}function hc(e,t){0===t&&(t=Se()),null!==(e=jr(e,t))&&(Ee(e,t),_c(e))}function pc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),hc(e,n)}function gc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),hc(e,n)}var mc=null,vc=null,yc=!1,bc=!1,wc=!1,kc=0;function _c(e){e!==vc&&null===e.next&&(null===vc?mc=vc=e:vc=vc.next=e),bc=!0,yc||(yc=!0,dd(function(){0!==(6&nu)?Q(re,xc):Ec()}))}function Sc(e,t){if(!wc&&bc){wc=!0;do{for(var n=!1,r=mc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var o=0;else{var i=r.suspendedLanes,s=r.pingedLanes;o=(1<<31-he(42|e)+1)-1,o=201326741&(o&=a&~(i&~s))?201326741&o|1:o?2|o:0}0!==o&&(n=!0,Pc(r,o))}else o=ou,0===(3&(o=be(r,r===ru?o:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,o)||(n=!0,Pc(r,o));r=r.next}}while(n);wc=!1}}function xc(){Ec()}function Ec(){bc=yc=!1;var e=0;0!==kc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==sd&&(sd=e,!0);return sd=null,!1}()&&(e=kc),kc=0);for(var t=te(),n=null,r=mc;null!==r;){var a=r.next,o=Cc(r,t);0===o?(r.next=null,null===n?mc=a:n.next=a,null===a&&(vc=n)):(n=r,(0!==e||0!==(3&o))&&(bc=!0)),r=a}Sc(e,!1)}function Cc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=-62914561&e.pendingLanes;0<o;){var i=31-he(o),s=1<<i,l=a[i];-1===l?0!==(s&n)&&0===(s&r)||(a[i]=ke(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}if(n=ou,n=be(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===iu||9===iu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&X(r),Te(n)){case 2:case 8:n=ae;break;case 32:default:n=oe;break;case 268435456:n=se}return r=Oc.bind(null,e),n=Q(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function Oc(e,t){if(0!==Eu&&5!==Eu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(sc()&&e.callbackNode!==n)return null;var r=ou;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Iu(e,r,t),Cc(e,te()),null!=e.callbackNode&&e.callbackNode===n?Oc.bind(null,e):null)}function Pc(e,t){if(sc())return null;Iu(e,t,!0)}function Tc(){return 0===kc&&(kc=_e()),kc}function Ac(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Tt(""+e)}function jc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Rc=0;Rc<kr.length;Rc++){var Nc=kr[Rc];_r(Nc.toLowerCase(),"on"+(Nc[0].toUpperCase()+Nc.slice(1)))}_r(hr,"onAnimationEnd"),_r(pr,"onAnimationIteration"),_r(gr,"onAnimationStart"),_r("dblclick","onDoubleClick"),_r("focusin","onFocus"),_r("focusout","onBlur"),_r(mr,"onTransitionRun"),_r(vr,"onTransitionStart"),_r(yr,"onTransitionCancel"),_r(br,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Je("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Je("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Je("onBeforeInput",["compositionend","keypress","textInput","paste"]),Je("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Je("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Je("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),$c=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Lc));function Dc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&a.isPropagationStopped())break e;o=s,a.currentTarget=u;try{o(a)}catch(c){vs(c)}a.currentTarget=null,o=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,u=s.currentTarget,s=s.listener,l!==o&&a.isPropagationStopped())break e;o=s,a.currentTarget=u;try{o(a)}catch(c){vs(c)}a.currentTarget=null,o=l}}}}function Ic(e,t){var n=t[$e];void 0===n&&(n=t[$e]=new Set);var r=e+"__bubble";n.has(r)||(Uc(t,e,2,!1),n.add(r))}function zc(e,t,n){var r=0;t&&(r|=4),Uc(n,e,r,t)}var Mc="_reactListening"+Math.random().toString(36).slice(2);function Fc(e){if(!e[Mc]){e[Mc]=!0,Ve.forEach(function(t){"selectionchange"!==t&&($c.has(t)||zc(t,!1,e),zc(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Mc]||(t[Mc]=!0,zc("selectionchange",!1,t))}}function Uc(e,t,n,r){switch(cf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=of}n=a.bind(null,t,n,e),a=void 0,!Mt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Bc(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===a)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==s;){if(null===(i=Ue(s)))return;if(5===(u=i.tag)||6===u||26===u||27===u){r=o=i;continue e}s=s.parentNode}}r=r.return}Dt(function(){var r=o,a=jt(n),i=[];e:{var s=wr.get(e);if(void 0!==s){var u=Zt,c=e;switch(e){case"keypress":if(0===qt(n))break e;case"keydown":case"keyup":u=gn;break;case"focusin":c="focus",u=on;break;case"focusout":c="blur",u=on;break;case"beforeblur":case"afterblur":u=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=vn;break;case hr:case pr:case gr:u=sn;break;case br:u=yn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=bn;break;case"copy":case"cut":case"paste":u=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=mn;break;case"toggle":case"beforetoggle":u=wn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),h=d?null!==s?s+"Capture":null:s;d=[];for(var p,g=r;null!==g;){var m=g;if(p=m.stateNode,5!==(m=m.tag)&&26!==m&&27!==m||null===p||null===h||null!=(m=It(g,h))&&d.push(Hc(g,m,p)),f)break;g=g.return}0<d.length&&(s=new u(s,c,null,n,a),i.push({event:s,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===At||!(c=n.relatedTarget||n.fromElement)||!Ue(c)&&!c[Le])&&(u||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Ue(c):null)&&(f=l(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,m="onMouseLeave",h="onMouseEnter",g="mouse","pointerout"!==e&&"pointerover"!==e||(d=mn,m="onPointerLeave",h="onPointerEnter",g="pointer"),f=null==u?s:He(u),p=null==c?s:He(c),(s=new d(m,g+"leave",u,n,a)).target=f,s.relatedTarget=p,m=null,Ue(a)===r&&((d=new d(h,g+"enter",c,n,a)).target=p,d.relatedTarget=f,m=d),f=m,u&&c)e:{for(h=c,g=0,p=d=u;p;p=qc(p))g++;for(p=0,m=h;m;m=qc(m))p++;for(;0<g-p;)d=qc(d),g--;for(;0<p-g;)h=qc(h),p--;for(;g--;){if(d===h||null!==h&&d===h.alternate)break e;d=qc(d),h=qc(h)}d=null}else d=null;null!==u&&Vc(i,s,u,d,!1),null!==c&&null!==f&&Vc(i,f,c,d,!0)}if("select"===(u=(s=r?He(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===u&&"file"===s.type)var v=zn;else if(Rn(s))if(Mn)v=Jn;else{v=Vn;var y=qn}else!(u=s.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Ct(r.elementType)&&(v=zn):v=Kn;switch(v&&(v=v(e,r))?Nn(i,v,n,a):(y&&y(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&yt(s,"number",s.value)),y=r?He(r):window,e){case"focusin":(Rn(y)||"true"===y.contentEditable)&&(rr=y,ar=r,or=null);break;case"focusout":or=ar=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,sr(i,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":sr(i,n,a)}var b;if(_n)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else An?Pn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(En&&"ko"!==n.locale&&(An||"onCompositionStart"!==w?"onCompositionEnd"===w&&An&&(b=Wt()):(Bt="value"in(Ut=a)?Ut.value:Ut.textContent,An=!0)),0<(y=Wc(r,w)).length&&(w=new un(w,e,null,n,a),i.push({event:w,listeners:y}),b?w.data=b:null!==(b=Tn(n))&&(w.data=b))),(b=xn?function(e,t){switch(e){case"compositionend":return Tn(t);case"keypress":return 32!==t.which?null:(On=!0,Cn);case"textInput":return(e=t.data)===Cn&&On?null:e;default:return null}}(e,n):function(e,t){if(An)return"compositionend"===e||!_n&&Pn(e,t)?(e=Wt(),Ht=Bt=Ut=null,An=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return En&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Wc(r,"onBeforeInput")).length&&(y=new un("onBeforeInput","beforeinput",null,n,a),i.push({event:y,listeners:w}),y.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var o=Ac((a[Ne]||null).action),i=r.submitter;i&&null!==(t=(t=i[Ne]||null)?Ac(t.formAction):i.getAttribute("formAction"))&&(o=t,i=null);var s=new Zt("action","action",null,r,a);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==kc){var e=i?jc(a,i):new FormData(a);ji(n,{pending:!0,data:e,method:a.method,action:o},null,e)}}else"function"===typeof o&&(s.preventDefault(),e=i?jc(a,i):new FormData(a),ji(n,{pending:!0,data:e,method:a.method,action:o},o,e))},currentTarget:a}]})}}(i,e,r,n,a)}Dc(i,t)})}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===o||(null!=(a=It(e,n))&&r.unshift(Hc(e,a,o)),null!=(a=It(e,t))&&r.push(Hc(e,a,o))),3===e.tag)return r;e=e.return}return[]}function qc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Vc(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(s=s.tag,null!==l&&l===r)break;5!==s&&26!==s&&27!==s||null===u||(l=u,a?null!=(u=It(n,o))&&i.unshift(Hc(n,u,l)):a||null!=(u=It(n,o))&&i.push(Hc(n,u,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Kc=/\r\n?/g,Jc=/\u0000|\uFFFD/g;function Yc(e){return("string"===typeof e?e:""+e).replace(Kc,"\n").replace(Jc,"")}function Gc(e,t){return t=Yc(t),Yc(e)===t}function Qc(){}function Xc(e,t,n,r,a,o){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||_t(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&_t(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Et(e,r,o);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof o&&("formAction"===n?("input"!==t&&Xc(e,t,"name",a.name,a,null),Xc(e,t,"formEncType",a.formEncType,a,null),Xc(e,t,"formMethod",a.formMethod,a,null),Xc(e,t,"formTarget",a.formTarget,a,null)):(Xc(e,t,"encType",a.encType,a,null),Xc(e,t,"method",a.method,a,null),Xc(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Qc);break;case"onScroll":null!=r&&Ic("scroll",e);break;case"onScrollEnd":null!=r&&Ic("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Tt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Ic("beforetoggle",e),Ic("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Ot.get(n)||n,r)}}function Zc(e,t,n,r,a,o){switch(n){case"style":Et(e,r,o);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?_t(e,r):("number"===typeof r||"bigint"===typeof r)&&_t(e,""+r);break;case"onScroll":null!=r&&Ic("scroll",e);break;case"onScrollEnd":null!=r&&Ic("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Qc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(o=null!=(o=e[Ne]||null)?o[n]:null)&&e.removeEventListener(t,o,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof o&&null!==o&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ic("error",e),Ic("load",e);var r,a=!1,o=!1;for(r in n)if(n.hasOwnProperty(r)){var s=n[r];if(null!=s)switch(r){case"src":a=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Xc(e,t,r,s,n,null)}}return o&&Xc(e,t,"srcSet",n.srcSet,n,null),void(a&&Xc(e,t,"src",n.src,n,null));case"input":Ic("invalid",e);var l=r=s=o=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":o=d;break;case"type":s=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Xc(e,t,a,d,n,null)}}return vt(e,r,l,u,c,s,o,!1),void dt(e);case"select":for(o in Ic("invalid",e),a=s=r=null,n)if(n.hasOwnProperty(o)&&null!=(l=n[o]))switch(o){case"value":r=l;break;case"defaultValue":s=l;break;case"multiple":a=l;default:Xc(e,t,o,l,n,null)}return t=r,n=s,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(s in Ic("invalid",e),r=o=a=null,n)if(n.hasOwnProperty(s)&&null!=(l=n[s]))switch(s){case"value":a=l;break;case"defaultValue":o=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(i(91));break;default:Xc(e,t,s,l,n,null)}return kt(e,a,o,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))if("selected"===u)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Xc(e,t,u,a,n,null);return;case"dialog":Ic("beforetoggle",e),Ic("toggle",e),Ic("cancel",e),Ic("close",e);break;case"iframe":case"object":Ic("load",e);break;case"video":case"audio":for(a=0;a<Lc.length;a++)Ic(Lc[a],e);break;case"image":Ic("error",e),Ic("load",e);break;case"details":Ic("toggle",e);break;case"embed":case"source":case"link":Ic("error",e),Ic("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Xc(e,t,c,a,n,null)}return;default:if(Ct(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zc(e,t,d,a,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(a=n[l])&&Xc(e,t,l,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var sd=null;var ld="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:ld;function fd(e){setTimeout(function(){throw e})}function hd(e){return"head"===e}function pd(e,t){var n=t,r=0,a=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&kd(i.documentElement),2&n&&kd(i.body),4&n)for(kd(n=i.head),i=n.firstChild;i;){var s=i.nextSibling,l=i.nodeName;i[Me]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=s}}if(0===a)return e.removeChild(o),void Pf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=o}while(n);Pf(t)}function gd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":gd(n),Fe(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function md(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var yd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Fe(e)}var _d=new Map,Sd=new Set;function xd(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Ed=$.d;$.d={f:function(){var e=Ed.f(),t=Uu();return e||t},r:function(e){var t=Be(e);null!==t&&5===t.tag&&"form"===t.type?Ni(t):Ed.r(e)},D:function(e){Ed.D(e),Od("dns-prefetch",e,null)},C:function(e,t){Ed.C(e,t),Od("preconnect",e,t)},L:function(e,t,n){Ed.L(e,t,n);var r=Cd;if(r&&e&&t){var a='link[rel="preload"][as="'+gt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+gt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+gt(n.imageSizes)+'"]')):a+='[href="'+gt(e)+'"]';var o=a;switch(t){case"style":o=Td(e);break;case"script":o=Rd(e)}_d.has(o)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),_d.set(o,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Ad(o))||"script"===t&&r.querySelector(Nd(o))||(ed(t=r.createElement("link"),"link",e),qe(t),r.head.appendChild(t)))}},m:function(e,t){Ed.m(e,t);var n=Cd;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+gt(r)+'"][href="'+gt(e)+'"]',o=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Rd(e)}if(!_d.has(o)&&(e=f({rel:"modulepreload",href:e},t),_d.set(o,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Nd(o)))return}ed(r=n.createElement("link"),"link",e),qe(r),n.head.appendChild(r)}}},X:function(e,t){Ed.X(e,t);var n=Cd;if(n&&e){var r=We(n).hoistableScripts,a=Rd(e),o=r.get(a);o||((o=n.querySelector(Nd(a)))||(e=f({src:e,async:!0},t),(t=_d.get(a))&&Id(e,t),qe(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}},S:function(e,t,n){Ed.S(e,t,n);var r=Cd;if(r&&e){var a=We(r).hoistableStyles,o=Td(e);t=t||"default";var i=a.get(o);if(!i){var s={loading:0,preload:null};if(i=r.querySelector(Ad(o)))s.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=_d.get(o))&&Dd(e,n);var l=i=r.createElement("link");qe(l),ed(l,"link",e),l._p=new Promise(function(e,t){l.onload=e,l.onerror=t}),l.addEventListener("load",function(){s.loading|=1}),l.addEventListener("error",function(){s.loading|=2}),s.loading|=4,$d(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:s},a.set(o,i)}}},M:function(e,t){Ed.M(e,t);var n=Cd;if(n&&e){var r=We(n).hoistableScripts,a=Rd(e),o=r.get(a);o||((o=n.querySelector(Nd(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=_d.get(a))&&Id(e,t),qe(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}}};var Cd="undefined"===typeof document?null:document;function Od(e,t,n){var r=Cd;if(r&&"string"===typeof t&&t){var a=gt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),qe(t),r.head.appendChild(t)))}}function Pd(e,t,n,r){var a,o,s,l,u=(u=W.current)?xd(u):null;if(!u)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Td(n.href),(r=(n=We(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Td(n.href);var c=We(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Ad(e)))&&!c._p&&(d.instance=c,d.state.loading=5),_d.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},_d.set(e,n),c||(a=u,o=e,s=n,l=d.state,a.querySelector('link[rel="preload"][as="style"]['+o+"]")?l.loading=1:(o=a.createElement("link"),l.preload=o,o.addEventListener("load",function(){return l.loading|=1}),o.addEventListener("error",function(){return l.loading|=2}),ed(o,"link",s),qe(o),a.head.appendChild(o))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Rd(n),(r=(n=We(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Td(e){return'href="'+gt(e)+'"'}function Ad(e){return'link[rel="stylesheet"]['+e+"]"}function jd(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Rd(e){return'[src="'+gt(e)+'"]'}function Nd(e){return"script[async]"+e}function Ld(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+gt(n.href)+'"]');if(r)return t.instance=r,qe(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return qe(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),$d(r,n.precedence,e),t.instance=r;case"stylesheet":a=Td(n.href);var o=e.querySelector(Ad(a));if(o)return t.state.loading|=4,t.instance=o,qe(o),o;r=jd(n),(a=_d.get(a))&&Dd(r,a),qe(o=(e.ownerDocument||e).createElement("link"));var s=o;return s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(o,"link",r),t.state.loading|=4,$d(o,n.precedence,e),t.instance=o;case"script":return o=Rd(n.src),(a=e.querySelector(Nd(o)))?(t.instance=a,qe(a),a):(r=n,(a=_d.get(o))&&Id(r=f({},n),a),qe(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,$d(r,n.precedence,e));return t.instance}function $d(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,o=a,i=0;i<r.length;i++){var s=r[i];if(s.dataset.precedence===t)o=s;else if(o!==a)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Id(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var zd=null;function Md(e,t,n){if(null===zd){var r=new Map,a=zd=new Map;a.set(n,r)}else(r=(a=zd).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var o=n[a];if(!(o[Me]||o[Re]||"link"===e&&"stylesheet"===o.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==o.namespaceURI){var i=o.getAttribute(t)||"";i=e+i;var s=r.get(i);s?s.push(o):r.set(i,[o])}}return r}function Fd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Ud(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Bd=null;function Hd(){}function Wd(){if(this.count--,0===this.count)if(this.stylesheets)Vd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var qd=null;function Vd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,qd=new Map,t.forEach(Kd,e),qd=null,Wd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=qd.get(e);if(n)var r=n.get(null);else{n=new Map,qd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<a.length;o++){var i=a[o];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(a=t.instance).getAttribute("data-precedence"),(o=n.get(i)||r)===r&&n.set(null,a),n.set(i,a),this.count++,r=Wd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),o?o.parentNode.insertBefore(a,o.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Jd={$$typeof:k,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Yd(e,t,n,r,a,o,i,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=xe(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xe(0),this.hiddenUpdates=xe(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=o,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function Gd(e,t,n,r,a,o,i,s,l,u,c,d){return e=new Yd(e,t,n,i,s,l,u,d),t=1,!0===o&&(t|=24),o=Dr(3,null,null,t),e.current=o,o.stateNode=e,(t=Na()).refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:r,isDehydrated:n,cache:t},no(o),e}function Qd(e){return e?e=Lr:Lr}function Xd(e,t,n,r,a,o){a=Qd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ao(t)).payload={element:n},null!==(o=void 0===o?null:o)&&(r.callback=o),null!==(n=oo(e,r,t))&&(Du(n,0,t),io(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=jr(e,67108864);null!==t&&Du(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=L.T;L.T=null;var o=$.p;try{$.p=2,of(e,t,n,r)}finally{$.p=o,L.T=a}}function af(e,t,n,r){var a=L.T;L.T=null;var o=$.p;try{$.p=8,of(e,t,n,r)}finally{$.p=o,L.T=a}}function of(e,t,n,r){if(nf){var a=sf(r);if(null===a)Bc(e,t,r,lf,n),bf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=wf(ff,e,t,n,r,a),!0;case"dragenter":return hf=wf(hf,e,t,n,r,a),!0;case"mouseover":return pf=wf(pf,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return gf.set(o,wf(gf.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,mf.set(o,wf(mf.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<yf.indexOf(e)){for(;null!==a;){var o=Be(a);if(null!==o)switch(o.tag){case 3:if((o=o.stateNode).current.memoizedState.isDehydrated){var i=ye(o.pendingLanes);if(0!==i){var s=o;for(s.pendingLanes|=2,s.entangledLanes|=2;i;){var l=1<<31-he(i);s.entanglements[1]|=l,i&=~l}_c(o),0===(6&nu)&&(_u=te()+500,Sc(0,!1))}}break;case 13:null!==(s=jr(o,2))&&Du(s,0,2),Uu(),ef(o,2)}if(null===(o=sf(r))&&Bc(e,t,r,lf,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Bc(e,t,r,null,n)}}function sf(e){return uf(e=jt(e))}var lf=null;function uf(e){if(lf=null,null!==(e=Ue(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case oe:case ie:return 32;case se:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,hf=null,pf=null,gf=new Map,mf=new Map,vf=[],yf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":hf=null;break;case"mouseover":case"mouseout":pf=null;break;case"pointerover":case"pointerout":gf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":mf.delete(t.pointerId)}}function wf(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=Be(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function kf(e){var t=Ue(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=$.p;try{return $.p=e,t()}finally{$.p=n}}(e.priority,function(){if(13===n.tag){var e=Lu();e=Pe(e);var t=jr(n,e);null!==t&&Du(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function _f(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=sf(e.nativeEvent);if(null!==n)return null!==(t=Be(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);At=r,n.target.dispatchEvent(r),At=null,t.shift()}return!0}function Sf(e,t,n){_f(e)&&n.delete(t)}function xf(){df=!1,null!==ff&&_f(ff)&&(ff=null),null!==hf&&_f(hf)&&(hf=null),null!==pf&&_f(pf)&&(pf=null),gf.forEach(Sf),mf.forEach(Sf)}function Ef(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,xf)))}var Cf=null;function Of(e){Cf!==e&&(Cf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Cf===e&&(Cf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===uf(r||n))continue;break}var o=Be(n);null!==o&&(e.splice(t,3),t-=3,ji(o,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Pf(e){function t(t){return Ef(t,e)}null!==ff&&Ef(ff,e),null!==hf&&Ef(hf,e),null!==pf&&Ef(pf,e),gf.forEach(t),mf.forEach(t);for(var n=0;n<vf.length;n++){var r=vf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<vf.length&&null===(n=vf[0]).blockedOn;)kf(n),null===n.blockedOn&&vf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],o=n[r+1],i=a[Ne]||null;if("function"===typeof o)i||Of(n);else if(i){var s=null;if(o&&o.hasAttribute("formAction")){if(a=o,i=o[Ne]||null)s=i.formAction;else if(null!==uf(a))continue}else s=i.action;"function"===typeof s?n[r+1]=s:(n.splice(r,3),r-=3),Of(n)}}}function Tf(e){this._internalRoot=e}function Af(e){this._internalRoot=e}Af.prototype.render=Tf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Xd(t.current,Lu(),e,t,null,null)},Af.prototype.unmount=Tf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Xd(e.current,2,null,e,null,null),Uu(),t[Le]=null}},Af.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ae();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vf.length&&0!==t&&t<vf[n].priority;n++);vf.splice(n,0,e),0===n&&kf(e)}};var jf=a.version;if("19.1.0"!==jf)throw Error(i(527,jf,"19.1.0"));$.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return c(a),e;if(o===r)return c(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,u=a.child;u;){if(u===n){s=!0,n=a,r=o;break}if(u===r){s=!0,r=a,n=o;break}u=u.sibling}if(!s){for(u=o.child;u;){if(u===n){s=!0,n=o,r=a;break}if(u===r){s=!0,r=o,n=a;break}u=u.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Rf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Nf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Nf.isDisabled&&Nf.supportsFiber)try{ce=Nf.inject(Rf),de=Nf}catch($f){}}t.createRoot=function(e,t){if(!s(e))throw Error(i(299));var n=!1,r="",a=ys,o=bs,l=ws;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(o=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Gd(e,1,!1,null,0,n,r,a,o,l,0,null),e[Le]=t.current,Fc(e),new Tf(t)},t.hydrateRoot=function(e,t,n){if(!s(e))throw Error(i(299));var r=!1,a="",o=ys,l=bs,u=ws,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(o=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Gd(e,1,!0,t,0,r,a,o,l,u,0,c)).context=Qd(null),n=t.current,(a=ao(r=Pe(r=Lu()))).callback=null,oo(n,a,r),n=r,t.current.lanes=n,Ee(t,n),_c(t),e[Le]=t.current,Fc(e),new Af(t)},t.version="19.1.0"},7071:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(5043),a=(n(8457),n(6218),n(8894));n(9753);n(2665),n(4696);n(2677);new WeakMap;var o=n(4140),i=n(579);const s=["onKeyDown"];const l=r.forwardRef((e,t)=>{let{onKeyDown:n}=e,r=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,s);const[l]=(0,o.Am)(Object.assign({tagName:"a"},r)),u=(0,a.A)(e=>{l.onKeyDown(e),null==n||n(e)});return(c=r.href)&&"#"!==c.trim()&&"button"!==r.role?(0,i.jsx)("a",Object.assign({ref:t},r,{onKeyDown:n})):(0,i.jsx)("a",Object.assign({ref:t},r,l,{onKeyDown:u}));var c});l.displayName="Anchor";const u=l},7374:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(9286));class o extends a.default{select(e){let t=!1;const n=(null!==e&&void 0!==e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",n),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e){let{ascending:t=!0,nullsFirst:n,foreignTable:r,referencedTable:a=r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=a?`${a}.order`:"order",i=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${i?`${i},`:""}${e}.${t?"asc":"desc"}${void 0===n?"":n?".nullsfirst":".nullslast"}`),this}limit(e){let{foreignTable:t,referencedTable:n=t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r="undefined"===typeof n?"limit":`${n}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t){let{foreignTable:n,referencedTable:r=n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a="undefined"===typeof r?"offset":`${r}.offset`,o="undefined"===typeof r?"limit":`${r}.limit`;return this.url.searchParams.set(a,`${e}`),this.url.searchParams.set(o,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain(){let{analyze:e=!1,verbose:t=!1,settings:n=!1,buffers:r=!1,wal:a=!1,format:o="text"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var i;const s=[e?"analyze":null,t?"verbose":null,n?"settings":null,r?"buffers":null,a?"wal":null].filter(Boolean).join("|"),l=null!==(i=this.headers.Accept)&&void 0!==i?i:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${l}"; options=${s};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=o},7852:(e,t,n)=>{"use strict";n.d(t,{Jm:()=>d,Wz:()=>f,gy:()=>c,oU:()=>u});var r=n(5043);n(579);const a=["xxl","xl","lg","md","sm","xs"],o="xs",i=r.createContext({prefixes:{},breakpoints:a,minBreakpoint:o}),{Consumer:s,Provider:l}=i;function u(e,t){const{prefixes:n}=(0,r.useContext)(i);return e||n[t]||t}function c(){const{breakpoints:e}=(0,r.useContext)(i);return e}function d(){const{minBreakpoint:e}=(0,r.useContext)(i);return e}function f(){const{dir:e}=(0,r.useContext)(i);return"rtl"===e}},7950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(6672)},7980:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;const a=r(n(1210));t.PostgrestClient=a.default;const o=r(n(8829));t.PostgrestQueryBuilder=o.default;const i=r(n(5736));t.PostgrestFilterBuilder=i.default;const s=r(n(7374));t.PostgrestTransformBuilder=s.default;const l=r(n(9286));t.PostgrestBuilder=l.default;const u=r(n(2611));t.PostgrestError=u.default,t.default={PostgrestClient:a.default,PostgrestQueryBuilder:o.default,PostgrestFilterBuilder:i.default,PostgrestTransformBuilder:s.default,PostgrestBuilder:l.default,PostgrestError:u.default}},8062:(e,t,n)=>{"use strict";n.d(t,{_K:()=>f,ns:()=>d,kp:()=>c,ze:()=>h,Ay:()=>m});var r=n(8587);function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var o=n(5043),i=n(7950);const s=!1,l=o.createContext(null);var u="unmounted",c="exited",d="entering",f="entered",h="exiting",p=function(e){var t,n;function p(t,n){var r;r=e.call(this,t,n)||this;var a,o=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?o?(a=c,r.appearStatus=d):a=f:a=t.unmountOnExit||t.mountOnEnter?u:c,r.state={status:a},r.nextCallback=null,r}n=e,(t=p).prototype=Object.create(n.prototype),t.prototype.constructor=t,a(t,n),p.getDerivedStateFromProps=function(e,t){return e.in&&t.status===u?{status:c}:null};var g=p.prototype;return g.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},g.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==d&&n!==f&&(t=d):n!==d&&n!==f||(t=h)}this.updateStatus(!1,t)},g.componentWillUnmount=function(){this.cancelNextCallback()},g.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!==typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},g.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===d){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:i.findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===c&&this.setState({status:u})},g.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,a=this.props.nodeRef?[r]:[i.findDOMNode(this),r],o=a[0],l=a[1],u=this.getTimeouts(),c=r?u.appear:u.enter;!e&&!n||s?this.safeSetState({status:f},function(){t.props.onEntered(o)}):(this.props.onEnter(o,l),this.safeSetState({status:d},function(){t.props.onEntering(o,l),t.onTransitionEnd(c,function(){t.safeSetState({status:f},function(){t.props.onEntered(o,l)})})}))},g.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:i.findDOMNode(this);t&&!s?(this.props.onExit(r),this.safeSetState({status:h},function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,function(){e.safeSetState({status:c},function(){e.props.onExited(r)})})})):this.safeSetState({status:c},function(){e.props.onExited(r)})},g.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},g.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},g.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},g.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:i.findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var a=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],o=a[0],s=a[1];this.props.addEndListener(o,s)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},g.render=function(){var e=this.state.status;if(e===u)return null;var t=this.props,n=t.children,a=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,(0,r.A)(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return o.createElement(l.Provider,{value:null},"function"===typeof n?n(e,a):o.cloneElement(o.Children.only(n),a))},p}(o.Component);function g(){}p.contextType=l,p.propTypes={},p.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:g,onEntering:g,onEntered:g,onExit:g,onExiting:g,onExited:g},p.UNMOUNTED=u,p.EXITED=c,p.ENTERING=d,p.ENTERED=f,p.EXITING=h;const m=p},8072:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var r=n(8139),a=n.n(r),o=n(5043),i=n(8062),s=n(9791),l=n(9841),u=n(2643),c=n(865),d=n(579);const f={[i.ns]:"show",[i._K]:"show"},h=o.forwardRef((e,t)=>{let{className:n,children:r,transitionClasses:i={},onEnter:h,...p}=e;const g={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...p},m=(0,o.useCallback)((e,t)=>{(0,u.A)(e),null==h||h(e,t)},[h]);return(0,d.jsx)(c.A,{ref:t,addEndListener:l.A,...g,onEnter:m,childRef:(0,s.am)(r),children:(e,t)=>o.cloneElement(r,{...t,className:a()("fade",n,r.props.className,f[e],i[e])})})});h.displayName="Fade";const p=h},8139:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,o(n)))}return e}function o(e){if("string"===typeof e||"number"===typeof e)return e;if("object"!==typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()},8279:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=!("undefined"===typeof window||!window.document||!window.document.createElement)},8293:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043);const a=e=>e&&"function"!==typeof e?t=>{e.current=t}:e;const o=function(e,t){return(0,r.useMemo)(()=>function(e,t){const n=a(e),r=a(t);return e=>{n&&n(e),r&&r(e)}}(e,t),[e,t])}},8457:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043);function a(){return(0,r.useState)(null)}},8466:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=n(5043).createContext(null)},8587:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}n.d(t,{A:()=>r})},8747:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(182);function a(e,t){return function(e){var t=(0,r.A)(e);return t&&t.defaultView||window}(e).getComputedStyle(e,t)}var o=/([A-Z])/g;var i=/^ms-/;function s(e){return function(e){return e.replace(o,"-$1").toLowerCase()}(e).replace(i,"-ms-")}var l=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;const u=function(e,t){var n="",r="";if("string"===typeof t)return e.style.getPropertyValue(s(t))||a(e).getPropertyValue(s(t));Object.keys(t).forEach(function(a){var o=t[a];o||0===o?!function(e){return!(!e||!l.test(e))}(a)?n+=s(a)+": "+o+";":r+=a+"("+o+") ":e.style.removeProperty(s(a))}),r&&(n+="transform: "+r+";"),e.style.cssText+=";"+n}},8829:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(5736));t.default=class{constructor(e,t){let{headers:n={},schema:r,fetch:a}=t;this.url=e,this.headers=n,this.schema=r,this.fetch=a}select(e){let{head:t=!1,count:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=t?"HEAD":"GET";let o=!1;const i=(null!==e&&void 0!==e?e:"*").split("").map(e=>/\s/.test(e)&&!o?"":('"'===e&&(o=!o),e)).join("");return this.url.searchParams.set("select",i),n&&(this.headers.Prefer=`count=${n}`),new a.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e){let{count:t,defaultToNull:n=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),n||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new a.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e){let{onConflict:t,ignoreDuplicates:n=!1,count:r,defaultToNull:o=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=[`resolution=${n?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&i.push(this.headers.Prefer),r&&i.push(`count=${r}`),o||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new a.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e){let{count:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=[];return this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),this.headers.Prefer=n.join(","),new a.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete(){let{count:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new a.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}},8843:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043);function a(){const[,e]=(0,r.useReducer)(e=>e+1,0);return e}},8853:(e,t,n)=>{"use strict";e.exports=n(5896)},8894:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043),a=n(6218);function o(e){const t=(0,a.A)(e);return(0,r.useCallback)(function(){return t.current&&t.current(...arguments)},[t])}},9048:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});const r=n(5043).createContext(null);r.displayName="NavContext";const a=r},9125:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});const r=n(5043).createContext(null);r.displayName="NavbarContext";const a=r},9286:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=r(n(4630)),o=r(n(2611));t.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"===typeof fetch?this.fetch=a.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let n=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,n,r;let a=null,i=null,s=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(i="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),o=null===(n=e.headers.get("content-range"))||void 0===n?void 0:n.split("/");r&&o&&o.length>1&&(s=parseInt(o[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(i)&&(i.length>1?(a={code:"PGRST116",details:`Results contain ${i.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},i=null,s=null,l=406,u="Not Acceptable"):i=1===i.length?i[0]:null)}else{const t=await e.text();try{a=JSON.parse(t),Array.isArray(a)&&404===e.status&&(i=[],a=null,l=200,u="OK")}catch(c){404===e.status&&""===t?(l=204,u="No Content"):a={message:t}}if(a&&this.isMaybeSingle&&(null===(r=null===a||void 0===a?void 0:a.details)||void 0===r?void 0:r.includes("0 rows"))&&(a=null,l=200,u="OK"),a&&this.shouldThrowOnError)throw new o.default(a)}return{error:a,data:i,count:s,status:l,statusText:u}});return this.shouldThrowOnError||(n=n.catch(e=>{var t,n,r;return{error:{message:`${null!==(t=null===e||void 0===e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null===e||void 0===e?void 0:e.message}`,details:`${null!==(n=null===e||void 0===e?void 0:e.stack)&&void 0!==n?n:""}`,hint:"",code:`${null!==(r=null===e||void 0===e?void 0:e.code)&&void 0!==r?r:""}`},data:null,count:null,status:0,statusText:""}})),n.then(e,t)}returns(){return this}overrideTypes(){return this}}},9753:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5043),a=n(8894);function o(e,t,n){let o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=(0,a.A)(n);(0,r.useEffect)(()=>{const n="function"===typeof e?e():e;return n.addEventListener(t,i,o),()=>n.removeEventListener(t,i,o)},[e])}},9791:(e,t,n)=>{"use strict";n.d(t,{am:()=>o,v$:()=>a});var r=n(5043);function a(e){return"Escape"===e.code||27===e.keyCode}function o(e){if(!e||"function"===typeof e)return null;const{major:t}=function(){const e=r.version.split(".");return{major:+e[0],minor:+e[1],patch:+e[2]}}();return t>=19?e.props.ref:e.ref}},9841:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(8747),a=n(1094);function o(e,t,n){void 0===n&&(n=5);var r=!1,o=setTimeout(function(){r||function(e,t,n,r){if(void 0===n&&(n=!1),void 0===r&&(r=!0),e){var a=document.createEvent("HTMLEvents");a.initEvent(t,n,r),e.dispatchEvent(a)}}(e,"transitionend",!0)},t+n),i=(0,a.A)(e,"transitionend",function(){r=!0},{once:!0});return function(){clearTimeout(o),i()}}function i(e,t,n,i){null==n&&(n=function(e){var t=(0,r.A)(e,"transitionDuration")||"",n=-1===t.indexOf("ms")?1e3:1;return parseFloat(t)*n}(e)||0);var s=o(e,n,i),l=(0,a.A)(e,"transitionend",t);return function(){s(),l()}}function s(e,t){const n=(0,r.A)(e,t)||"",a=-1===n.indexOf("ms")?1e3:1;return parseFloat(n)*a}function l(e,t){const n=s(e,"transitionDuration"),r=s(e,"transitionDelay"),a=i(e,n=>{n.target===e&&(a(),t(n))},n+r)}}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+"."+{53:"818c7207",93:"4cc1ddf4",141:"14649523",145:"e68c852e",190:"2a8236f7",254:"5d8db93d",285:"419abf5e",365:"7aaa2f05",455:"33315ca0",505:"ade57923",560:"90e2b36f",588:"a67b12ea",592:"24acd47e",622:"2f5694c5",668:"a183cab0",738:"d9ae6fa6",741:"801ddc5b",880:"17ebb583"}[e]+".chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="frontend:";n.l=(r,a,o,i)=>{if(e[r])e[r].push(a);else{var s,l;if(void 0!==o)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+o),s.src=r),e[r]=[a];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(h);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(e=>e(n)),t)return t(n)},h=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="./",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=o);var i=n.p+n.u(t),s=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",s.name="ChunkLoadError",s.type=o,s.request=i,a[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,o,i=r[0],s=r[1],l=r[2],u=0;if(i.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)l(n)}for(t&&t(r);u<i.length;u++)o=i[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkfrontend=self.webpackChunkfrontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e=n(5043),t=n(4391);const r=e=>"string"===typeof e,a=()=>{let e,t;const n=new Promise((n,r)=>{e=n,t=r});return n.resolve=e,n.reject=t,n},o=e=>null==e?"":""+e,i=/###/g,s=e=>e&&e.indexOf("###")>-1?e.replace(i,"."):e,l=e=>!e||r(e),u=(e,t,n)=>{const a=r(t)?t.split("."):t;let o=0;for(;o<a.length-1;){if(l(e))return{};const t=s(a[o]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++o}return l(e)?{}:{obj:e,k:s(a[o])}},c=(e,t,n)=>{const{obj:r,k:a}=u(e,t,Object);if(void 0!==r||1===t.length)return void(r[a]=n);let o=t[t.length-1],i=t.slice(0,t.length-1),s=u(e,i,Object);for(;void 0===s.obj&&i.length;)o=`${i[i.length-1]}.${o}`,i=i.slice(0,i.length-1),s=u(e,i,Object),s?.obj&&"undefined"!==typeof s.obj[`${s.k}.${o}`]&&(s.obj=void 0);s.obj[`${s.k}.${o}`]=n},d=(e,t)=>{const{obj:n,k:r}=u(e,t);if(n&&Object.prototype.hasOwnProperty.call(n,r))return n[r]},f=(e,t,n)=>{for(const a in t)"__proto__"!==a&&"constructor"!==a&&(a in e?r(e[a])||e[a]instanceof String||r(t[a])||t[a]instanceof String?n&&(e[a]=t[a]):f(e[a],t[a],n):e[a]=t[a]);return e},h=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var p={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const g=e=>r(e)?e.replace(/[&<>"'\/]/g,e=>p[e]):e;const m=[" ",",","?","!",";"],v=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}(20),y=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}const r=t.split(n);let a=e;for(let o=0;o<r.length;){if(!a||"object"!==typeof a)return;let e,t="";for(let i=o;i<r.length;++i)if(i!==o&&(t+=n),t+=r[i],e=a[t],void 0!==e){if(["string","number","boolean"].indexOf(typeof e)>-1&&i<r.length-1)continue;o+=i-o+1;break}a=e}return a},b=e=>e?.replace("_","-"),w={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}};class k{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||w,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,a){return a&&!this.debug?null:(r(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new k(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new k(this.logger,e)}}var _=new k;class S{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);const n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)}),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(this.observers[e]){Array.from(this.observers[e].entries()).forEach(e=>{let[t,r]=e;for(let a=0;a<r;a++)t(...n)})}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach(t=>{let[r,a]=t;for(let o=0;o<a;o++)r.apply(r,[e,...n])})}}}class x extends S{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator,i=void 0!==a.ignoreJSONStructure?a.ignoreJSONStructure:this.options.ignoreJSONStructure;let s;e.indexOf(".")>-1?s=e.split("."):(s=[e,t],n&&(Array.isArray(n)?s.push(...n):r(n)&&o?s.push(...n.split(o)):s.push(n)));const l=d(this.data,s);return!l&&!t&&!n&&e.indexOf(".")>-1&&(e=s[0],t=s[1],n=s.slice(2).join(".")),!l&&i&&r(n)?y(this.data?.[e]?.[t],n,o):l}addResource(e,t,n,r){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1};const o=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator;let i=[e,t];n&&(i=i.concat(o?n.split(o):n)),e.indexOf(".")>-1&&(i=e.split("."),r=t,t=i[1]),this.addNamespaces(t),c(this.data,i,r),a.silent||this.emit("added",e,t,n,r)}addResources(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(const o in n)(r(n[o])||Array.isArray(n[o]))&&this.addResource(e,t,o,n[o],{silent:!0});a.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,r,a){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},i=[e,t];e.indexOf(".")>-1&&(i=e.split("."),r=n,n=t,t=i[1]),this.addNamespaces(t);let s=d(this.data,i)||{};o.skipCopy||(n=JSON.parse(JSON.stringify(n))),r?f(s,n,a):s={...s,...n},c(this.data,i,s),o.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var E={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,a){return e.forEach(e=>{t=this.processors[e]?.process(t,n,r,a)??t}),t}};const C={},O=e=>!r(e)&&"boolean"!==typeof e&&"number"!==typeof e;class P extends S{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),((e,t,n)=>{e.forEach(e=>{t[e]&&(n[e]=t[e])})})(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=_.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){const t={...arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}}};if(null==e)return!1;const n=this.resolve(e,t);return void 0!==n?.res}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");const a=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator;let o=t.ns||this.options.defaultNS||[];const i=n&&e.indexOf(n)>-1,s=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!((e,t,n)=>{t=t||"",n=n||"";const r=m.filter(e=>t.indexOf(e)<0&&n.indexOf(e)<0);if(0===r.length)return!0;const a=v.getRegExp(`(${r.map(e=>"?"===e?"\\?":e).join("|")})`);let o=!a.test(e);if(!o){const t=e.indexOf(n);t>0&&!a.test(e.substring(0,t))&&(o=!0)}return o})(e,n,a);if(i&&!s){const t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:r(o)?[o]:o};const i=e.split(n);(n!==a||n===a&&this.options.ns.indexOf(i[0])>-1)&&(o=i.shift()),e=i.join(a)}return{key:e,namespaces:r(o)?[o]:o}}translate(e,t,n){let a="object"===typeof t?{...t}:t;if("object"!==typeof a&&this.options.overloadTranslationOptionHandler&&(a=this.options.overloadTranslationOptionHandler(arguments)),"object"===typeof options&&(a={...a}),a||(a={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);const o=void 0!==a.returnDetails?a.returnDetails:this.options.returnDetails,i=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator,{key:s,namespaces:l}=this.extractFromKey(e[e.length-1],a),u=l[l.length-1];let c=void 0!==a.nsSeparator?a.nsSeparator:this.options.nsSeparator;void 0===c&&(c=":");const d=a.lng||this.language,f=a.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if("cimode"===d?.toLowerCase())return f?o?{res:`${u}${c}${s}`,usedKey:s,exactUsedKey:s,usedLng:d,usedNS:u,usedParams:this.getUsedParamsDetails(a)}:`${u}${c}${s}`:o?{res:s,usedKey:s,exactUsedKey:s,usedLng:d,usedNS:u,usedParams:this.getUsedParamsDetails(a)}:s;const h=this.resolve(e,a);let p=h?.res;const g=h?.usedKey||s,m=h?.exactUsedKey||s,v=void 0!==a.joinArrays?a.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,b=void 0!==a.count&&!r(a.count),w=P.hasDefaultValue(a),k=b?this.pluralResolver.getSuffix(d,a.count,a):"",_=a.ordinal&&b?this.pluralResolver.getSuffix(d,a.count,{ordinal:!1}):"",S=b&&!a.ordinal&&0===a.count,x=S&&a[`defaultValue${this.options.pluralSeparator}zero`]||a[`defaultValue${k}`]||a[`defaultValue${_}`]||a.defaultValue;let E=p;y&&!p&&w&&(E=x);const C=O(E),T=Object.prototype.toString.apply(E);if(!(y&&E&&C&&["[object Number]","[object Function]","[object RegExp]"].indexOf(T)<0)||r(v)&&Array.isArray(E))if(y&&r(v)&&Array.isArray(p))p=p.join(v),p&&(p=this.extendTranslation(p,e,a,n));else{let t=!1,r=!1;!this.isValidLookup(p)&&w&&(t=!0,p=x),this.isValidLookup(p)||(r=!0,p=s);const o=(a.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&r?void 0:p,l=w&&x!==p&&this.options.updateMissing;if(r||t||l){if(this.logger.log(l?"updateKey":"missingKey",d,u,s,l?x:p),i){const e=this.resolve(s,{...a,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,a.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let r=0;r<t.length;r++)e.push(t[r]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(a.lng||this.language):e.push(a.lng||this.language);const n=(e,t,n)=>{const r=w&&n!==p?n:o;this.options.missingKeyHandler?this.options.missingKeyHandler(e,u,t,r,l,a):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,u,t,r,l,a),this.emit("missingKey",e,u,t,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&b?e.forEach(e=>{const t=this.pluralResolver.getSuffixes(e,a);S&&a[`defaultValue${this.options.pluralSeparator}zero`]&&t.indexOf(`${this.options.pluralSeparator}zero`)<0&&t.push(`${this.options.pluralSeparator}zero`),t.forEach(t=>{n([e],s+t,a[`defaultValue${t}`]||x)})}):n(e,s,x))}p=this.extendTranslation(p,e,a,h,n),r&&p===s&&this.options.appendNamespaceToMissingKey&&(p=`${u}${c}${s}`),(r||t)&&this.options.parseMissingKeyHandler&&(p=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${c}${s}`:s,t?p:void 0,a))}else{if(!a.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,E,{...a,ns:l}):`key '${s} (${this.language})' returned an object instead of string.`;return o?(h.res=e,h.usedParams=this.getUsedParamsDetails(a),h):e}if(i){const e=Array.isArray(E),t=e?[]:{},n=e?m:g;for(const r in E)if(Object.prototype.hasOwnProperty.call(E,r)){const e=`${n}${i}${r}`;t[r]=w&&!p?this.translate(e,{...a,defaultValue:O(x)?x[r]:void 0,joinArrays:!1,ns:l}):this.translate(e,{...a,joinArrays:!1,ns:l}),t[r]===e&&(t[r]=E[r])}p=t}}return o?(h.res=p,h.usedParams=this.getUsedParamsDetails(a),h):p}extendTranslation(e,t,n,a,o){var i=this;if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||a.usedLng,a.usedNS,a.usedKey,{resolved:a});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});const s=r(e)&&(void 0!==n?.interpolation?.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let l;if(s){const t=e.match(this.interpolator.nestingRegexp);l=t&&t.length}let u=n.replace&&!r(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(u={...this.options.interpolation.defaultVariables,...u}),e=this.interpolator.interpolate(e,u,n.lng||this.language||a.usedLng,n),s){const t=e.match(this.interpolator.nestingRegexp);l<(t&&t.length)&&(n.nest=!1)}!n.lng&&a&&a.res&&(n.lng=this.language||a.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return o?.[0]!==r[0]||n.context?i.translate(...r,t):(i.logger.warn(`It seems you are nesting recursively key: ${r[0]} in key: ${t[0]}`),null)},n)),n.interpolation&&this.interpolator.reset()}const s=n.postProcess||this.options.postProcess,l=r(s)?[s]:s;return null!=e&&l?.length&&!1!==n.applyPostProcessor&&(e=E.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...a,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t,n,a,o,i,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;const l=this.extractFromKey(e,s),u=l.key;n=u;let c=l.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));const d=void 0!==s.count&&!r(s.count),f=d&&!s.ordinal&&0===s.count,h=void 0!==s.context&&(r(s.context)||"number"===typeof s.context)&&""!==s.context,p=s.lngs?s.lngs:this.languageUtils.toResolveHierarchy(s.lng||this.language,s.fallbackLng);c.forEach(e=>{this.isValidLookup(t)||(i=e,C[`${p[0]}-${e}`]||!this.utils?.hasLoadedNamespace||this.utils?.hasLoadedNamespace(i)||(C[`${p[0]}-${e}`]=!0,this.logger.warn(`key "${n}" for languages "${p.join(", ")}" won't get resolved as namespace "${i}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(n=>{if(this.isValidLookup(t))return;o=n;const r=[u];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(r,u,n,e,s);else{let e;d&&(e=this.pluralResolver.getSuffix(n,s.count,s));const t=`${this.options.pluralSeparator}zero`,a=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(d&&(r.push(u+e),s.ordinal&&0===e.indexOf(a)&&r.push(u+e.replace(a,this.options.pluralSeparator)),f&&r.push(u+t)),h){const n=`${u}${this.options.contextSeparator}${s.context}`;r.push(n),d&&(r.push(n+e),s.ordinal&&0===e.indexOf(a)&&r.push(n+e.replace(a,this.options.pluralSeparator)),f&&r.push(n+t))}}let i;for(;i=r.pop();)this.isValidLookup(t)||(a=i,t=this.getResource(n,e,i,s))}))})}),{res:t,usedKey:n,exactUsedKey:a,usedLng:o,usedNS:i}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!r(e.replace);let a=n?e.replace:e;if(n&&"undefined"!==typeof e.count&&(a.count=e.count),this.options.interpolation.defaultVariables&&(a={...this.options.interpolation.defaultVariables,...a}),!n){a={...a};for(const e of t)delete a[e]}return a}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,12)&&void 0!==e[n])return!0;return!1}}class T{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=_.create("languageUtils")}getScriptPartFromCode(e){if(!(e=b(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=b(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(r(e)&&e.indexOf("-")>-1){let n;try{n=Intl.getCanonicalLocales(e)[0]}catch(t){}return n&&this.options.lowerCaseLng&&(n=n.toLowerCase()),n||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(e=>{if(t)return;const n=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(n)||(t=n)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;const n=this.getScriptPartFromCode(e);if(this.isSupportedCode(n))return t=n;const r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return t=r;t=this.options.supportedLngs.find(e=>e===r?e:e.indexOf("-")<0&&r.indexOf("-")<0?void 0:e.indexOf("-")>0&&r.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&r.length>1?e:void 0)}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if("function"===typeof e&&(e=e(t)),r(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes((!1===t?[]:t)||this.options.fallbackLng||[],e),a=[],o=e=>{e&&(this.isSupportedCode(e)?a.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return r(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&o(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&o(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&o(this.getLanguagePartFromCode(e))):r(e)&&o(this.formatLanguageCode(e)),n.forEach(e=>{a.indexOf(e)<0&&o(this.formatLanguageCode(e))}),a}}const A={zero:0,one:1,two:2,few:3,many:4,other:5},j={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class R{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=_.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=b("dev"===e?"en":e),r=t.ordinal?"ordinal":"cardinal",a=JSON.stringify({cleanedCode:n,type:r});if(a in this.pluralRulesCache)return this.pluralRulesCache[a];let o;try{o=new Intl.PluralRules(n,{type:r})}catch(i){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),j;if(!e.match(/-|_/))return j;const n=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(n,t)}return this.pluralRulesCache[a]=o,o}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),n?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),n?n.resolvedOptions().pluralCategories.sort((e,t)=>A[e]-A[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):[]}getSuffix(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=this.getRule(e,n);return r?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${r.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,n))}}const N=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],i=((e,t,n)=>{const r=d(e,n);return void 0!==r?r:d(t,n)})(e,t,n);return!i&&o&&r(n)&&(i=y(e,n,a),void 0===i&&(i=y(t,n,a))),i},L=e=>e.replace(/\$/g,"$$$$");class ${constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=_.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:r,prefix:a,prefixEscaped:o,suffix:i,suffixEscaped:s,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:f,nestingSuffix:p,nestingSuffixEscaped:m,nestingOptionsSeparator:v,maxReplaces:y,alwaysFormat:b}=e.interpolation;this.escape=void 0!==t?t:g,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==r&&r,this.prefix=a?h(a):o||"{{",this.suffix=i?h(i):s||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?h(d):f||h("$t("),this.nestingSuffix=p?h(p):m||h(")"),this.nestingOptionsSeparator=v||",",this.maxReplaces=y||1e3,this.alwaysFormat=void 0!==b&&b,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e?.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,a){let i,s,l;const u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=e=>{if(e.indexOf(this.formatSeparator)<0){const r=N(t,u,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(r,void 0,n,{...a,...t,interpolationkey:e}):r}const r=e.split(this.formatSeparator),o=r.shift().trim(),i=r.join(this.formatSeparator).trim();return this.format(N(t,u,o,this.options.keySeparator,this.options.ignoreJSONStructure),i,n,{...a,...t,interpolationkey:o})};this.resetRegExp();const d=a?.missingInterpolationHandler||this.options.missingInterpolationHandler,f=void 0!==a?.interpolation?.skipOnVariables?a.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>L(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?L(this.escape(e)):L(e)}].forEach(t=>{for(l=0;i=t.regex.exec(e);){const n=i[1].trim();if(s=c(n),void 0===s)if("function"===typeof d){const t=d(e,i,a);s=r(t)?t:""}else if(a&&Object.prototype.hasOwnProperty.call(a,n))s="";else{if(f){s=i[0];continue}this.logger.warn(`missed to pass in variable ${n} for interpolating ${e}`),s=""}else r(s)||this.useRawValueToEscape||(s=o(s));const u=t.safeValue(s);if(e=e.replace(i[0],u),f?(t.regex.lastIndex+=s.length,t.regex.lastIndex-=i[0].length):t.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),e}nest(e,t){let n,a,i,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const l=(e,t)=>{const n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;const r=e.split(new RegExp(`${n}[ ]*{`));let a=`{${r[1]}`;e=r[0],a=this.interpolate(a,i);const o=a.match(/'/g),s=a.match(/"/g);((o?.length??0)%2===0&&!s||s.length%2!==0)&&(a=a.replace(/'/g,'"'));try{i=JSON.parse(a),t&&(i={...t,...i})}catch(l){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,l),`${e}${n}${a}`}return i.defaultValue&&i.defaultValue.indexOf(this.prefix)>-1&&delete i.defaultValue,e};for(;n=this.nestingRegexp.exec(e);){let u=[];i={...s},i=i.replace&&!r(i.replace)?i.replace:i,i.applyPostProcessor=!1,delete i.defaultValue;const c=/{.*}/.test(n[1])?n[1].lastIndexOf("}")+1:n[1].indexOf(this.formatSeparator);if(-1!==c&&(u=n[1].slice(c).split(this.formatSeparator).map(e=>e.trim()).filter(Boolean),n[1]=n[1].slice(0,c)),a=t(l.call(this,n[1].trim(),i),i),a&&n[0]===e&&!r(a))return a;r(a)||(a=o(a)),a||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),a=""),u.length&&(a=u.reduce((e,t)=>this.format(e,t,s.lng,{...s,interpolationkey:n[1].trim()}),a.trim())),e=e.replace(n[0],a),this.regexp.lastIndex=0}return e}}const D=e=>{const t={};return(n,r,a)=>{let o=a;a&&a.interpolationkey&&a.formatParams&&a.formatParams[a.interpolationkey]&&a[a.interpolationkey]&&(o={...o,[a.interpolationkey]:void 0});const i=r+JSON.stringify(o);let s=t[i];return s||(s=e(b(r),a),t[i]=s),s(n)}},I=e=>(t,n,r)=>e(b(n),r)(t);class z{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=_.create("formatter"),this.options=e,this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||",";const n=t.cacheInBuiltFormats?D:I;this.formats={number:n((e,t)=>{const n=new Intl.NumberFormat(e,{...t});return e=>n.format(e)}),currency:n((e,t)=>{const n=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>n.format(e)}),datetime:n((e,t)=>{const n=new Intl.DateTimeFormat(e,{...t});return e=>n.format(e)}),relativetime:n((e,t)=>{const n=new Intl.RelativeTimeFormat(e,{...t});return e=>n.format(e,t.range||"day")}),list:n((e,t)=>{const n=new Intl.ListFormat(e,{...t});return e=>n.format(e)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=D(t)}format(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const a=t.split(this.formatSeparator);if(a.length>1&&a[0].indexOf("(")>1&&a[0].indexOf(")")<0&&a.find(e=>e.indexOf(")")>-1)){const e=a.findIndex(e=>e.indexOf(")")>-1);a[0]=[a[0],...a.splice(1,e)].join(this.formatSeparator)}return a.reduce((e,t)=>{const{formatName:a,formatOptions:o}=(e=>{let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const a=r[1].substring(0,r[1].length-1);"currency"===t&&a.indexOf(":")<0?n.currency||(n.currency=a.trim()):"relativetime"===t&&a.indexOf(":")<0?n.range||(n.range=a.trim()):a.split(";").forEach(e=>{if(e){const[t,...r]=e.split(":"),a=r.join(":").trim().replace(/^'+|'+$/g,""),o=t.trim();n[o]||(n[o]=a),"false"===a&&(n[o]=!1),"true"===a&&(n[o]=!0),isNaN(a)||(n[o]=parseInt(a,10))}})}return{formatName:t,formatOptions:n}})(t);if(this.formats[a]){let t=e;try{const i=r?.formatParams?.[r.interpolationkey]||{},s=i.locale||i.lng||r.locale||r.lng||n;t=this.formats[a](e,s,{...o,...r,...i})}catch(i){this.logger.warn(i)}return t}return this.logger.warn(`there was no format function for ${a}`),e},e)}}class M extends S{constructor(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=r,this.logger=_.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(n,r.backend,r)}queueLoad(e,t,n,r){const a={},o={},i={},s={};return e.forEach(e=>{let r=!0;t.forEach(t=>{const i=`${e}|${t}`;!n.reload&&this.store.hasResourceBundle(e,t)?this.state[i]=2:this.state[i]<0||(1===this.state[i]?void 0===o[i]&&(o[i]=!0):(this.state[i]=1,r=!1,void 0===o[i]&&(o[i]=!0),void 0===a[i]&&(a[i]=!0),void 0===s[t]&&(s[t]=!0)))}),r||(i[e]=!0)}),(Object.keys(a).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(a),pending:Object.keys(o),toLoadLanguages:Object.keys(i),toLoadNamespaces:Object.keys(s)}}loaded(e,t,n){const r=e.split("|"),a=r[0],o=r[1];t&&this.emit("failedLoading",a,o,t),!t&&n&&this.store.addResourceBundle(a,o,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const i={};this.queue.forEach(n=>{((e,t,n)=>{const{obj:r,k:a}=u(e,t,Object);r[a]=r[a]||[],r[a].push(n)})(n.loaded,[a],o),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach(e=>{i[e]||(i[e]={});const t=n.loaded[e];t.length&&t.forEach(t=>{void 0===i[e][t]&&(i[e][t]=!0)})}),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())}),this.emit("loaded",i),this.queue=this.queue.filter(e=>!e.done)}read(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,o=arguments.length>5?arguments[5]:void 0;if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:n,tried:r,wait:a,callback:o});this.readingCalls++;const i=(i,s)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}i&&s&&r<this.maxRetries?setTimeout(()=>{this.read.call(this,e,t,n,r+1,2*a,o)},a):o(i,s)},s=this.backend[n].bind(this.backend);if(2!==s.length)return s(e,t,i);try{const n=s(e,t);n&&"function"===typeof n.then?n.then(e=>i(null,e)).catch(i):i(null,n)}catch(l){i(l)}}prepareLoading(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),a&&a();r(e)&&(e=this.languageUtils.toResolveHierarchy(e)),r(t)&&(t=[t]);const o=this.queueLoad(e,t,n,a);if(!o.toLoad.length)return o.pending.length||a(),null;o.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const n=e.split("|"),r=n[0],a=n[1];this.read(r,a,"read",void 0,void 0,(n,o)=>{n&&this.logger.warn(`${t}loading namespace ${a} for language ${r} failed`,n),!n&&o&&this.logger.log(`${t}loaded namespace ${a} for language ${r}`,o),this.loaded(e,n,o)})}saveMissing(e,t,n,r,a){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(!this.services?.utils?.hasLoadedNamespace||this.services?.utils?.hasLoadedNamespace(t)){if(void 0!==n&&null!==n&&""!==n){if(this.backend?.create){const l={...o,isUpdate:a},u=this.backend.create.bind(this.backend);if(u.length<6)try{let a;a=5===u.length?u(e,t,n,r,l):u(e,t,n,r),a&&"function"===typeof a.then?a.then(e=>i(null,e)).catch(i):i(null,a)}catch(s){i(s)}else u(e,t,n,r,i,l)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}else this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")}}const F=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"===typeof e[1]&&(t=e[1]),r(e[1])&&(t.defaultValue=e[1]),r(e[2])&&(t.tDescription=e[2]),"object"===typeof e[2]||"object"===typeof e[3]){const n=e[3]||e[2];Object.keys(n).forEach(e=>{t[e]=n[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),U=e=>(r(e.ns)&&(e.ns=[e.ns]),r(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),r(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"===typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),B=()=>{};class H extends S{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;var n;if(super(),this.options=U(e),this.services={},this.logger=_,this.modules={external:[]},n=this,Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach(e=>{"function"===typeof n[e]&&(n[e]=n[e].bind(n))}),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"===typeof t&&(n=t,t={}),null==t.defaultNS&&t.ns&&(r(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const o=F();this.options={...o,...this.options,...U(t)},this.options.interpolation={...o.interpolation,...this.options.interpolation},void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);const i=e=>e?"function"===typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?_.init(i(this.modules.logger),this.options):_.init(null,this.options),t=this.modules.formatter?this.modules.formatter:z;const n=new T(this.options);this.store=new x(this.options.resources,this.options);const r=this.services;r.logger=_,r.resourceStore=this.store,r.languageUtils=n,r.pluralResolver=new R(n,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix});this.options.interpolation.format&&this.options.interpolation.format!==o.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),!t||this.options.interpolation.format&&this.options.interpolation.format!==o.interpolation.format||(r.formatter=i(t),r.formatter.init&&r.formatter.init(r,this.options),this.options.interpolation.format=r.formatter.format.bind(r.formatter)),r.interpolator=new $(this.options),r.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},r.backendConnector=new M(i(this.modules.backend),r.resourceStore,r,this.options),r.backendConnector.on("*",function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];e.emit(t,...r)}),this.modules.languageDetector&&(r.languageDetector=i(this.modules.languageDetector),r.languageDetector.init&&r.languageDetector.init(r,this.options.detection,this.options)),this.modules.i18nFormat&&(r.i18nFormat=i(this.modules.i18nFormat),r.i18nFormat.init&&r.i18nFormat.init(this)),this.translator=new P(this.services,this.options),this.translator.on("*",function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];e.emit(t,...r)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,n||(n=B),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}});["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});const s=a(),l=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),s.resolve(t),n(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),s}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:B;const n=r(e)?e:this.language;if("function"===typeof e&&(t=e),!this.options.resources||this.options.partialBundledLanguages){if("cimode"===n?.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return t();const e=[],r=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)})};if(n)r(n);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>r(e))}this.options.preload?.forEach?.(e=>r(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),t(e)})}else t(null)}reloadResources(e,t,n){const r=a();return"function"===typeof e&&(n=e,e=void 0),"function"===typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=B),this.services.backendConnector.reload(e,t,e=>{r.resolve(),n(e)}),r}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&E.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){const t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const o=a();this.emit("languageChanging",e);const i=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},s=(r,a)=>{a?this.isLanguageChangingTo===e&&(i(a),this.translator.changeLanguage(a),this.isLanguageChangingTo=void 0,this.emit("languageChanged",a),this.logger.log("languageChanged",a)):this.isLanguageChangingTo=void 0,o.resolve(function(){return n.t(...arguments)}),t&&t(r,function(){return n.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);const n=r(t)?t:t&&t[0],a=this.store.hasLanguageSomeTranslations(n)?n:this.services.languageUtils.getBestMatchFromCodes(r(t)?[t]:t);a&&(this.language||i(a),this.translator.language||this.translator.changeLanguage(a),this.services.languageDetector?.cacheUserLanguage?.(a)),this.loadResources(a,e=>{s(e,a)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),o}getFixedT(e,t,n){var a=this;const o=function(e,t){let r;if("object"!==typeof t){for(var i=arguments.length,s=new Array(i>2?i-2:0),l=2;l<i;l++)s[l-2]=arguments[l];r=a.options.overloadTranslationOptionHandler([e,t].concat(s))}else r={...t};r.lng=r.lng||o.lng,r.lngs=r.lngs||o.lngs,r.ns=r.ns||o.ns,""!==r.keyPrefix&&(r.keyPrefix=r.keyPrefix||n||o.keyPrefix);const u=a.options.keySeparator||".";let c;return c=r.keyPrefix&&Array.isArray(e)?e.map(e=>`${r.keyPrefix}${u}${e}`):r.keyPrefix?`${r.keyPrefix}${u}${e}`:e,a.t(c,r)};return r(e)?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=n,o}t(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.translator?.translate(...t)}exists(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.translator?.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,a=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;const o=(e,t)=>{const n=this.services.backendConnector.state[`${e}|${t}`];return-1===n||0===n||2===n};if(t.precheck){const e=t.precheck(this,o);if(void 0!==e)return e}return!!this.hasResourceBundle(n,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!o(n,e)||r&&!o(a,e)))}loadNamespaces(e,t){const n=a();return this.options.ns?(r(e)&&(e=[e]),e.forEach(e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=a();r(e)&&(e=[e]);const o=this.options.preload||[],i=e.filter(e=>o.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e));return i.length?(this.options.preload=o.concat(i),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!e)return"rtl";if(Intl.Locale){const t=new Intl.Locale(e);if(t&&t.getTextInfo){const e=t.getTextInfo();if(e&&e.direction)return e.direction}}const t=this.services?.languageUtils||new T(F());return e.toLowerCase().indexOf("-latn")>1?"ltr":["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new H(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:B;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const r={...this.options,...e,isClone:!0},a=new H(r);void 0===e.debug&&void 0===e.prefix||(a.logger=a.logger.clone(e));if(["store","services","language"].forEach(e=>{a[e]=this[e]}),a.services={...this.services},a.services.utils={hasLoadedNamespace:a.hasLoadedNamespace.bind(a)},n){const e=Object.keys(this.store.data).reduce((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce((n,r)=>(n[r]={...e[t][r]},n),e[t]),e),{});a.store=new x(e,r),a.services.resourceStore=a.store}return a.translator=new P(a.services,r),a.translator.on("*",function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];a.emit(e,...n)}),a.init(r,t),a.translator.options=r,a.translator.backendConnector.services.utils={hasLoadedNamespace:a.hasLoadedNamespace.bind(a)},a}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const W=H.createInstance();W.createInstance=H.createInstance;W.createInstance,W.dir,W.init,W.loadResources,W.reloadResources,W.use,W.changeLanguage,W.getFixedT,W.t,W.exists,W.setDefaultNamespace,W.hasLoadedNamespace,W.loadNamespaces,W.loadLanguages;var q=n(4117);W.use(q.r9).init({resources:{en:{translation:{login:"Login",dashboard:"Dashboard",email_address:"Email Address",my_account:"My Account",my_gains:"My Gains",my_wallet:"My Wallet",products:"Products",orders:"Orders",total_earnings:"Total Earnings",yesterday_earnings:"Yesterday's Earnings",available_balance:"Available Balance",power_pledge:"Power Pledge",earnings_trend:"Earnings Trend (Last 7 Days)",FIL_earnings:"FIL Earnings",USD_estimate:"USD Estimate",wallet_management:"Wallet Management",buy_power:"Buy Power",enter_wallet:"Enter Wallet",browse_products:"Browse Products",product_list:"Product List",shares:"Shares",price:"Price",total_shares:"Total Shares",sold_shares:"Sold Shares",remaining_shares:"Remaining Shares",purchase_min:"Min. Purchase",waiting_period:"Waiting Period",sold_out:"Sold Out",buy_now:"Buy Now",order_id:"Order ID",product_name:"Product Name",storage_cost:"Storage Cost",pledge_cost:"Pledge Cost",total_rate:"Total Rate",start_date:"Start Date",end_date:"End Date",status:"Status",created_at:"Created At",no_orders:"No orders yet.",currency:"Currency",locked_balance:"Locked Balance",total_balance:"Total Balance",withdrawn:"Withdrawn",no_assets:"No assets yet.",overview:"Overview",deposit:"Deposit",withdraw:"Withdraw",exchange:"Exchange",kyc_verification:"KYC Verification",real_name:"Real Name",id_number:"ID Number",id_front:"ID Front",id_back:"ID Back",submit_review:"Submit for Review",pending_review:"Pending Review",approved:"Approved",rejected:"Rejected",my_recommendations:"My Recommendations",my_invite_code:"My Invite Code",copy_code:"Copy Code",my_subordinates:"My Subordinates",user_id:"User ID",email:"Email",registration_time:"Registration Time",no_subordinates:"No subordinates yet.",agent_dashboard:"Agent Dashboard",brand_name:"Brand Name",commission_rate:"Commission Rate",kyc_status:"KYC Status",member_management:"Member Management",product_management:"Product Management",enter_member_list:"Enter Member List",browse_agent_products:"Browse Products",my_subordinate_members:"My Subordinate Members",maker_dashboard:"Maker Dashboard",domain:"Domain",support_email:"Support Email",sms_signature:"SMS Signature",add_product:"Add Product",edit:"Edit",delete:"Delete",disabled:"Disabled",enabled:"Enabled",review_status:"Review Status",all_orders:"All Orders",customer:"Customer",agent:"Agent",manage_your_digital_assets:"Manage Your Digital Assets",view_and_purchase_new_power_products:"View and Purchase New Power Products",failed_to_load_kyc_status:"Failed to load KYC status.",user_not_logged_in:"User not logged in.",kyc_submit_success:"KYC information submitted successfully. Please wait for review.",failed_to_submit_kyc:"Failed to submit KYC",loading_kyc_status:"Loading KYC status...",kyc_approved:"Your KYC has been approved.",kyc_pending_review:"Your KYC is under review, please wait.",kyc_rejected:"Your KYC was rejected, please modify and resubmit.",submitting:"Submitting...",personal_info:"Personal Information",change_login_password:"Change Login Password",change_withdraw_password:"Change Withdraw Password",view_my_recommendations:"View My Recommendations",gain_id:"Gain ID",gain_amount:"Gain Amount",fee:"Fee",progress:"Progress",time:"Time",no_gains_record:"No gain records.",loading_earnings:"Loading earnings...",loading_orders:"Loading orders...",manage_your_products:"Manage Your Products",order_management:"Order Management",enter_order_list:"Enter Order List",loading_wallet:"Loading wallet...",deposit_coming_soon:"Deposit feature coming soon...",withdraw_coming_soon:"Withdraw feature coming soon...",exchange_coming_soon:"Exchange feature coming soon...",loading_members:"Loading members...",products_on_sale:"Products on sale",loading_products:"Loading products...",product_id:"Product ID",category:"Category",maker:"Maker",no_products:"No products on sale.",spot:"Spot",futures:"Futures",loading_agent_dashboard:"Loading agent dashboard...",not_agent:"You are not an agent or agent information not found.",password:"Password",logging_in:"Logging in...",login_failed:"Failed to log in. Please check your credentials.",no_assets:"No assets yet.",loading_referral_data:"Loading referral data...",my_invite_code:"My Invite Code",copy_invite_code:"Copy Invite Code",invite_code_copied:"Invite code copied to clipboard!",share_invite_description:"Share this invite code with your friends. They can use it when registering to become your referrals.",my_subordinate_users:"My Subordinate Users",no_subordinate_users:"No subordinate users yet.",my_products:"My Products",add_new_product:"Add New Product",product_name_header:"Name",price_per_share:"Price (USDT/Share)",no_products_available:"No products available.",spot_category:"Spot",futures_category:"Futures",loading_orders_text:"Loading orders...",no_orders_available:"No orders available.",actions:"Actions",fil:"FIL",usd:"USD",loading_maker_dashboard:"Loading maker dashboard...",not_maker:"You are not a maker or maker information not found.",enter_product_list:"Enter Product List",power_products:"Power Products",provided_by:"Provided by",official:"Official",price_label:"Price",usdt_per_share:"USDT / Share",total_shares_label:"Total Shares",remaining_shares_label:"Remaining Shares",min_purchase_label:"Min. Purchase",shares_unit:"Shares",waiting_period_label:"Waiting Period",days_unit:"Days",language:"Language",loading:"Loading...",initializing_platform:"Initializing Platform...",backend_connection_failed:"Failed to connect to backend. Please check the plugin configuration."}},zh:{translation:{login:"\u767b\u5f55",dashboard:"\u4eea\u8868\u76d8",email_address:"\u90ae\u7bb1\u5730\u5740",my_account:"\u6211\u7684\u8d26\u6237",my_gains:"\u6211\u7684\u6536\u76ca",my_wallet:"\u6211\u7684\u94b1\u5305",products:"\u4ea7\u54c1",orders:"\u8ba2\u5355",total_earnings:"\u603b\u6536\u76ca",yesterday_earnings:"\u6628\u65e5\u6536\u76ca",available_balance:"\u53ef\u7528\u4f59\u989d",power_pledge:"\u7b97\u529b\u8d28\u62bc",earnings_trend:"\u6536\u76ca\u8d70\u52bf (\u8fd17\u65e5)",FIL_earnings:"FIL\u6536\u76ca",USD_estimate:"USD\u4f30\u503c",wallet_management:"\u94b1\u5305\u7ba1\u7406",buy_power:"\u8d2d\u4e70\u7b97\u529b",enter_wallet:"\u8fdb\u5165\u94b1\u5305",browse_products:"\u6d4f\u89c8\u4ea7\u54c1",product_list:"\u4ea7\u54c1\u5217\u8868",shares:"\u4efd\u989d",price:"\u4ef7\u683c",total_shares:"\u603b\u4efd\u989d",sold_shares:"\u5df2\u552e\u4efd\u989d",remaining_shares:"\u5269\u4f59\u4efd\u989d",purchase_min:"\u8d77\u8d2d",waiting_period:"\u7b49\u5f85\u671f",sold_out:"\u5df2\u552e\u7f44",buy_now:"\u7acb\u5373\u8d2d\u4e70",order_id:"\u8ba2\u5355ID",product_name:"\u4ea7\u54c1\u540d\u79f0",storage_cost:"\u5b58\u50a8\u6210\u672c",pledge_cost:"\u8d28\u62bc\u6210\u672c",total_rate:"\u603b\u8d39\u7387",start_date:"\u5f00\u59cb\u65e5\u671f",end_date:"\u7ed3\u675f\u65e5\u671f",status:"\u72b6\u6001",created_at:"\u521b\u5efa\u65f6\u95f4",no_orders:"\u6682\u65e0\u8ba2\u5355",currency:"\u5e01\u79cd",locked_balance:"\u9501\u5b9a\u4f59\u989d",total_balance:"\u603b\u4f59\u989d",withdrawn:"\u5df2\u63d0\u73b0",no_assets:"\u6682\u65e0\u8d44\u4ea7",overview:"\u8d44\u4ea7\u6982\u89c8",deposit:"\u5145\u503c",withdraw:"\u63d0\u73b0",exchange:"\u5151\u6362",kyc_verification:"KYC \u8ba4\u8bc1",real_name:"\u771f\u5b9e\u59d3\u540d",id_number:"\u8eab\u4efd\u8bc1\u53f7\u7801",id_front:"\u8eab\u4efd\u8bc1\u6b63\u9762",id_back:"\u8eab\u4efd\u8bc1\u53cd\u9762",submit_review:"\u63d0\u4ea4\u5ba1\u6838",pending_review:"\u5ba1\u6838\u4e2d",approved:"\u5df2\u901a\u8fc7",rejected:"\u672a\u901a\u8fc7",my_recommendations:"\u6211\u7684\u63a8\u8350",my_invite_code:"\u6211\u7684\u9080\u8bf7\u7801",copy_code:"\u590d\u5236\u9080\u8bf7\u7801",my_subordinates:"\u6211\u7684\u4e0b\u7ea7\u6210\u5458",user_id:"\u7528\u6237ID",email:"\u90ae\u7bb1",registration_time:"\u6ce8\u518c\u65f6\u95f4",no_subordinates:"\u6682\u65e0\u4e0b\u7ea7\u7528\u6237",agent_dashboard:"\u4ee3\u7406\u5546\u540e\u53f0",brand_name:"\u54c1\u724c\u540d\u79f0",commission_rate:"\u4f63\u91d1\u6bd4\u4f8b",kyc_status:"KYC \u72b6\u6001",member_management:"\u6210\u5458\u7ba1\u7406",product_management:"\u4ea7\u54c1\u7ba1\u7406",enter_member_list:"\u8fdb\u5165\u6210\u5458\u5217\u8868",browse_agent_products:"\u6d4f\u89c8\u4ea7\u54c1",my_subordinate_members:"\u6211\u7684\u4e0b\u7ea7\u6210\u5458",maker_dashboard:"\u5236\u9020\u5546\u540e\u53f0",domain:"\u54c1\u724c\u57df\u540d",support_email:"\u652f\u6301\u90ae\u7bb1",sms_signature:"\u77ed\u4fe1\u7b7e\u540d",add_product:"\u65b0\u589e\u4ea7\u54c1",edit:"\u7f16\u8f91",delete:"\u5220\u9664",disabled:"\u5df2\u7981\u7528",enabled:"\u542f\u7528\u4e2d",review_status:"\u5ba1\u6838\u72b6\u6001",all_orders:"\u6240\u6709\u8ba2\u5355",customer:"\u5ba2\u6237",agent:"\u4ee3\u7406\u5546",manage_your_digital_assets:"\u7ba1\u7406\u60a8\u7684\u6570\u5b57\u8d44\u4ea7",view_and_purchase_new_power_products:"\u67e5\u770b\u5e76\u8d2d\u4e70\u65b0\u7684\u7b97\u529b\u4ea7\u54c1",failed_to_load_kyc_status:"\u52a0\u8f7dKYC\u72b6\u6001\u5931\u8d25\u3002",user_not_logged_in:"\u7528\u6237\u672a\u767b\u5f55\u3002",kyc_submit_success:"KYC\u4fe1\u606f\u63d0\u4ea4\u6210\u529f\uff0c\u8bf7\u7b49\u5f85\u5ba1\u6838\u3002",failed_to_submit_kyc:"\u63d0\u4ea4KYC\u5931\u8d25",loading_kyc_status:"\u6b63\u5728\u52a0\u8f7dKYC\u72b6\u6001...",kyc_approved:"\u60a8\u7684KYC\u5df2\u901a\u8fc7\u5ba1\u6838\u3002",kyc_pending_review:"\u60a8\u7684KYC\u6b63\u5728\u5ba1\u6838\u4e2d\uff0c\u8bf7\u8010\u5fc3\u7b49\u5f85\u3002",kyc_rejected:"\u60a8\u7684KYC\u5ba1\u6838\u672a\u901a\u8fc7\uff0c\u8bf7\u4fee\u6539\u540e\u91cd\u65b0\u63d0\u4ea4\u3002",submitting:"\u63d0\u4ea4\u4e2d...",personal_info:"\u4e2a\u4eba\u4fe1\u606f",change_login_password:"\u4fee\u6539\u767b\u5f55\u5bc6\u7801",change_withdraw_password:"\u4fee\u6539\u63d0\u73b0\u5bc6\u7801",view_my_recommendations:"\u67e5\u770b\u6211\u7684\u63a8\u8350",gain_id:"\u6536\u76caID",gain_amount:"\u6536\u76ca\u91d1\u989d",fee:"\u624b\u7eed\u8d39",progress:"\u8fdb\u5ea6",time:"\u65f6\u95f4",no_gains_record:"\u6682\u65e0\u6536\u76ca\u8bb0\u5f55",loading_earnings:"\u6b63\u5728\u52a0\u8f7d\u6536\u76ca...",loading_orders:"\u6b63\u5728\u52a0\u8f7d\u8ba2\u5355...",manage_your_products:"\u7ba1\u7406\u60a8\u7684\u4ea7\u54c1\u5217\u8868",order_management:"\u8ba2\u5355\u7ba1\u7406",enter_order_list:"\u8fdb\u5165\u8ba2\u5355\u5217\u8868",loading_wallet:"\u6b63\u5728\u52a0\u8f7d\u94b1\u5305...",deposit_coming_soon:"\u5145\u503c\u529f\u80fd\u5373\u5c06\u4e0a\u7ebf...",withdraw_coming_soon:"\u63d0\u73b0\u529f\u80fd\u5373\u5c06\u4e0a\u7ebf...",exchange_coming_soon:"\u5151\u6362\u529f\u80fd\u5373\u5c06\u4e0a\u7ebf...",loading_members:"\u6b63\u5728\u52a0\u8f7d\u6210\u5458...",products_on_sale:"\u5728\u552e\u4ea7\u54c1",loading_products:"\u6b63\u5728\u52a0\u8f7d\u4ea7\u54c1...",product_id:"\u4ea7\u54c1ID",category:"\u7c7b\u522b",maker:"\u5236\u9020\u5546",no_products:"\u6682\u65e0\u5728\u552e\u4ea7\u54c1",spot:"\u73b0\u8d27",futures:"\u671f\u8d27",loading_agent_dashboard:"\u6b63\u5728\u52a0\u8f7d\u4ee3\u7406\u5546\u540e\u53f0...",not_agent:"\u60a8\u4e0d\u662f\u4ee3\u7406\u5546\u6216\u4ee3\u7406\u5546\u4fe1\u606f\u672a\u627e\u5230\u3002",password:"\u5bc6\u7801",logging_in:"\u767b\u5f55\u4e2d...",login_failed:"\u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u60a8\u7684\u51ed\u636e\u3002",no_assets:"\u6682\u65e0\u8d44\u4ea7",loading_referral_data:"\u6b63\u5728\u52a0\u8f7d\u63a8\u8350\u6570\u636e...",my_invite_code:"\u6211\u7684\u9080\u8bf7\u7801",copy_invite_code:"\u590d\u5236\u9080\u8bf7\u7801",invite_code_copied:"\u9080\u8bf7\u7801\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f\uff01",share_invite_description:"\u5206\u4eab\u6b64\u9080\u8bf7\u7801\u7ed9\u60a8\u7684\u670b\u53cb\uff0c\u4ed6\u4eec\u6ce8\u518c\u65f6\u4f7f\u7528\u5373\u53ef\u6210\u4e3a\u60a8\u7684\u4e0b\u7ea7\u3002",my_subordinate_users:"\u6211\u7684\u4e0b\u7ea7\u7528\u6237",no_subordinate_users:"\u6682\u65e0\u4e0b\u7ea7\u7528\u6237",my_products:"\u6211\u7684\u4ea7\u54c1",add_new_product:"\u65b0\u589e\u4ea7\u54c1",product_name_header:"\u540d\u79f0",price_per_share:"\u4ef7\u683c (USDT/\u4efd)",no_products_available:"\u6682\u65e0\u4ea7\u54c1",spot_category:"\u73b0\u8d27",futures_category:"\u671f\u8d27",loading_orders_text:"\u6b63\u5728\u52a0\u8f7d\u8ba2\u5355...",no_orders_available:"\u6682\u65e0\u8ba2\u5355",actions:"\u64cd\u4f5c",fil:"FIL",usd:"USD",loading_maker_dashboard:"\u6b63\u5728\u52a0\u8f7d\u5236\u9020\u5546\u540e\u53f0...",not_maker:"\u60a8\u4e0d\u662f\u5236\u9020\u5546\u6216\u5236\u9020\u5546\u4fe1\u606f\u672a\u627e\u5230\u3002",enter_product_list:"\u8fdb\u5165\u4ea7\u54c1\u5217\u8868",power_products:"\u7b97\u529b\u4ea7\u54c1",provided_by:"\u7531",official:"\u5b98\u65b9",price_label:"\u4ef7\u683c",usdt_per_share:"USDT / \u4efd",total_shares_label:"\u603b\u4efd\u989d",remaining_shares_label:"\u5269\u4f59\u4efd\u989d",min_purchase_label:"\u8d77\u8d2d",shares_unit:"\u4efd",waiting_period_label:"\u7b49\u5f85\u671f",days_unit:"\u5929",language:"\u8bed\u8a00",loading:"\u52a0\u8f7d\u4e2d...",initializing_platform:"\u6b63\u5728\u521d\u59cb\u5316\u5e73\u53f0...",backend_connection_failed:"\u8fde\u63a5\u540e\u7aef\u5931\u8d25\u3002\u8bf7\u68c0\u67e5\u63d2\u4ef6\u914d\u7f6e\u3002"}},ja:{translation:{login:"\u30ed\u30b0\u30a4\u30f3",dashboard:"\u30c0\u30c3\u30b7\u30e5\u30dc\u30fc\u30c9",email_address:"\u30e1\u30fc\u30eb\u30a2\u30c9\u30ec\u30b9",my_account:"\u30de\u30a4\u30a2\u30ab\u30a6\u30f3\u30c8",my_gains:"\u79c1\u306e\u53ce\u76ca",my_wallet:"\u79c1\u306e\u30a6\u30a9\u30ec\u30c3\u30c8",products:"\u88fd\u54c1",orders:"\u6ce8\u6587",total_earnings:"\u7dcf\u53ce\u76ca",yesterday_earnings:"\u6628\u65e5\u306e\u53ce\u76ca",available_balance:"\u5229\u7528\u53ef\u80fd\u6b8b\u9ad8",power_pledge:"\u30d1\u30ef\u30fc\u30d7\u30ec\u30c3\u30b8",earnings_trend:"\u53ce\u76ca\u30c8\u30ec\u30f3\u30c9 (\u904e\u53bb7\u65e5\u9593)",FIL_earnings:"FIL\u53ce\u76ca",USD_estimate:"USD\u898b\u7a4d\u3082\u308a",wallet_management:"\u30a6\u30a9\u30ec\u30c3\u30c8\u7ba1\u7406",buy_power:"\u30d1\u30ef\u30fc\u3092\u8cfc\u5165",enter_wallet:"\u30a6\u30a9\u30ec\u30c3\u30c8\u306b\u5165\u308b",browse_products:"\u88fd\u54c1\u3092\u95b2\u89a7",product_list:"\u88fd\u54c1\u30ea\u30b9\u30c8",shares:"\u30b7\u30a7\u30a2",price:"\u4fa1\u683c",total_shares:"\u7dcf\u30b7\u30a7\u30a2",sold_shares:"\u8ca9\u58f2\u6e08\u307f\u30b7\u30a7\u30a2",remaining_shares:"\u6b8b\u308a\u30b7\u30a7\u30a2",purchase_min:"\u6700\u4f4e\u8cfc\u5165",waiting_period:"\u5f85\u6a5f\u671f\u9593",sold_out:"\u58f2\u308a\u5207\u308c",buy_now:"\u4eca\u3059\u3050\u8cfc\u5165",order_id:"\u6ce8\u6587ID",product_name:"\u88fd\u54c1\u540d",storage_cost:"\u30b9\u30c8\u30ec\u30fc\u30b8\u30b3\u30b9\u30c8",pledge_cost:"\u30d7\u30ec\u30c3\u30b8\u30b3\u30b9\u30c8",total_rate:"\u7dcf\u30ec\u30fc\u30c8",start_date:"\u958b\u59cb\u65e5",end_date:"\u7d42\u4e86\u65e5",status:"\u30b9\u30c6\u30fc\u30bf\u30b9",created_at:"\u4f5c\u6210\u65e5\u6642",no_orders:"\u307e\u3060\u6ce8\u6587\u306f\u3042\u308a\u307e\u305b\u3093\u3002",currency:"\u901a\u8ca8",locked_balance:"\u30ed\u30c3\u30af\u3055\u308c\u305f\u6b8b\u9ad8",total_balance:"\u5408\u8a08\u6b8b\u9ad8",withdrawn:"\u5f15\u304d\u51fa\u3057\u6e08\u307f",no_assets:"\u307e\u3060\u8cc7\u7523\u306f\u3042\u308a\u307e\u305b\u3093\u3002",overview:"\u6982\u8981",deposit:"\u5165\u91d1",withdraw:"\u51fa\u91d1",exchange:"\u4ea4\u63db",kyc_verification:"KYC\u8a8d\u8a3c",real_name:"\u672c\u540d",id_number:"\u8eab\u5206\u8a3c\u660e\u66f8\u756a\u53f7",id_front:"\u8eab\u5206\u8a3c\u660e\u66f8\u8868\u9762",id_back:"\u8eab\u5206\u8a3c\u660e\u66f8\u88cf\u9762",submit_review:"\u5be9\u67fb\u3092\u63d0\u51fa",pending_review:"\u5be9\u67fb\u4e2d",approved:"\u627f\u8a8d\u6e08\u307f",rejected:"\u5374\u4e0b\u6e08\u307f",my_recommendations:"\u79c1\u306e\u7d39\u4ecb",my_invite_code:"\u79c1\u306e\u62db\u5f85\u30b3\u30fc\u30c9",copy_code:"\u30b3\u30fc\u30c9\u3092\u30b3\u30d4\u30fc",my_subordinates:"\u79c1\u306e\u30e1\u30f3\u30d0\u30fc",user_id:"\u30e6\u30fc\u30b6\u30fcID",email:"\u30e1\u30fc\u30eb",registration_time:"\u767b\u9332\u6642\u9593",no_subordinates:"\u307e\u3060\u90e8\u4e0b\u30e6\u30fc\u30b6\u30fc\u306f\u3044\u307e\u305b\u3093\u3002",agent_dashboard:"\u30a8\u30fc\u30b8\u30a7\u30f3\u30c8\u30c0\u30c3\u30b7\u30e5\u30dc\u30fc\u30c9",brand_name:"\u30d6\u30e9\u30f3\u30c9\u540d",commission_rate:"\u30b3\u30df\u30c3\u30b7\u30e7\u30f3\u7387",kyc_status:"KYC\u30b9\u30c6\u30fc\u30bf\u30b9",member_management:"\u30e1\u30f3\u30d0\u30fc\u7ba1\u7406",product_management:"\u88fd\u54c1\u7ba1\u7406",enter_member_list:"\u30e1\u30f3\u30d0\u30fc\u30ea\u30b9\u30c8\u306b\u5165\u308b",browse_agent_products:"\u88fd\u54c1\u3092\u95b2\u89a7",my_subordinate_members:"\u79c1\u306e\u90e8\u4e0b\u30e1\u30f3\u30d0\u30fc",maker_dashboard:"\u30e1\u30fc\u30ab\u30fc\u30c0\u30c3\u30b7\u30e5\u30dc\u30fc\u30c9",domain:"\u30d6\u30e9\u30f3\u30c9\u30c9\u30e1\u30a4\u30f3",support_email:"\u30b5\u30dd\u30fc\u30c8\u30e1\u30fc\u30eb",sms_signature:"SMS\u7f72\u540d",add_product:"\u88fd\u54c1\u3092\u8ffd\u52a0",edit:"\u7de8\u96c6",delete:"\u524a\u9664",disabled:"\u7121\u52b9",enabled:"\u6709\u52b9",review_status:"\u5be9\u67fb\u30b9\u30c6\u30fc\u30bf\u30b9",all_orders:"\u3059\u3079\u3066\u306e\u6ce8\u6587",customer:"\u9867\u5ba2",agent:"\u30a8\u30fc\u30b8\u30a7\u30f3\u30c8",manage_your_digital_assets:"\u30c7\u30b8\u30bf\u30eb\u8cc7\u7523\u3092\u7ba1\u7406\u3059\u308b",view_and_purchase_new_power_products:"\u65b0\u3057\u3044\u30d1\u30ef\u30fc\u30d7\u30ed\u30c0\u30af\u30c8\u3092\u8868\u793a\u3057\u3066\u8cfc\u5165\u3059\u308b",failed_to_load_kyc_status:"KYC\u30b9\u30c6\u30fc\u30bf\u30b9\u306e\u8aad\u307f\u8fbc\u307f\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002",user_not_logged_in:"\u30e6\u30fc\u30b6\u30fc\u304c\u30ed\u30b0\u30a4\u30f3\u3057\u3066\u3044\u307e\u305b\u3093\u3002",kyc_submit_success:"KYC\u60c5\u5831\u304c\u6b63\u5e38\u306b\u9001\u4fe1\u3055\u308c\u307e\u3057\u305f\u3002\u5be9\u67fb\u3092\u304a\u5f85\u3061\u304f\u3060\u3055\u3044\u3002",failed_to_submit_kyc:"KYC\u306e\u9001\u4fe1\u306b\u5931\u6557\u3057\u307e\u3057\u305f",loading_kyc_status:"KYC\u30b9\u30c6\u30fc\u30bf\u30b9\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",kyc_approved:"\u3042\u306a\u305f\u306eKYC\u306f\u627f\u8a8d\u3055\u308c\u307e\u3057\u305f\u3002",kyc_pending_review:"\u3042\u306a\u305f\u306eKYC\u306f\u5be9\u67fb\u4e2d\u3067\u3059\u3002\u304a\u5f85\u3061\u304f\u3060\u3055\u3044\u3002",kyc_rejected:"\u3042\u306a\u305f\u306eKYC\u306f\u5374\u4e0b\u3055\u308c\u307e\u3057\u305f\u3002\u4fee\u6b63\u3057\u3066\u518d\u63d0\u51fa\u3057\u3066\u304f\u3060\u3055\u3044\u3002",submitting:"\u9001\u4fe1\u4e2d...",personal_info:"\u500b\u4eba\u60c5\u5831",change_login_password:"\u30ed\u30b0\u30a4\u30f3\u30d1\u30b9\u30ef\u30fc\u30c9\u3092\u5909\u66f4",change_withdraw_password:"\u51fa\u91d1\u30d1\u30b9\u30ef\u30fc\u30c9\u3092\u5909\u66f4",view_my_recommendations:"\u81ea\u5206\u306e\u63a8\u85a6\u3092\u898b\u308b",gain_id:"\u53ce\u76caID",gain_amount:"\u53ce\u76ca\u984d",fee:"\u624b\u6570\u6599",progress:"\u9032\u6357",time:"\u6642\u9593",no_gains_record:"\u53ce\u76ca\u8a18\u9332\u306f\u3042\u308a\u307e\u305b\u3093\u3002",loading_earnings:"\u53ce\u76ca\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",loading_orders:"\u6ce8\u6587\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",manage_your_products:"\u3042\u306a\u305f\u306e\u88fd\u54c1\u3092\u7ba1\u7406\u3059\u308b",order_management:"\u6ce8\u6587\u7ba1\u7406",enter_order_list:"\u6ce8\u6587\u30ea\u30b9\u30c8\u306b\u5165\u308b",loading_wallet:"\u30a6\u30a9\u30ec\u30c3\u30c8\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",deposit_coming_soon:"\u5165\u91d1\u6a5f\u80fd\u306f\u8fd1\u65e5\u516c\u958b\u3055\u308c\u307e\u3059\u3002",withdraw_coming_soon:"\u51fa\u91d1\u6a5f\u80fd\u306f\u8fd1\u65e5\u516c\u958b\u3055\u308c\u307e\u3059\u3002",exchange_coming_soon:"\u4ea4\u63db\u6a5f\u80fd\u306f\u8fd1\u65e5\u516c\u958b\u3055\u308c\u307e\u3059\u3002",loading_members:"\u30e1\u30f3\u30d0\u30fc\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",products_on_sale:"\u8ca9\u58f2\u4e2d\u306e\u88fd\u54c1",loading_products:"\u88fd\u54c1\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",product_id:"\u88fd\u54c1ID",category:"\u30ab\u30c6\u30b4\u30ea",maker:"\u30e1\u30fc\u30ab\u30fc",no_products:"\u8ca9\u58f2\u4e2d\u306e\u88fd\u54c1\u306f\u3042\u308a\u307e\u305b\u3093\u3002",spot:"\u30b9\u30dd\u30c3\u30c8",futures:"\u5148\u7269",loading_agent_dashboard:"\u30a8\u30fc\u30b8\u30a7\u30f3\u30c8\u30c0\u30c3\u30b7\u30e5\u30dc\u30fc\u30c9\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",not_agent:"\u3042\u306a\u305f\u306f\u30a8\u30fc\u30b8\u30a7\u30f3\u30c8\u3067\u306f\u3042\u308a\u307e\u305b\u3093\u304b\u3001\u30a8\u30fc\u30b8\u30a7\u30f3\u30c8\u60c5\u5831\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093\u3067\u3057\u305f\u3002",password:"\u30d1\u30b9\u30ef\u30fc\u30c9",logging_in:"\u30ed\u30b0\u30a4\u30f3\u4e2d...",login_failed:"\u30ed\u30b0\u30a4\u30f3\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002\u8a8d\u8a3c\u60c5\u5831\u3092\u78ba\u8a8d\u3057\u3066\u304f\u3060\u3055\u3044\u3002",no_assets:"\u307e\u3060\u8cc7\u7523\u306f\u3042\u308a\u307e\u305b\u3093\u3002",loading_referral_data:"\u7d39\u4ecb\u30c7\u30fc\u30bf\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",my_invite_code:"\u79c1\u306e\u62db\u5f85\u30b3\u30fc\u30c9",copy_invite_code:"\u62db\u5f85\u30b3\u30fc\u30c9\u3092\u30b3\u30d4\u30fc",invite_code_copied:"\u62db\u5f85\u30b3\u30fc\u30c9\u304c\u30af\u30ea\u30c3\u30d7\u30dc\u30fc\u30c9\u306b\u30b3\u30d4\u30fc\u3055\u308c\u307e\u3057\u305f\uff01",share_invite_description:"\u3053\u306e\u62db\u5f85\u30b3\u30fc\u30c9\u3092\u53cb\u9054\u3068\u5171\u6709\u3057\u3066\u304f\u3060\u3055\u3044\u3002\u767b\u9332\u6642\u306b\u4f7f\u7528\u3059\u308b\u3068\u3001\u3042\u306a\u305f\u306e\u7d39\u4ecb\u8005\u306b\u306a\u308a\u307e\u3059\u3002",my_subordinate_users:"\u79c1\u306e\u90e8\u4e0b\u30e6\u30fc\u30b6\u30fc",no_subordinate_users:"\u307e\u3060\u90e8\u4e0b\u30e6\u30fc\u30b6\u30fc\u306f\u3044\u307e\u305b\u3093\u3002",my_products:"\u79c1\u306e\u88fd\u54c1",add_new_product:"\u65b0\u3057\u3044\u88fd\u54c1\u3092\u8ffd\u52a0",product_name_header:"\u540d\u524d",price_per_share:"\u4fa1\u683c (USDT/\u30b7\u30a7\u30a2)",no_products_available:"\u5229\u7528\u53ef\u80fd\u306a\u88fd\u54c1\u306f\u3042\u308a\u307e\u305b\u3093\u3002",spot_category:"\u30b9\u30dd\u30c3\u30c8",futures_category:"\u5148\u7269",loading_orders_text:"\u6ce8\u6587\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",no_orders_available:"\u5229\u7528\u53ef\u80fd\u306a\u6ce8\u6587\u306f\u3042\u308a\u307e\u305b\u3093\u3002",actions:"\u30a2\u30af\u30b7\u30e7\u30f3",fil:"FIL",usd:"USD",loading_maker_dashboard:"\u30e1\u30fc\u30ab\u30fc\u30c0\u30c3\u30b7\u30e5\u30dc\u30fc\u30c9\u3092\u8aad\u307f\u8fbc\u307f\u4e2d...",not_maker:"\u3042\u306a\u305f\u306f\u30e1\u30fc\u30ab\u30fc\u3067\u306f\u3042\u308a\u307e\u305b\u3093\u304b\u3001\u30e1\u30fc\u30ab\u30fc\u60c5\u5831\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093\u3067\u3057\u305f\u3002",enter_product_list:"\u88fd\u54c1\u30ea\u30b9\u30c8\u306b\u5165\u308b",power_products:"\u30d1\u30ef\u30fc\u30d7\u30ed\u30c0\u30af\u30c8",provided_by:"\u63d0\u4f9b\u8005",official:"\u516c\u5f0f",price_label:"\u4fa1\u683c",usdt_per_share:"USDT / \u30b7\u30a7\u30a2",total_shares_label:"\u7dcf\u30b7\u30a7\u30a2",remaining_shares_label:"\u6b8b\u308a\u30b7\u30a7\u30a2",min_purchase_label:"\u6700\u4f4e\u8cfc\u5165",shares_unit:"\u30b7\u30a7\u30a2",waiting_period_label:"\u5f85\u6a5f\u671f\u9593",days_unit:"\u65e5",language:"\u8a00\u8a9e",loading:"\u8aad\u307f\u8fbc\u307f\u4e2d...",initializing_platform:"\u30d7\u30e9\u30c3\u30c8\u30d5\u30a9\u30fc\u30e0\u3092\u521d\u671f\u5316\u4e2d...",backend_connection_failed:"\u30d0\u30c3\u30af\u30a8\u30f3\u30c9\u3078\u306e\u63a5\u7d9a\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002\u30d7\u30e9\u30b0\u30a4\u30f3\u306e\u8a2d\u5b9a\u3092\u78ba\u8a8d\u3057\u3066\u304f\u3060\u3055\u3044\u3002"}}},lng:"en",fallbackLng:"en",interpolation:{escapeValue:!1}});var V=n(4312),K=n(1283),J=n(8139),Y=n.n(J),G=n(5901),Q=n(1969),X=n(7852),Z=n(579);const ee=e.forwardRef((e,t)=>{let{bsPrefix:n,className:r,as:a,...o}=e;n=(0,X.oU)(n,"navbar-brand");const i=a||(o.href?"a":"span");return(0,Z.jsx)(i,{...o,ref:t,className:Y()(r,n)})});ee.displayName="NavbarBrand";const te=ee;var ne=n(8747),re=n(8062),ae=n(9791),oe=n(9841);const ie=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(e=>null!=e).reduce((e,t)=>{if("function"!==typeof t)throw new Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(){for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];e.apply(this,r),t.apply(this,r)}},null)};var se=n(2643),le=n(865);const ue={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function ce(e,t){const n=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],r=ue[e];return n+parseInt((0,ne.A)(t,r[0]),10)+parseInt((0,ne.A)(t,r[1]),10)}const de={[re.kp]:"collapse",[re.ze]:"collapsing",[re.ns]:"collapsing",[re._K]:"collapse show"},fe=e.forwardRef((t,n)=>{let{onEnter:r,onEntering:a,onEntered:o,onExit:i,onExiting:s,className:l,children:u,dimension:c="height",in:d=!1,timeout:f=300,mountOnEnter:h=!1,unmountOnExit:p=!1,appear:g=!1,getDimensionValue:m=ce,...v}=t;const y="function"===typeof c?c():c,b=(0,e.useMemo)(()=>ie(e=>{e.style[y]="0"},r),[y,r]),w=(0,e.useMemo)(()=>ie(e=>{const t=`scroll${y[0].toUpperCase()}${y.slice(1)}`;e.style[y]=`${e[t]}px`},a),[y,a]),k=(0,e.useMemo)(()=>ie(e=>{e.style[y]=null},o),[y,o]),_=(0,e.useMemo)(()=>ie(e=>{e.style[y]=`${m(y,e)}px`,(0,se.A)(e)},i),[i,m,y]),S=(0,e.useMemo)(()=>ie(e=>{e.style[y]=null},s),[y,s]);return(0,Z.jsx)(le.A,{ref:n,addEndListener:oe.A,...v,"aria-expanded":v.role?d:null,onEnter:b,onEntering:w,onEntered:k,onExit:_,onExiting:S,childRef:(0,ae.am)(u),in:d,timeout:f,mountOnEnter:h,unmountOnExit:p,appear:g,children:(t,n)=>e.cloneElement(u,{...n,className:Y()(l,u.props.className,de[t],"width"===y&&"collapse-horizontal")})})});fe.displayName="Collapse";const he=fe;var pe=n(9125);const ge=e.forwardRef((t,n)=>{let{children:r,bsPrefix:a,...o}=t;a=(0,X.oU)(a,"navbar-collapse");const i=(0,e.useContext)(pe.A);return(0,Z.jsx)(he,{in:!(!i||!i.expanded),...o,children:(0,Z.jsx)("div",{ref:n,className:a,children:r})})});ge.displayName="NavbarCollapse";const me=ge;var ve=n(6618);const ye=e.forwardRef((t,n)=>{let{bsPrefix:r,className:a,children:o,label:i="Toggle navigation",as:s="button",onClick:l,...u}=t;r=(0,X.oU)(r,"navbar-toggler");const{onToggle:c,expanded:d}=(0,e.useContext)(pe.A)||{},f=(0,ve.A)(e=>{l&&l(e),c&&c()});return"button"===s&&(u.type="button"),(0,Z.jsx)(s,{...u,ref:n,onClick:f,"aria-label":i,className:Y()(a,r,!d&&"collapsed"),children:o||(0,Z.jsx)("span",{className:`${r}-icon`})})});ye.displayName="NavbarToggle";const be=ye,we="undefined"!==typeof n.g&&n.g.navigator&&"ReactNative"===n.g.navigator.product,ke="undefined"!==typeof document||we?e.useLayoutEffect:e.useEffect,_e=new WeakMap,Se=(e,t)=>{if(!e||!t)return;const n=_e.get(t)||new Map;_e.set(t,n);let r=n.get(e);return r||(r=t.matchMedia(e),r.refCount=0,n.set(r.media,r)),r};function xe(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"===typeof window?void 0:window;const r=Se(t,n),[a,o]=(0,e.useState)(()=>!!r&&r.matches);return ke(()=>{let e=Se(t,n);if(!e)return o(!1);let r=_e.get(n);const a=()=>{o(e.matches)};return e.refCount++,e.addListener(a),a(),()=>{e.removeListener(a),e.refCount--,e.refCount<=0&&(null==r||r.delete(e.media)),e=void 0}},[t]),a}const Ee=function(t){const n=Object.keys(t);function r(e,t){return e===t?t:e?`${e} and ${t}`:t}function a(e){const r=function(e){return n[Math.min(n.indexOf(e)+1,n.length-1)]}(e);let a=t[r];return a="number"===typeof a?a-.2+"px":`calc(${a} - 0.2px)`,`(max-width: ${a})`}return function(n,o,i){let s;return"object"===typeof n?(s=n,i=o,o=!0):(o=o||!0,s={[n]:o}),xe((0,e.useMemo)(()=>Object.entries(s).reduce((e,n)=>{let[o,i]=n;return"up"!==i&&!0!==i||(e=r(e,function(e){let n=t[e];return"number"===typeof n&&(n=`${n}px`),`(min-width: ${n})`}(o))),"down"!==i&&!0!==i||(e=r(e,a(o))),e},""),[JSON.stringify(s)]),i)}}({xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400});var Ce=n(182);function Oe(e){void 0===e&&(e=(0,Ce.A)());try{var t=e.activeElement;return t&&t.nodeName?t:null}catch(n){return e.body}}function Pe(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}var Te=n(8279),Ae=n(1094),je=n(7950),Re=n(2665);function Ne(t){const n=function(t){const n=(0,e.useRef)(t);return n.current=t,n}(t);(0,e.useEffect)(()=>()=>n.current(),[])}var Le=n(4696),$e=n(8894),De=n(5425);const Ie=(0,De.sE)("modal-open");const ze=class{constructor(){let{ownerDocument:e,handleContainerOverflow:t=!0,isRTL:n=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.handleContainerOverflow=t,this.isRTL=n,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;const t=e.defaultView;return Math.abs(t.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){const t={overflow:"hidden"},n=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[n]:r.style[n]},e.scrollBarWidth&&(t[n]=`${parseInt((0,ne.A)(r,n)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(Ie,""),(0,ne.A)(r,t)}reset(){[...this.modals].forEach(e=>this.remove(e))}removeContainerStyle(e){const t=this.getElement();t.removeAttribute(Ie),Object.assign(t.style,e.style)}add(e){let t=this.modals.indexOf(e);return-1!==t?t:(t=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==t||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state)),t)}remove(e){const t=this.modals.indexOf(e);-1!==t&&(this.modals.splice(t,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}},Me=(0,e.createContext)(Te.A?window:void 0);Me.Provider;function Fe(){return(0,e.useContext)(Me)}const Ue=(e,t)=>Te.A?null==e?(t||(0,Ce.A)()).body:("function"===typeof e&&(e=e()),e&&"current"in e&&(e=e.current),e&&("nodeType"in e||e.getBoundingClientRect)?e:null):null;var Be=n(3539),He=n(2677);const We=function(t){let{children:n,in:r,onExited:a,mountOnEnter:o,unmountOnExit:i}=t;const s=(0,e.useRef)(null),l=(0,e.useRef)(r),u=(0,$e.A)(a);(0,e.useEffect)(()=>{r?l.current=!0:u(s.current)},[r,u]);const c=(0,Be.A)(s,(0,ae.am)(n)),d=(0,e.cloneElement)(n,{ref:c});return r?d:i||!l.current&&o?null:d},qe=["onEnter","onEntering","onEntered","onExit","onExiting","onExited","addEndListener","children"];const Ve=["component"];const Ke=e.forwardRef((t,n)=>{let{component:r}=t,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(t,Ve);const o=function(t){let{onEnter:n,onEntering:r,onEntered:a,onExit:o,onExiting:i,onExited:s,addEndListener:l,children:u}=t,c=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(t,qe);const d=(0,e.useRef)(null),f=(0,Be.A)(d,(0,ae.am)(u)),h=e=>t=>{e&&d.current&&e(d.current,t)},p=(0,e.useCallback)(h(n),[n]),g=(0,e.useCallback)(h(r),[r]),m=(0,e.useCallback)(h(a),[a]),v=(0,e.useCallback)(h(o),[o]),y=(0,e.useCallback)(h(i),[i]),b=(0,e.useCallback)(h(s),[s]),w=(0,e.useCallback)(h(l),[l]);return Object.assign({},c,{nodeRef:d},n&&{onEnter:p},r&&{onEntering:g},a&&{onEntered:m},o&&{onExit:v},i&&{onExiting:y},s&&{onExited:b},l&&{addEndListener:w},{children:"function"===typeof u?(e,t)=>u(e,Object.assign({},t,{ref:f})):(0,e.cloneElement)(u,{ref:f})})}(a);return(0,Z.jsx)(r,Object.assign({ref:n},o))}),Je=Ke;function Ye(t){let{children:n,in:r,onExited:a,onEntered:o,transition:i}=t;const[s,l]=(0,e.useState)(!r);r&&s&&l(!1);const u=function(t){let{in:n,onTransition:r}=t;const a=(0,e.useRef)(null),o=(0,e.useRef)(!0),i=(0,$e.A)(r);return(0,He.A)(()=>{if(!a.current)return;let e=!1;return i({in:n,element:a.current,initial:o.current,isStale:()=>e}),()=>{e=!0}},[n,i]),(0,He.A)(()=>(o.current=!1,()=>{o.current=!0}),[]),a}({in:!!r,onTransition:e=>{Promise.resolve(i(e)).then(()=>{e.isStale()||(e.in?null==o||o(e.element,e.initial):(l(!0),null==a||a(e.element)))},t=>{throw e.in||l(!0),t})}}),c=(0,Be.A)(u,(0,ae.am)(n));return s&&!r?null:(0,e.cloneElement)(n,{ref:c})}function Ge(e,t,n){return e?(0,Z.jsx)(Je,Object.assign({},n,{component:e})):t?(0,Z.jsx)(Ye,Object.assign({},n,{transition:t})):(0,Z.jsx)(We,Object.assign({},n))}const Qe=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let Xe;function Ze(t){const n=Fe(),r=t||function(e){return Xe||(Xe=new ze({ownerDocument:null==e?void 0:e.document})),Xe}(n),a=(0,e.useRef)({dialog:null,backdrop:null});return Object.assign(a.current,{add:()=>r.add(a.current),remove:()=>r.remove(a.current),isTopModal:()=>r.isTopModal(a.current),setDialogRef:(0,e.useCallback)(e=>{a.current.dialog=e},[]),setBackdropRef:(0,e.useCallback)(e=>{a.current.backdrop=e},[])})}const et=(0,e.forwardRef)((t,n)=>{let{show:r=!1,role:a="dialog",className:o,style:i,children:s,backdrop:l=!0,keyboard:u=!0,onBackdropClick:c,onEscapeKeyDown:d,transition:f,runTransition:h,backdropTransition:p,runBackdropTransition:g,autoFocus:m=!0,enforceFocus:v=!0,restoreFocus:y=!0,restoreFocusOptions:b,renderDialog:w,renderBackdrop:k=e=>(0,Z.jsx)("div",Object.assign({},e)),manager:_,container:S,onShow:x,onHide:E=()=>{},onExit:C,onExited:O,onExiting:P,onEnter:T,onEntering:A,onEntered:j}=t,R=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(t,Qe);const N=Fe(),L=function(t,n){const r=Fe(),[a,o]=(0,e.useState)(()=>Ue(t,null==r?void 0:r.document));if(!a){const e=Ue(t);e&&o(e)}return(0,e.useEffect)(()=>{n&&a&&n(a)},[n,a]),(0,e.useEffect)(()=>{const e=Ue(t);e!==a&&o(e)},[t,a]),a}(S),$=Ze(_),D=(0,Re.A)(),I=(0,Le.A)(r),[z,M]=(0,e.useState)(!r),F=(0,e.useRef)(null);(0,e.useImperativeHandle)(n,()=>$,[$]),Te.A&&!I&&r&&(F.current=Oe(null==N?void 0:N.document)),r&&z&&M(!1);const U=(0,$e.A)(()=>{if($.add(),K.current=(0,Ae.A)(document,"keydown",q),V.current=(0,Ae.A)(document,"focus",()=>setTimeout(H),!0),x&&x(),m){var e,t;const n=Oe(null!=(e=null==(t=$.dialog)?void 0:t.ownerDocument)?e:null==N?void 0:N.document);$.dialog&&n&&!Pe($.dialog,n)&&(F.current=n,$.dialog.focus())}}),B=(0,$e.A)(()=>{var e;($.remove(),null==K.current||K.current(),null==V.current||V.current(),y)&&(null==(e=F.current)||null==e.focus||e.focus(b),F.current=null)});(0,e.useEffect)(()=>{r&&L&&U()},[r,L,U]),(0,e.useEffect)(()=>{z&&B()},[z,B]),Ne(()=>{B()});const H=(0,$e.A)(()=>{if(!v||!D()||!$.isTopModal())return;const e=Oe(null==N?void 0:N.document);$.dialog&&e&&!Pe($.dialog,e)&&$.dialog.focus()}),W=(0,$e.A)(e=>{e.target===e.currentTarget&&(null==c||c(e),!0===l&&E())}),q=(0,$e.A)(e=>{u&&(0,ae.v$)(e)&&$.isTopModal()&&(null==d||d(e),e.defaultPrevented||E())}),V=(0,e.useRef)(),K=(0,e.useRef)();if(!L)return null;const J=Object.assign({role:a,ref:$.setDialogRef,"aria-modal":"dialog"===a||void 0},R,{style:i,className:o,tabIndex:-1});let Y=w?w(J):(0,Z.jsx)("div",Object.assign({},J,{children:e.cloneElement(s,{role:"document"})}));Y=Ge(f,h,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!r,onExit:C,onExiting:P,onExited:function(){M(!0),null==O||O(...arguments)},onEnter:T,onEntering:A,onEntered:j,children:Y});let G=null;return l&&(G=k({ref:$.setBackdropRef,onClick:W}),G=Ge(p,g,{in:!!r,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:G})),(0,Z.jsx)(Z.Fragment,{children:je.createPortal((0,Z.jsxs)(Z.Fragment,{children:[G,Y]}),L)})});et.displayName="Modal";const tt=Object.assign(et,{Manager:ze});var nt=n(8072);const rt=e.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:a="div",...o}=e;return r=(0,X.oU)(r,"offcanvas-body"),(0,Z.jsx)(a,{ref:t,className:Y()(n,r),...o})});rt.displayName="OffcanvasBody";const at=rt,ot={[re.ns]:"show",[re._K]:"show"},it=e.forwardRef((t,n)=>{let{bsPrefix:r,className:a,children:o,in:i=!1,mountOnEnter:s=!1,unmountOnExit:l=!1,appear:u=!1,...c}=t;return r=(0,X.oU)(r,"offcanvas"),(0,Z.jsx)(le.A,{ref:n,addEndListener:oe.A,in:i,mountOnEnter:s,unmountOnExit:l,appear:u,...c,childRef:(0,ae.am)(o),children:(t,n)=>e.cloneElement(o,{...n,className:Y()(a,o.props.className,(t===re.ns||t===re.ze)&&`${r}-toggling`,ot[t])})})});it.displayName="OffcanvasToggling";const st=it,lt=e.createContext({onHide(){}});var ut=n(5632);const ct=e.forwardRef((t,n)=>{let{closeLabel:r="Close",closeVariant:a,closeButton:o=!1,onHide:i,children:s,...l}=t;const u=(0,e.useContext)(lt),c=(0,ve.A)(()=>{null==u||u.onHide(),null==i||i()});return(0,Z.jsxs)("div",{ref:n,...l,children:[s,o&&(0,Z.jsx)(ut.A,{"aria-label":r,variant:a,onClick:c})]})});ct.displayName="AbstractModalHeader";const dt=ct,ft=e.forwardRef((e,t)=>{let{bsPrefix:n,className:r,closeLabel:a="Close",closeButton:o=!1,...i}=e;return n=(0,X.oU)(n,"offcanvas-header"),(0,Z.jsx)(dt,{ref:t,...i,className:Y()(r,n),closeLabel:a,closeButton:o})});ft.displayName="OffcanvasHeader";const ht=ft;const pt=(0,n(4488).A)("h5"),gt=e.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:a=pt,...o}=e;return r=(0,X.oU)(r,"offcanvas-title"),(0,Z.jsx)(a,{ref:t,className:Y()(n,r),...o})});gt.displayName="OffcanvasTitle";const mt=gt;var vt=n(3818);function yt(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const bt=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",wt=".sticky-top",kt=".navbar-toggler";class _t extends ze{adjustAndStore(e,t,n){const r=t.style[e];t.dataset[e]=r,(0,ne.A)(t,{[e]:`${parseFloat((0,ne.A)(t,e))+n}px`})}restore(e,t){const n=t.dataset[e];void 0!==n&&(delete t.dataset[e],(0,ne.A)(t,{[e]:n}))}setContainerStyle(e){super.setContainerStyle(e);const t=this.getElement();var n,r;if(r="modal-open",(n=t).classList?n.classList.add(r):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(n,r)||("string"===typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)),!e.scrollBarWidth)return;const a=this.isRTL?"paddingLeft":"paddingRight",o=this.isRTL?"marginLeft":"marginRight";(0,vt.A)(t,bt).forEach(t=>this.adjustAndStore(a,t,e.scrollBarWidth)),(0,vt.A)(t,wt).forEach(t=>this.adjustAndStore(o,t,-e.scrollBarWidth)),(0,vt.A)(t,kt).forEach(t=>this.adjustAndStore(o,t,e.scrollBarWidth))}removeContainerStyle(e){super.removeContainerStyle(e);const t=this.getElement();var n,r;r="modal-open",(n=t).classList?n.classList.remove(r):"string"===typeof n.className?n.className=yt(n.className,r):n.setAttribute("class",yt(n.className&&n.className.baseVal||"",r));const a=this.isRTL?"paddingLeft":"paddingRight",o=this.isRTL?"marginLeft":"marginRight";(0,vt.A)(t,bt).forEach(e=>this.restore(a,e)),(0,vt.A)(t,wt).forEach(e=>this.restore(o,e)),(0,vt.A)(t,kt).forEach(e=>this.restore(o,e))}}let St;const xt=_t;function Et(e){return(0,Z.jsx)(st,{...e})}function Ct(e){return(0,Z.jsx)(nt.A,{...e})}const Ot=e.forwardRef((t,n)=>{let{bsPrefix:r,className:a,children:o,"aria-labelledby":i,placement:s="start",responsive:l,show:u=!1,backdrop:c=!0,keyboard:d=!0,scroll:f=!1,onEscapeKeyDown:h,onShow:p,onHide:g,container:m,autoFocus:v=!0,enforceFocus:y=!0,restoreFocus:b=!0,restoreFocusOptions:w,onEntered:k,onExit:_,onExiting:S,onEnter:x,onEntering:E,onExited:C,backdropClassName:O,manager:P,renderStaticNode:T=!1,...A}=t;const j=(0,e.useRef)();r=(0,X.oU)(r,"offcanvas");const[R,N]=(0,e.useState)(!1),L=(0,ve.A)(g),$=Ee(l||"xs","up");(0,e.useEffect)(()=>{N(l?u&&!$:u)},[u,l,$]);const D=(0,e.useMemo)(()=>({onHide:L}),[L]);const I=(0,e.useCallback)(e=>(0,Z.jsx)("div",{...e,className:Y()(`${r}-backdrop`,O)}),[O,r]),z=e=>(0,Z.jsx)("div",{...e,...A,className:Y()(a,l?`${r}-${l}`:r,`${r}-${s}`),"aria-labelledby":i,children:o});return(0,Z.jsxs)(Z.Fragment,{children:[!R&&(l||T)&&z({}),(0,Z.jsx)(lt.Provider,{value:D,children:(0,Z.jsx)(tt,{show:R,ref:n,backdrop:c,container:m,keyboard:d,autoFocus:v,enforceFocus:y&&!f,restoreFocus:b,restoreFocusOptions:w,onEscapeKeyDown:h,onShow:p,onHide:L,onEnter:function(e){e&&(e.style.visibility="visible");for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];null==x||x(e,...n)},onEntering:E,onEntered:k,onExit:_,onExiting:S,onExited:function(e){e&&(e.style.visibility="");for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];null==C||C(...n)},manager:P||(f?(j.current||(j.current=new xt({handleContainerOverflow:!1})),j.current):function(e){return St||(St=new _t(e)),St}()),transition:Et,backdropTransition:Ct,renderBackdrop:I,renderDialog:z})})]})});Ot.displayName="Offcanvas";const Pt=Object.assign(Ot,{Body:at,Header:ht,Title:mt}),Tt=e.forwardRef((t,n)=>{let{onHide:r,...a}=t;const o=(0,e.useContext)(pe.A),i=(0,ve.A)(()=>{null==o||null==o.onToggle||o.onToggle(),null==r||r()});return(0,Z.jsx)(Pt,{ref:n,show:!(null==o||!o.expanded),...a,renderStaticNode:!0,onHide:i})});Tt.displayName="NavbarOffcanvas";const At=Tt,jt=e.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:a="span",...o}=e;return r=(0,X.oU)(r,"navbar-text"),(0,Z.jsx)(a,{ref:t,className:Y()(n,r),...o})});jt.displayName="NavbarText";const Rt=jt,Nt=e.forwardRef((t,n)=>{const{bsPrefix:r,expand:a=!0,variant:o="light",bg:i,fixed:s,sticky:l,className:u,as:c="nav",expanded:d,onToggle:f,onSelect:h,collapseOnSelect:p=!1,...g}=(0,Q.Zw)(t,{expanded:"onToggle"}),m=(0,X.oU)(r,"navbar"),v=(0,e.useCallback)(function(){null==h||h(...arguments),p&&d&&(null==f||f(!1))},[h,p,d,f]);void 0===g.role&&"nav"!==c&&(g.role="navigation");let y=`${m}-expand`;"string"===typeof a&&(y=`${y}-${a}`);const b=(0,e.useMemo)(()=>({onToggle:()=>null==f?void 0:f(!d),bsPrefix:m,expanded:!!d,expand:a}),[m,d,a,f]);return(0,Z.jsx)(pe.A.Provider,{value:b,children:(0,Z.jsx)(G.A.Provider,{value:v,children:(0,Z.jsx)(c,{ref:n,...g,className:Y()(u,m,a&&y,o&&`${m}-${o}`,i&&`bg-${i}`,l&&`sticky-${l}`,s&&`fixed-${s}`)})})})});Nt.displayName="Navbar";const Lt=Object.assign(Nt,{Brand:te,Collapse:me,Offcanvas:At,Text:Rt,Toggle:be});var $t=n(3519),Dt=n(4737),It=n(3043);function zt(t,n,r){const a=(0,e.useRef)(void 0!==t),[o,i]=(0,e.useState)(n),s=void 0!==t,l=a.current;return a.current=s,!s&&l&&o!==n&&i(n),[s?t:o,(0,e.useCallback)(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const[a,...o]=t;let s=null==r?void 0:r(a,...o);return i(a),s},[r])]}var Mt=n(8843),Ft=n(9753);const Ut=e.createContext(null);var Bt=n(8457),Ht=Object.prototype.hasOwnProperty;function Wt(e,t,n){for(n of e.keys())if(qt(n,t))return n}function qt(e,t){var n,r,a;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&qt(e[r],t[r]););return-1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e){if((a=r)&&"object"===typeof a&&!(a=Wt(t,a)))return!1;if(!t.has(a))return!1}return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e){if((a=r[0])&&"object"===typeof a&&!(a=Wt(t,a)))return!1;if(!qt(r[1],t.get(a)))return!1}return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return-1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return-1===r}if(!n||"object"===typeof e){for(n in r=0,e){if(Ht.call(e,n)&&++r&&!Ht.call(t,n))return!1;if(!(n in t)||!qt(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!==e&&t!==t}const Vt=function(t){const n=(0,Re.A)();return[t[0],(0,e.useCallback)(e=>{if(n())return t[1](e)},[n,t[1]])]};function Kt(e){return e.split("-")[0]}function Jt(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Yt(e){return e instanceof Jt(e).Element||e instanceof Element}function Gt(e){return e instanceof Jt(e).HTMLElement||e instanceof HTMLElement}function Qt(e){return"undefined"!==typeof ShadowRoot&&(e instanceof Jt(e).ShadowRoot||e instanceof ShadowRoot)}var Xt=Math.max,Zt=Math.min,en=Math.round;function tn(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function nn(){return!/^((?!chrome|android).)*safari/i.test(tn())}function rn(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),a=1,o=1;t&&Gt(e)&&(a=e.offsetWidth>0&&en(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&en(r.height)/e.offsetHeight||1);var i=(Yt(e)?Jt(e):window).visualViewport,s=!nn()&&n,l=(r.left+(s&&i?i.offsetLeft:0))/a,u=(r.top+(s&&i?i.offsetTop:0))/o,c=r.width/a,d=r.height/o;return{width:c,height:d,top:u,right:l+c,bottom:u+d,left:l,x:l,y:u}}function an(e){var t=rn(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function on(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Qt(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function sn(e){return e?(e.nodeName||"").toLowerCase():null}function ln(e){return Jt(e).getComputedStyle(e)}function un(e){return["table","td","th"].indexOf(sn(e))>=0}function cn(e){return((Yt(e)?e.ownerDocument:e.document)||window.document).documentElement}function dn(e){return"html"===sn(e)?e:e.assignedSlot||e.parentNode||(Qt(e)?e.host:null)||cn(e)}function fn(e){return Gt(e)&&"fixed"!==ln(e).position?e.offsetParent:null}function hn(e){for(var t=Jt(e),n=fn(e);n&&un(n)&&"static"===ln(n).position;)n=fn(n);return n&&("html"===sn(n)||"body"===sn(n)&&"static"===ln(n).position)?t:n||function(e){var t=/firefox/i.test(tn());if(/Trident/i.test(tn())&&Gt(e)&&"fixed"===ln(e).position)return null;var n=dn(e);for(Qt(n)&&(n=n.host);Gt(n)&&["html","body"].indexOf(sn(n))<0;){var r=ln(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function pn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function gn(e,t,n){return Xt(e,Zt(t,n))}function mn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function vn(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}var yn="top",bn="bottom",wn="right",kn="left",_n="auto",Sn=[yn,bn,wn,kn],xn="start",En="end",Cn="viewport",On="popper",Pn=Sn.reduce(function(e,t){return e.concat([t+"-"+xn,t+"-"+En])},[]),Tn=[].concat(Sn,[_n]).reduce(function(e,t){return e.concat([t,t+"-"+xn,t+"-"+En])},[]),An=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];const jn={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,a=e.options,o=n.elements.arrow,i=n.modifiersData.popperOffsets,s=Kt(n.placement),l=pn(s),u=[kn,wn].indexOf(s)>=0?"height":"width";if(o&&i){var c=function(e,t){return mn("number"!==typeof(e="function"===typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:vn(e,Sn))}(a.padding,n),d=an(o),f="y"===l?yn:kn,h="y"===l?bn:wn,p=n.rects.reference[u]+n.rects.reference[l]-i[l]-n.rects.popper[u],g=i[l]-n.rects.reference[l],m=hn(o),v=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,y=p/2-g/2,b=c[f],w=v-d[u]-c[h],k=v/2-d[u]/2+y,_=gn(b,k,w),S=l;n.modifiersData[r]=((t={})[S]=_,t.centerOffset=_-k,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!==typeof r||(r=t.elements.popper.querySelector(r)))&&on(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Rn(e){return e.split("-")[1]}var Nn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ln(e){var t,n=e.popper,r=e.popperRect,a=e.placement,o=e.variation,i=e.offsets,s=e.position,l=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,d=e.isFixed,f=i.x,h=void 0===f?0:f,p=i.y,g=void 0===p?0:p,m="function"===typeof c?c({x:h,y:g}):{x:h,y:g};h=m.x,g=m.y;var v=i.hasOwnProperty("x"),y=i.hasOwnProperty("y"),b=kn,w=yn,k=window;if(u){var _=hn(n),S="clientHeight",x="clientWidth";if(_===Jt(n)&&"static"!==ln(_=cn(n)).position&&"absolute"===s&&(S="scrollHeight",x="scrollWidth"),a===yn||(a===kn||a===wn)&&o===En)w=bn,g-=(d&&_===k&&k.visualViewport?k.visualViewport.height:_[S])-r.height,g*=l?1:-1;if(a===kn||(a===yn||a===bn)&&o===En)b=wn,h-=(d&&_===k&&k.visualViewport?k.visualViewport.width:_[x])-r.width,h*=l?1:-1}var E,C=Object.assign({position:s},u&&Nn),O=!0===c?function(e,t){var n=e.x,r=e.y,a=t.devicePixelRatio||1;return{x:en(n*a)/a||0,y:en(r*a)/a||0}}({x:h,y:g},Jt(n)):{x:h,y:g};return h=O.x,g=O.y,l?Object.assign({},C,((E={})[w]=y?"0":"",E[b]=v?"0":"",E.transform=(k.devicePixelRatio||1)<=1?"translate("+h+"px, "+g+"px)":"translate3d("+h+"px, "+g+"px, 0)",E)):Object.assign({},C,((t={})[w]=y?g+"px":"",t[b]=v?h+"px":"",t.transform="",t))}var $n={passive:!0};const Dn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,a=r.scroll,o=void 0===a||a,i=r.resize,s=void 0===i||i,l=Jt(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&u.forEach(function(e){e.addEventListener("scroll",n.update,$n)}),s&&l.addEventListener("resize",n.update,$n),function(){o&&u.forEach(function(e){e.removeEventListener("scroll",n.update,$n)}),s&&l.removeEventListener("resize",n.update,$n)}},data:{}};var In={left:"right",right:"left",bottom:"top",top:"bottom"};function zn(e){return e.replace(/left|right|bottom|top/g,function(e){return In[e]})}var Mn={start:"end",end:"start"};function Fn(e){return e.replace(/start|end/g,function(e){return Mn[e]})}function Un(e){var t=Jt(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Bn(e){return rn(cn(e)).left+Un(e).scrollLeft}function Hn(e){var t=ln(e),n=t.overflow,r=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+r)}function Wn(e){return["html","body","#document"].indexOf(sn(e))>=0?e.ownerDocument.body:Gt(e)&&Hn(e)?e:Wn(dn(e))}function qn(e,t){var n;void 0===t&&(t=[]);var r=Wn(e),a=r===(null==(n=e.ownerDocument)?void 0:n.body),o=Jt(r),i=a?[o].concat(o.visualViewport||[],Hn(r)?r:[]):r,s=t.concat(i);return a?s:s.concat(qn(dn(i)))}function Vn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Kn(e,t,n){return t===Cn?Vn(function(e,t){var n=Jt(e),r=cn(e),a=n.visualViewport,o=r.clientWidth,i=r.clientHeight,s=0,l=0;if(a){o=a.width,i=a.height;var u=nn();(u||!u&&"fixed"===t)&&(s=a.offsetLeft,l=a.offsetTop)}return{width:o,height:i,x:s+Bn(e),y:l}}(e,n)):Yt(t)?function(e,t){var n=rn(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Vn(function(e){var t,n=cn(e),r=Un(e),a=null==(t=e.ownerDocument)?void 0:t.body,o=Xt(n.scrollWidth,n.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),i=Xt(n.scrollHeight,n.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),s=-r.scrollLeft+Bn(e),l=-r.scrollTop;return"rtl"===ln(a||n).direction&&(s+=Xt(n.clientWidth,a?a.clientWidth:0)-o),{width:o,height:i,x:s,y:l}}(cn(e)))}function Jn(e,t,n,r){var a="clippingParents"===t?function(e){var t=qn(dn(e)),n=["absolute","fixed"].indexOf(ln(e).position)>=0&&Gt(e)?hn(e):e;return Yt(n)?t.filter(function(e){return Yt(e)&&on(e,n)&&"body"!==sn(e)}):[]}(e):[].concat(t),o=[].concat(a,[n]),i=o[0],s=o.reduce(function(t,n){var a=Kn(e,n,r);return t.top=Xt(a.top,t.top),t.right=Zt(a.right,t.right),t.bottom=Zt(a.bottom,t.bottom),t.left=Xt(a.left,t.left),t},Kn(e,i,r));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function Yn(e){var t,n=e.reference,r=e.element,a=e.placement,o=a?Kt(a):null,i=a?Rn(a):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(o){case yn:t={x:s,y:n.y-r.height};break;case bn:t={x:s,y:n.y+n.height};break;case wn:t={x:n.x+n.width,y:l};break;case kn:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var u=o?pn(o):null;if(null!=u){var c="y"===u?"height":"width";switch(i){case xn:t[u]=t[u]-(n[c]/2-r[c]/2);break;case En:t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}function Gn(e,t){void 0===t&&(t={});var n=t,r=n.placement,a=void 0===r?e.placement:r,o=n.strategy,i=void 0===o?e.strategy:o,s=n.boundary,l=void 0===s?"clippingParents":s,u=n.rootBoundary,c=void 0===u?Cn:u,d=n.elementContext,f=void 0===d?On:d,h=n.altBoundary,p=void 0!==h&&h,g=n.padding,m=void 0===g?0:g,v=mn("number"!==typeof m?m:vn(m,Sn)),y=f===On?"reference":On,b=e.rects.popper,w=e.elements[p?y:f],k=Jn(Yt(w)?w:w.contextElement||cn(e.elements.popper),l,c,i),_=rn(e.elements.reference),S=Yn({reference:_,element:b,strategy:"absolute",placement:a}),x=Vn(Object.assign({},b,S)),E=f===On?x:_,C={top:k.top-E.top+v.top,bottom:E.bottom-k.bottom+v.bottom,left:k.left-E.left+v.left,right:E.right-k.right+v.right},O=e.modifiersData.offset;if(f===On&&O){var P=O[a];Object.keys(C).forEach(function(e){var t=[wn,bn].indexOf(e)>=0?1:-1,n=[yn,bn].indexOf(e)>=0?"y":"x";C[e]+=P[n]*t})}return C}function Qn(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Xn(e){return[yn,wn,bn,kn].some(function(t){return e[t]>=0})}const Zn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.offset,o=void 0===a?[0,0]:a,i=Tn.reduce(function(e,n){return e[n]=function(e,t,n){var r=Kt(e),a=[kn,yn].indexOf(r)>=0?-1:1,o="function"===typeof n?n(Object.assign({},t,{placement:e})):n,i=o[0],s=o[1];return i=i||0,s=(s||0)*a,[kn,wn].indexOf(r)>=0?{x:s,y:i}:{x:i,y:s}}(n,t.rects,o),e},{}),s=i[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=i}};const er={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.mainAxis,o=void 0===a||a,i=n.altAxis,s=void 0!==i&&i,l=n.boundary,u=n.rootBoundary,c=n.altBoundary,d=n.padding,f=n.tether,h=void 0===f||f,p=n.tetherOffset,g=void 0===p?0:p,m=Gn(t,{boundary:l,rootBoundary:u,padding:d,altBoundary:c}),v=Kt(t.placement),y=Rn(t.placement),b=!y,w=pn(v),k="x"===w?"y":"x",_=t.modifiersData.popperOffsets,S=t.rects.reference,x=t.rects.popper,E="function"===typeof g?g(Object.assign({},t.rects,{placement:t.placement})):g,C="number"===typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),O=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(_){if(o){var T,A="y"===w?yn:kn,j="y"===w?bn:wn,R="y"===w?"height":"width",N=_[w],L=N+m[A],$=N-m[j],D=h?-x[R]/2:0,I=y===xn?S[R]:x[R],z=y===xn?-x[R]:-S[R],M=t.elements.arrow,F=h&&M?an(M):{width:0,height:0},U=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},B=U[A],H=U[j],W=gn(0,S[R],F[R]),q=b?S[R]/2-D-W-B-C.mainAxis:I-W-B-C.mainAxis,V=b?-S[R]/2+D+W+H+C.mainAxis:z+W+H+C.mainAxis,K=t.elements.arrow&&hn(t.elements.arrow),J=K?"y"===w?K.clientTop||0:K.clientLeft||0:0,Y=null!=(T=null==O?void 0:O[w])?T:0,G=N+V-Y,Q=gn(h?Zt(L,N+q-Y-J):L,N,h?Xt($,G):$);_[w]=Q,P[w]=Q-N}if(s){var X,Z="x"===w?yn:kn,ee="x"===w?bn:wn,te=_[k],ne="y"===k?"height":"width",re=te+m[Z],ae=te-m[ee],oe=-1!==[yn,kn].indexOf(v),ie=null!=(X=null==O?void 0:O[k])?X:0,se=oe?re:te-S[ne]-x[ne]-ie+C.altAxis,le=oe?te+S[ne]+x[ne]-ie-C.altAxis:ae,ue=h&&oe?function(e,t,n){var r=gn(e,t,n);return r>n?n:r}(se,te,le):gn(h?se:re,te,h?le:ae);_[k]=ue,P[k]=ue-te}t.modifiersData[r]=P}},requiresIfExists:["offset"]};function tr(e,t,n){void 0===n&&(n=!1);var r=Gt(t),a=Gt(t)&&function(e){var t=e.getBoundingClientRect(),n=en(t.width)/e.offsetWidth||1,r=en(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),o=cn(t),i=rn(e,a,n),s={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&(("body"!==sn(t)||Hn(o))&&(s=function(e){return e!==Jt(e)&&Gt(e)?{scrollLeft:(t=e).scrollLeft,scrollTop:t.scrollTop}:Un(e);var t}(t)),Gt(t)?((l=rn(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):o&&(l.x=Bn(o))),{x:i.left+s.scrollLeft-l.x,y:i.top+s.scrollTop-l.y,width:i.width,height:i.height}}function nr(e){var t=new Map,n=new Set,r=[];function a(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!n.has(e)){var r=t.get(e);r&&a(r)}}),r.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||a(e)}),r}function rr(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}var ar={placement:"bottom",modifiers:[],strategy:"absolute"};function or(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"===typeof e.getBoundingClientRect)})}function ir(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,a=t.defaultOptions,o=void 0===a?ar:a;return function(e,t,n){void 0===n&&(n=o);var a={placement:"bottom",orderedModifiers:[],options:Object.assign({},ar,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],s=!1,l={state:a,setOptions:function(n){var s="function"===typeof n?n(a.options):n;u(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:Yt(e)?qn(e):e.contextElement?qn(e.contextElement):[],popper:qn(t)};var c=function(e){var t=nr(e);return An.reduce(function(e,n){return e.concat(t.filter(function(e){return e.phase===n}))},[])}(function(e){var t=e.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{});return Object.keys(t).map(function(e){return t[e]})}([].concat(r,a.options.modifiers)));return a.orderedModifiers=c.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"===typeof o){var s=o({state:a,name:t,instance:l,options:r}),u=function(){};i.push(s||u)}}),l.update()},forceUpdate:function(){if(!s){var e=a.elements,t=e.reference,n=e.popper;if(or(t,n)){a.rects={reference:tr(t,hn(n),"fixed"===a.options.strategy),popper:an(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var o=a.orderedModifiers[r],i=o.fn,u=o.options,c=void 0===u?{}:u,d=o.name;"function"===typeof i&&(a=i({state:a,options:c,name:d,instance:l})||a)}else a.reset=!1,r=-1}}},update:rr(function(){return new Promise(function(e){l.forceUpdate(),e(a)})}),destroy:function(){u(),s=!0}};if(!or(e,t))return l;function u(){i.forEach(function(e){return e()}),i=[]}return l.setOptions(n).then(function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)}),l}}const sr=ir({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,a=t.rects.popper,o=t.modifiersData.preventOverflow,i=Gn(t,{elementContext:"reference"}),s=Gn(t,{altBoundary:!0}),l=Qn(i,r),u=Qn(s,a,o),c=Xn(l),d=Xn(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Yn({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,a=void 0===r||r,o=n.adaptive,i=void 0===o||o,s=n.roundOffsets,l=void 0===s||s,u={placement:Kt(t.placement),variation:Rn(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Ln(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ln(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Dn,Zn,{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var a=n.mainAxis,o=void 0===a||a,i=n.altAxis,s=void 0===i||i,l=n.fallbackPlacements,u=n.padding,c=n.boundary,d=n.rootBoundary,f=n.altBoundary,h=n.flipVariations,p=void 0===h||h,g=n.allowedAutoPlacements,m=t.options.placement,v=Kt(m),y=l||(v===m||!p?[zn(m)]:function(e){if(Kt(e)===_n)return[];var t=zn(e);return[Fn(e),t,Fn(t)]}(m)),b=[m].concat(y).reduce(function(e,n){return e.concat(Kt(n)===_n?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,a=n.boundary,o=n.rootBoundary,i=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?Tn:l,c=Rn(r),d=c?s?Pn:Pn.filter(function(e){return Rn(e)===c}):Sn,f=d.filter(function(e){return u.indexOf(e)>=0});0===f.length&&(f=d);var h=f.reduce(function(t,n){return t[n]=Gn(e,{placement:n,boundary:a,rootBoundary:o,padding:i})[Kt(n)],t},{});return Object.keys(h).sort(function(e,t){return h[e]-h[t]})}(t,{placement:n,boundary:c,rootBoundary:d,padding:u,flipVariations:p,allowedAutoPlacements:g}):n)},[]),w=t.rects.reference,k=t.rects.popper,_=new Map,S=!0,x=b[0],E=0;E<b.length;E++){var C=b[E],O=Kt(C),P=Rn(C)===xn,T=[yn,bn].indexOf(O)>=0,A=T?"width":"height",j=Gn(t,{placement:C,boundary:c,rootBoundary:d,altBoundary:f,padding:u}),R=T?P?wn:kn:P?bn:yn;w[A]>k[A]&&(R=zn(R));var N=zn(R),L=[];if(o&&L.push(j[O]<=0),s&&L.push(j[R]<=0,j[N]<=0),L.every(function(e){return e})){x=C,S=!1;break}_.set(C,L)}if(S)for(var $=function(e){var t=b.find(function(t){var n=_.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return x=t,"break"},D=p?3:1;D>0;D--){if("break"===$(D))break}t.placement!==x&&(t.modifiersData[r]._skip=!0,t.placement=x,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},er,jn]}),lr=["enabled","placement","strategy","modifiers"];const ur={name:"applyStyles",enabled:!1,phase:"afterWrite",fn:()=>{}},cr={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:e=>{let{state:t}=e;return()=>{const{reference:e,popper:n}=t.elements;if("removeAttribute"in e){const t=(e.getAttribute("aria-describedby")||"").split(",").filter(e=>e.trim()!==n.id);t.length?e.setAttribute("aria-describedby",t.join(",")):e.removeAttribute("aria-describedby")}}},fn:e=>{let{state:t}=e;var n;const{popper:r,reference:a}=t.elements,o=null==(n=r.getAttribute("role"))?void 0:n.toLowerCase();if(r.id&&"tooltip"===o&&"setAttribute"in a){const e=a.getAttribute("aria-describedby");if(e&&-1!==e.split(",").indexOf(r.id))return;a.setAttribute("aria-describedby",e?`${e},${r.id}`:r.id)}}},dr=[];const fr=function(t,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{enabled:a=!0,placement:o="bottom",strategy:i="absolute",modifiers:s=dr}=r,l=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(r,lr);const u=(0,e.useRef)(s),c=(0,e.useRef)(),d=(0,e.useCallback)(()=>{var e;null==(e=c.current)||e.update()},[]),f=(0,e.useCallback)(()=>{var e;null==(e=c.current)||e.forceUpdate()},[]),[h,p]=Vt((0,e.useState)({placement:o,update:d,forceUpdate:f,attributes:{},styles:{popper:{},arrow:{}}})),g=(0,e.useMemo)(()=>({name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:e=>{let{state:t}=e;const n={},r={};Object.keys(t.elements).forEach(e=>{n[e]=t.styles[e],r[e]=t.attributes[e]}),p({state:t,styles:n,attributes:r,update:d,forceUpdate:f,placement:t.placement})}}),[d,f,p]),m=(0,e.useMemo)(()=>(qt(u.current,s)||(u.current=s),u.current),[s]);return(0,e.useEffect)(()=>{c.current&&a&&c.current.setOptions({placement:o,strategy:i,modifiers:[...m,g,ur]})},[i,o,g,a,m]),(0,e.useEffect)(()=>{if(a&&null!=t&&null!=n)return c.current=sr(t,n,Object.assign({},l,{placement:o,strategy:i,modifiers:[...m,cr,g]})),()=>{null!=c.current&&(c.current.destroy(),c.current=void 0,p(e=>Object.assign({},e,{attributes:{},styles:{popper:{}}})))}},[a,t,n]),h};var hr=n(6440),pr=n.n(hr);const gr=()=>{};const mr=e=>e&&("current"in e?e.current:e),vr={click:"mousedown",mouseup:"mousedown",pointerup:"pointerdown"};const yr=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:gr,{disabled:r,clickTrigger:a="click"}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=(0,e.useRef)(!1),i=(0,e.useRef)(!1),s=(0,e.useCallback)(e=>{const n=mr(t);var r;pr()(!!n,"ClickOutside captured a close event but does not have a ref to compare it to. useClickOutside(), should be passed a ref that resolves to a DOM node"),o.current=!n||!!((r=e).metaKey||r.altKey||r.ctrlKey||r.shiftKey)||!function(e){return 0===e.button}(e)||!!Pe(n,e.target)||i.current,i.current=!1},[t]),l=(0,$e.A)(e=>{const n=mr(t);n&&Pe(n,e.target)?i.current=!0:i.current=!1}),u=(0,$e.A)(e=>{o.current||n(e)});(0,e.useEffect)(()=>{var e,n;if(r||null==t)return;const o=(0,Ce.A)(mr(t)),i=o.defaultView||window;let c=null!=(e=i.event)?e:null==(n=i.parent)?void 0:n.event,d=null;vr[a]&&(d=(0,Ae.A)(o,vr[a],l,!0));const f=(0,Ae.A)(o,a,s,!0),h=(0,Ae.A)(o,a,e=>{e!==c?u(e):c=void 0});let p=[];return"ontouchstart"in o.documentElement&&(p=[].slice.call(o.body.children).map(e=>(0,Ae.A)(e,"mousemove",gr))),()=>{null==d||d(),f(),h(),p.forEach(e=>e())}},[t,r,a,s,l,u])};function br(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Array.isArray(e)?e:Object.keys(e).map(t=>(e[t].name=t,e[t]))}function wr(e){let{enabled:t,enableEvents:n,placement:r,flip:a,offset:o,fixed:i,containerPadding:s,arrowElement:l,popperConfig:u={}}=e;var c,d,f,h,p;const g=function(e){const t={};return Array.isArray(e)?(null==e||e.forEach(e=>{t[e.name]=e}),t):e||t}(u.modifiers);return Object.assign({},u,{placement:r,enabled:t,strategy:i?"fixed":u.strategy,modifiers:br(Object.assign({},g,{eventListeners:{enabled:n,options:null==(c=g.eventListeners)?void 0:c.options},preventOverflow:Object.assign({},g.preventOverflow,{options:s?Object.assign({padding:s},null==(d=g.preventOverflow)?void 0:d.options):null==(f=g.preventOverflow)?void 0:f.options}),offset:{options:Object.assign({offset:o},null==(h=g.offset)?void 0:h.options)},arrow:Object.assign({},g.arrow,{enabled:!!l,options:Object.assign({},null==(p=g.arrow)?void 0:p.options,{element:l})}),flip:Object.assign({enabled:!!a},g.flip)}))})}const kr=["children","usePopper"];const _r=()=>{};function Sr(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=(0,e.useContext)(Ut),[r,a]=(0,Bt.A)(),o=(0,e.useRef)(!1),{flip:i,offset:s,rootCloseEvent:l,fixed:u=!1,placement:c,popperConfig:d={},enableEventListeners:f=!0,usePopper:h=!!n}=t,p=null==(null==n?void 0:n.show)?!!t.show:n.show;p&&!o.current&&(o.current=!0);const{placement:g,setMenu:m,menuElement:v,toggleElement:y}=n||{},b=fr(y,v,wr({placement:c||g||"bottom-start",enabled:h,enableEvents:null==f?p:f,offset:s,flip:i,fixed:u,arrowElement:r,popperConfig:d})),w=Object.assign({ref:m||_r,"aria-labelledby":null==y?void 0:y.id},b.attributes.popper,{style:b.styles.popper}),k={show:p,placement:g,hasShown:o.current,toggle:null==n?void 0:n.toggle,popper:h?b:null,arrowProps:h?Object.assign({ref:a},b.attributes.arrow,{style:b.styles.arrow}):{}};return yr(v,e=>{null==n||n.toggle(!1,e)},{clickTrigger:l,disabled:!p}),[w,k]}function xr(e){let{children:t,usePopper:n=!0}=e,r=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,kr);const[a,o]=Sr(Object.assign({},r,{usePopper:n}));return(0,Z.jsx)(Z.Fragment,{children:t(a,o)})}xr.displayName="DropdownMenu";const Er=xr,Cr={prefix:String(Math.round(1e10*Math.random())),current:0},Or=e.createContext(Cr),Pr=e.createContext(!1);Boolean("undefined"!==typeof window&&window.document&&window.document.createElement);let Tr=new WeakMap;function Ar(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=(0,e.useContext)(Or),r=(0,e.useRef)(null);if(null===r.current&&!t){var a,o;let t=null===(o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===o||null===(a=o.ReactCurrentOwner)||void 0===a?void 0:a.current;if(t){let e=Tr.get(t);null==e?Tr.set(t,{id:n.current,state:t.memoizedState}):t.memoizedState!==e.state&&(n.current=e.id,Tr.delete(t))}r.current=++n.current}return r.current}const jr="function"===typeof e.useId?function(t){let n=e.useId(),[r]=(0,e.useState)("function"===typeof e.useSyncExternalStore?e.useSyncExternalStore(Lr,Rr,Nr):(0,e.useContext)(Pr));return t||`${r?"react-aria":`react-aria${Cr.prefix}`}-${n}`}:function(t){let n=(0,e.useContext)(Or),r=Ar(!!t),a=`react-aria${n.prefix}`;return t||`${a}-${r}`};function Rr(){return!1}function Nr(){return!0}function Lr(e){return()=>{}}const $r=e=>{var t;return"menu"===(null==(t=e.getAttribute("role"))?void 0:t.toLowerCase())},Dr=()=>{};function Ir(){const t=jr(),{show:n=!1,toggle:r=Dr,setToggle:a,menuElement:o}=(0,e.useContext)(Ut)||{},i=(0,e.useCallback)(e=>{r(!n,e)},[n,r]),s={id:t,ref:a||Dr,onClick:i,"aria-expanded":!!n};return o&&$r(o)&&(s["aria-haspopup"]=!0),[s,{show:n,toggle:r}]}function zr(e){let{children:t}=e;const[n,r]=Ir();return(0,Z.jsx)(Z.Fragment,{children:t(n,r)})}zr.displayName="DropdownToggle";const Mr=zr;var Fr=n(9048),Ur=n(4140);const Br=["eventKey","disabled","onClick","active","as"];function Hr(t){let{key:n,href:r,active:a,disabled:o,onClick:i}=t;const s=(0,e.useContext)(G.A),l=(0,e.useContext)(Fr.A),{activeKey:u}=l||{},c=(0,G.u)(n,r),d=null==a&&null!=n?(0,G.u)(u)===c:a;return[{onClick:(0,$e.A)(e=>{o||(null==i||i(e),s&&!e.isPropagationStopped()&&s(c,e))}),"aria-disabled":o||void 0,"aria-selected":d,[(0,De.sE)("dropdown-item")]:""},{isActive:d}]}const Wr=e.forwardRef((e,t)=>{let{eventKey:n,disabled:r,onClick:a,active:o,as:i=Ur.Ay}=e,s=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,Br);const[l]=Hr({key:n,href:s.href,disabled:r,onClick:a,active:o});return(0,Z.jsx)(i,Object.assign({},s,{ref:t},l))});Wr.displayName="DropdownItem";const qr=Wr;function Vr(){const t=(0,Mt.A)(),n=(0,e.useRef)(null),r=(0,e.useCallback)(e=>{n.current=e,t()},[t]);return[n,r]}function Kr(t){let{defaultShow:n,show:r,onSelect:a,onToggle:o,itemSelector:i=`* [${(0,De.sE)("dropdown-item")}]`,focusFirstItemOnShow:s,placement:l="bottom-start",children:u}=t;const c=Fe(),[d,f]=zt(r,n,o),[h,p]=Vr(),g=h.current,[m,v]=Vr(),y=m.current,b=(0,Le.A)(d),w=(0,e.useRef)(null),k=(0,e.useRef)(!1),_=(0,e.useContext)(G.A),S=(0,e.useCallback)(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null==t?void 0:t.type;f(e,{originalEvent:t,source:n})},[f]),x=(0,$e.A)((e,t)=>{null==a||a(e,t),S(!1,t,"select"),t.isPropagationStopped()||null==_||_(e,t)}),E=(0,e.useMemo)(()=>({toggle:S,placement:l,show:d,menuElement:g,toggleElement:y,setMenu:p,setToggle:v}),[S,l,d,g,y,p,v]);g&&b&&!d&&(k.current=g.contains(g.ownerDocument.activeElement));const C=(0,$e.A)(()=>{y&&y.focus&&y.focus()}),O=(0,$e.A)(()=>{const e=w.current;let t=s;if(null==t&&(t=!(!h.current||!$r(h.current))&&"keyboard"),!1===t||"keyboard"===t&&!/^key.+$/.test(e))return;const n=(0,vt.A)(h.current,i)[0];n&&n.focus&&n.focus()});(0,e.useEffect)(()=>{d?O():k.current&&(k.current=!1,C())},[d,k,C,O]),(0,e.useEffect)(()=>{w.current=null});const P=(e,t)=>{if(!h.current)return null;const n=(0,vt.A)(h.current,i);let r=n.indexOf(e)+t;return r=Math.max(0,Math.min(r,n.length)),n[r]};return(0,Ft.A)((0,e.useCallback)(()=>c.document,[c]),"keydown",e=>{var t,n;const{key:r}=e,a=e.target,o=null==(t=h.current)?void 0:t.contains(a),i=null==(n=m.current)?void 0:n.contains(a);if(/input|textarea/i.test(a.tagName)&&(" "===r||"Escape"!==r&&o||"Escape"===r&&"search"===a.type))return;if(!o&&!i)return;if("Tab"===r&&(!h.current||!d))return;w.current=e.type;const s={originalEvent:e,source:e.type};switch(r){case"ArrowUp":{const t=P(a,-1);return t&&t.focus&&t.focus(),void e.preventDefault()}case"ArrowDown":if(e.preventDefault(),d){const e=P(a,1);e&&e.focus&&e.focus()}else f(!0,s);return;case"Tab":(0,It.Ay)(a.ownerDocument,"keyup",e=>{var t;("Tab"!==e.key||e.target)&&null!=(t=h.current)&&t.contains(e.target)||f(!1,s)},{once:!0});break;case"Escape":"Escape"===r&&(e.preventDefault(),e.stopPropagation()),f(!1,s)}}),(0,Z.jsx)(G.A.Provider,{value:x,children:(0,Z.jsx)(Ut.Provider,{value:E,children:u})})}Kr.displayName="Dropdown",Kr.Menu=Er,Kr.Toggle=Mr,Kr.Item=qr;const Jr=Kr,Yr=e.createContext({});Yr.displayName="DropdownContext";const Gr=Yr,Qr=e.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:a="hr",role:o="separator",...i}=e;return r=(0,X.oU)(r,"dropdown-divider"),(0,Z.jsx)(a,{ref:t,className:Y()(n,r),role:o,...i})});Qr.displayName="DropdownDivider";const Xr=Qr,Zr=e.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:a="div",role:o="heading",...i}=e;return r=(0,X.oU)(r,"dropdown-header"),(0,Z.jsx)(a,{ref:t,className:Y()(n,r),role:o,...i})});Zr.displayName="DropdownHeader";const ea=Zr;var ta=n(7071);const na=e.forwardRef((e,t)=>{let{bsPrefix:n,className:r,eventKey:a,disabled:o=!1,onClick:i,active:s,as:l=ta.A,...u}=e;const c=(0,X.oU)(n,"dropdown-item"),[d,f]=Hr({key:a,href:u.href,disabled:o,onClick:i,active:s});return(0,Z.jsx)(l,{...u,...d,ref:t,className:Y()(r,c,f.isActive&&"active",o&&"disabled")})});na.displayName="DropdownItem";const ra=na,aa=e.forwardRef((e,t)=>{let{className:n,bsPrefix:r,as:a="span",...o}=e;return r=(0,X.oU)(r,"dropdown-item-text"),(0,Z.jsx)(a,{ref:t,className:Y()(n,r),...o})});aa.displayName="DropdownItemText";const oa=aa;var ia=n(8293);const sa=e.createContext(null);sa.displayName="InputGroupContext";const la=sa;n(2740);function ua(e,t){return e}function ca(e,t,n){let r=e?n?"bottom-start":"bottom-end":n?"bottom-end":"bottom-start";return"up"===t?r=e?n?"top-start":"top-end":n?"top-end":"top-start":"end"===t?r=e?n?"left-end":"right-end":n?"left-start":"right-start":"start"===t?r=e?n?"right-end":"left-end":n?"right-start":"left-start":"down-centered"===t?r="bottom":"up-centered"===t&&(r="top"),r}const da=e.forwardRef((t,n)=>{let{bsPrefix:r,className:a,align:o,rootCloseEvent:i,flip:s=!0,show:l,renderOnMount:u,as:c="div",popperConfig:d,variant:f,...h}=t,p=!1;const g=(0,e.useContext)(pe.A),m=(0,X.oU)(r,"dropdown-menu"),{align:v,drop:y,isRTL:b}=(0,e.useContext)(Gr);o=o||v;const w=(0,e.useContext)(la),k=[];if(o)if("object"===typeof o){const e=Object.keys(o);if(e.length){const t=e[0],n=o[t];p="start"===n,k.push(`${m}-${t}-${n}`)}}else"end"===o&&(p=!0);const _=ca(p,y,b),[S,{hasShown:x,popper:E,show:C,toggle:O}]=Sr({flip:s,rootCloseEvent:i,show:l,usePopper:!g&&0===k.length,offset:[0,2],popperConfig:d,placement:_});if(S.ref=(0,ia.A)(ua(n),S.ref),ke(()=>{C&&(null==E||E.update())},[C]),!x&&!u&&!w)return null;"string"!==typeof c&&(S.show=C,S.close=()=>null==O?void 0:O(!1),S.align=o);let P=h.style;return null!=E&&E.placement&&(P={...h.style,...S.style},h["x-placement"]=E.placement),(0,Z.jsx)(c,{...h,...S,style:P,...(k.length||g)&&{"data-bs-popper":"static"},className:Y()(a,m,C&&"show",p&&`${m}-end`,f&&`${m}-${f}`,...k)})});da.displayName="DropdownMenu";const fa=da;var ha=n(4282);const pa=e.forwardRef((t,n)=>{let{bsPrefix:r,split:a,className:o,childBsPrefix:i,as:s=ha.A,...l}=t;const u=(0,X.oU)(r,"dropdown-toggle"),c=(0,e.useContext)(Ut);void 0!==i&&(l.bsPrefix=i);const[d]=Ir();return d.ref=(0,ia.A)(d.ref,ua(n)),(0,Z.jsx)(s,{className:Y()(o,u,a&&`${u}-split`,(null==c?void 0:c.show)&&"show"),...d,...l})});pa.displayName="DropdownToggle";const ga=pa,ma=e.forwardRef((t,n)=>{const{bsPrefix:r,drop:a="down",show:o,className:i,align:s="start",onSelect:l,onToggle:u,focusFirstItemOnShow:c,as:d="div",navbar:f,autoClose:h=!0,...p}=(0,Q.Zw)(t,{show:"onToggle"}),g=(0,e.useContext)(la),m=(0,X.oU)(r,"dropdown"),v=(0,X.Wz)(),y=(0,ve.A)((e,t)=>{var n;var r;(null==(n=t.originalEvent)||null==(n=n.target)?void 0:n.classList.contains("dropdown-toggle"))&&"mousedown"===t.source||(t.originalEvent.currentTarget!==document||"keydown"===t.source&&"Escape"!==t.originalEvent.key||(t.source="rootClose"),r=t.source,(!1===h?"click"===r:"inside"===h?"rootClose"!==r:"outside"!==h||"select"!==r)&&(null==u||u(e,t)))}),b=ca("end"===s,a,v),w=(0,e.useMemo)(()=>({align:s,drop:a,isRTL:v}),[s,a,v]),k={down:m,"down-centered":`${m}-center`,up:"dropup","up-centered":"dropup-center dropup",end:"dropend",start:"dropstart"};return(0,Z.jsx)(Gr.Provider,{value:w,children:(0,Z.jsx)(Jr,{placement:b,show:o,onSelect:l,onToggle:y,focusFirstItemOnShow:c,itemSelector:`.${m}-item:not(.disabled):not(:disabled)`,children:g?p.children:(0,Z.jsx)(d,{...p,ref:n,className:Y()(i,o&&"show",k[a])})})})});ma.displayName="Dropdown";const va=Object.assign(ma,{Toggle:ga,Menu:fa,Item:ra,ItemText:oa,Divider:Xr,Header:ea});var ya=n(4541);const ba=e.forwardRef((e,t)=>{let{id:n,title:r,children:a,bsPrefix:o,className:i,rootCloseEvent:s,menuRole:l,disabled:u,active:c,renderMenuOnMount:d,menuVariant:f,...h}=e;const p=(0,X.oU)(void 0,"nav-item");return(0,Z.jsxs)(va,{ref:t,...h,className:Y()(i,p),children:[(0,Z.jsx)(va.Toggle,{id:n,eventKey:null,active:c,disabled:u,childBsPrefix:o,as:ya.A,children:r}),(0,Z.jsx)(va.Menu,{role:l,renderOnMount:d,rootCloseEvent:s,variant:f,children:a})]})});ba.displayName="NavDropdown";const wa=Object.assign(ba,{Item:va.Item,ItemText:va.ItemText,Divider:va.Divider,Header:va.Header}),ka=e.lazy(()=>Promise.all([n.e(741),n.e(93)]).then(n.bind(n,9093))),_a=e.lazy(()=>Promise.all([n.e(141),n.e(588)]).then(n.bind(n,588))),Sa=e.lazy(()=>n.e(560).then(n.bind(n,3560))),xa=e.lazy(()=>n.e(455).then(n.bind(n,8455))),Ea=e.lazy(()=>n.e(880).then(n.bind(n,880))),Ca=e.lazy(()=>n.e(592).then(n.bind(n,592))),Oa=e.lazy(()=>n.e(285).then(n.bind(n,1285))),Pa=e.lazy(()=>Promise.all([n.e(741),n.e(738)]).then(n.bind(n,2738))),Ta=e.lazy(()=>n.e(53).then(n.bind(n,7434))),Aa=e.lazy(()=>n.e(505).then(n.bind(n,505))),ja=e.lazy(()=>n.e(190).then(n.bind(n,6190))),Ra=e.lazy(()=>n.e(254).then(n.bind(n,7254))),Na=e.lazy(()=>n.e(668).then(n.bind(n,668))),La=e.lazy(()=>n.e(622).then(n.bind(n,8622))),$a=e.lazy(()=>n.e(365).then(n.bind(n,746))),Da=e.lazy(()=>n.e(145).then(n.bind(n,5145)));const Ia=function(){const{t:t,i18n:n}=(0,q.Bd)(),[r,a]=(0,e.useState)(null),[o,i]=(0,e.useState)(!0);(0,e.useEffect)(()=>{(async()=>{const e=await(0,V.F)();a(e),i(!1)})()},[]);const s=e=>{n.changeLanguage(e)};return e.useEffect(()=>{console.log("App mounted. Current URL:",window.location.href),console.log("Hash:",window.location.hash)},[]),(0,Z.jsx)(K.I9,{children:(0,Z.jsxs)("div",{children:[(0,Z.jsx)(Lt,{bg:"dark",variant:"dark",expand:"lg",children:(0,Z.jsxs)($t.A,{children:[(0,Z.jsx)(Lt.Brand,{as:K.N_,to:"/",children:"FIL Platform"}),(0,Z.jsx)(Lt.Toggle,{"aria-controls":"basic-navbar-nav"}),(0,Z.jsxs)(Lt.Collapse,{id:"basic-navbar-nav",children:[(0,Z.jsx)(Dt.A,{className:"me-auto",children:(0,Z.jsx)(Dt.A.Link,{href:"#/",children:t("dashboard")})}),(0,Z.jsx)(Dt.A,{children:(0,Z.jsxs)(wa,{title:t("language"),id:"basic-nav-dropdown",children:[(0,Z.jsx)(wa.Item,{onClick:()=>s("en"),children:"English"}),(0,Z.jsx)(wa.Item,{onClick:()=>s("zh"),children:"\u4e2d\u6587"}),(0,Z.jsx)(wa.Item,{onClick:()=>s("ja"),children:"\u65e5\u672c\u8a9e"})]})})]})]})}),(0,Z.jsx)($t.A,{className:"mt-4",children:(0,Z.jsxs)(e.Suspense,{fallback:(0,Z.jsx)("div",{children:t("loading")}),children:[(0,Z.jsx)(Da,{}),o?(0,Z.jsx)("div",{children:t("initializing_platform")}):r?(0,Z.jsxs)(K.BV,{children:[(0,Z.jsx)(K.qh,{path:"/login",element:(0,Z.jsx)(ka,{})}),(0,Z.jsx)(K.qh,{path:"/",element:(0,Z.jsx)(_a,{})}),(0,Z.jsx)(K.qh,{path:"/products",element:(0,Z.jsx)(Sa,{})}),(0,Z.jsx)(K.qh,{path:"/orders",element:(0,Z.jsx)(xa,{})}),(0,Z.jsx)(K.qh,{path:"/wallet",element:(0,Z.jsx)(Ea,{})}),(0,Z.jsx)(K.qh,{path:"/my",element:(0,Z.jsx)(Ca,{})}),(0,Z.jsx)(K.qh,{path:"/my-gains",element:(0,Z.jsx)(Oa,{})}),(0,Z.jsx)(K.qh,{path:"/my/kyc",element:(0,Z.jsx)(Pa,{})}),(0,Z.jsx)(K.qh,{path:"/my/recommend",element:(0,Z.jsx)(Ta,{})}),(0,Z.jsx)(K.qh,{path:"/agent",element:(0,Z.jsx)(Aa,{})}),(0,Z.jsx)(K.qh,{path:"/agent/members",element:(0,Z.jsx)(ja,{})}),(0,Z.jsx)(K.qh,{path:"/agent/products",element:(0,Z.jsx)(Ra,{})}),(0,Z.jsx)(K.qh,{path:"/maker",element:(0,Z.jsx)(Na,{})}),(0,Z.jsx)(K.qh,{path:"/maker/products",element:(0,Z.jsx)(La,{})}),(0,Z.jsx)(K.qh,{path:"/maker/orders",element:(0,Z.jsx)($a,{})})]}):(0,Z.jsx)("div",{className:"alert alert-danger",children:t("backend_connection_failed")})]})})]})})};t.createRoot(document.getElementById("fil-platform-root")).render((0,Z.jsx)(e.StrictMode,{children:(0,Z.jsx)(Ia,{})}))})()})();
//# sourceMappingURL=main.939a8581.js.map