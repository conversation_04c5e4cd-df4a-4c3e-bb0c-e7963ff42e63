"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[254],{1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...c}=e;const o=(0,l.oU)(r,"row"),i=(0,l.gy)(),f=(0,l.Jm)(),h=`${o}-cols`,x=[];return i.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${h}${a}-${r}`)}),(0,n.jsx)(d,{ref:s,...c,className:t()(a,o,...x)})});c.displayName="Row";const o=c},4063:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:c,className:o,as:i="span",...f}=e;const h=(0,l.oU)(r,"badge");return(0,n.jsx)(i,{ref:s,...f,className:t()(o,h,d&&"rounded-pill",c&&`text-${c}`,a&&`bg-${a}`)})});c.displayName="Badge";const o=c},4196:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:c,borderless:o,hover:i,size:f,variant:h,responsive:x,...m}=e;const p=(0,l.oU)(r,"table"),u=t()(a,p,h&&`${p}-${h}`,f&&`${p}-${f}`,d&&`${p}-${"string"===typeof d?`striped-${d}`:"striped"}`,c&&`${p}-bordered`,o&&`${p}-borderless`,i&&`${p}-hover`),j=(0,n.jsx)("table",{...m,className:u,ref:s});if(x){let e=`${p}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,n.jsx)("div",{className:e,children:j})}return j});c.displayName="Table";const o=c},7254:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),d=r(1072),l=r(8602),n=r(8628),c=r(4196),o=r(4063),i=r(4312),f=r(4117),h=r(579);const x=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[x,m]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;m(!0);const{data:s,error:a}=await e.from("products").select("\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    maker_profiles ( brand_name )\n                ").eq("is_disabled",!1).order("created_at",{ascending:!1});a?console.error("Error fetching products:",a):r(s),m(!1)})()},[]),x?(0,h.jsx)("div",{children:e("loading_products")}):(0,h.jsxs)(t.A,{children:[(0,h.jsx)("h2",{className:"mb-4",children:e("products_on_sale")}),(0,h.jsx)(d.A,{children:(0,h.jsx)(l.A,{children:(0,h.jsx)(n.A,{children:(0,h.jsx)(n.A.Body,{children:(0,h.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,h.jsx)("thead",{children:(0,h.jsxs)("tr",{children:[(0,h.jsx)("th",{children:e("product_id")}),(0,h.jsx)("th",{children:e("product_name")}),(0,h.jsx)("th",{children:e("category")}),(0,h.jsx)("th",{children:e("price")}),(0,h.jsx)("th",{children:e("total_shares")}),(0,h.jsx)("th",{children:e("sold_shares")}),(0,h.jsx)("th",{children:e("remaining_shares")}),(0,h.jsx)("th",{children:e("maker")})]})}),(0,h.jsx)("tbody",{children:0===s.length?(0,h.jsx)("tr",{children:(0,h.jsx)("td",{colSpan:"8",className:"text-center",children:e("no_products")})}):s.map(s=>{var r;return(0,h.jsxs)("tr",{children:[(0,h.jsxs)("td",{children:[s.id.substring(0,8),"..."]}),(0,h.jsx)("td",{children:s.name}),(0,h.jsx)("td",{children:(0,h.jsx)(o.A,{bg:"spot"===s.category?"success":"primary",children:e("spot"===s.category?"spot":"futures")})}),(0,h.jsx)("td",{children:s.price}),(0,h.jsx)("td",{children:s.total_shares}),(0,h.jsx)("td",{children:s.sold_shares}),(0,h.jsx)("td",{children:s.total_shares-s.sold_shares}),(0,h.jsx)("td",{children:(null===(r=s.maker_profiles)||void 0===r?void 0:r.brand_name)||"N/A"})]},s.id)})})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:c,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,l.oU)(r,"col");const n=(0,l.gy)(),c=(0,l.Jm)(),o=[],i=[];return n.forEach(e=>{const s=d[e];let a,t,l;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const n=e!==c?`-${e}`:"";a&&o.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=l&&i.push(`order${n}-${l}`),null!=t&&i.push(`offset${n}-${t}`)}),[{...d,className:t()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,n.jsx)(d,{...a,ref:s,className:t()(r,!o.length&&c)})});c.displayName="Col";const o=c},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),n=r(579);const c=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,l.oU)(a,"card-body"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const o=c,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,l.oU)(a,"card-footer"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});i.displayName="CardFooter";const f=i;var h=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...o}=e;const i=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,n.jsx)(h.A.Provider,{value:f,children:(0,n.jsx)(c,{ref:s,...o,className:t()(a,i)})})});x.displayName="CardHeader";const m=x,p=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:c="img",...o}=e;const i=(0,l.oU)(r,"card-img");return(0,n.jsx)(c,{ref:s,className:t()(d?`${i}-${d}`:i,a),...o})});p.displayName="CardImg";const u=p,j=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});j.displayName="CardImgOverlay";const b=j,N=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...c}=e;return a=(0,l.oU)(a,"card-link"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});N.displayName="CardLink";const y=N;var $=r(4488);const v=(0,$.A)("h6"),g=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=v,...c}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});g.displayName="CardSubtitle";const _=g,w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...c}=e;return a=(0,l.oU)(a,"card-text"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});w.displayName="CardText";const P=w,A=(0,$.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=A,...c}=e;return a=(0,l.oU)(a,"card-title"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...c})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:c,border:i,body:f=!1,children:h,as:x="div",...m}=e;const p=(0,l.oU)(r,"card");return(0,n.jsx)(x,{ref:s,...m,className:t()(a,p,d&&`bg-${d}`,c&&`text-${c}`,i&&`border-${i}`),children:f?(0,n.jsx)(o,{children:h}):h})});C.displayName="Card";const k=Object.assign(C,{Img:u,Title:U,Subtitle:_,Body:o,Link:y,Text:P,Header:m,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=254.5d8db93d.chunk.js.map