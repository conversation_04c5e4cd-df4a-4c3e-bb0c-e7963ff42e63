{"version": 3, "file": "static/js/190.2a8236f7.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sFCjCA,MAAMC,EAAqBzB,EAAAA,WAAiB,CAAAC,EAQzCC,KAAQ,IARkC,SAC3CC,EAAQ,GACRuB,EAAK,UAAS,KACdC,GAAO,EAAK,KACZC,EAAI,UACJxB,EACAC,GAAIC,EAAY,UACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,SAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAQF,GAAQ,eAAgBC,GAAQ,QAAQA,IAAQF,GAAM,MAAMA,SAGzGD,EAAMD,YAAc,QACpB,S,sFCjBA,MAAMM,EAAqB9B,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACT2B,EAAO,SACPC,EAAQ,WACRC,EAAU,MACVC,EAAK,KACLC,EAAI,QACJC,EAAO,WACPC,KACG9B,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmB4B,GAAW,GAAG5B,KAAqB4B,IAAWD,GAAQ,GAAG3B,KAAqB2B,IAAQJ,GAAW,GAAGvB,KAAwC,kBAAZuB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGxB,aAA8ByB,GAAc,GAAGzB,eAAgC0B,GAAS,GAAG1B,WACxV8B,GAAqBhB,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAImC,EAAY,CACd,IAAIE,EAAkB,GAAG/B,eAIzB,MAH0B,kBAAf6B,IACTE,EAAkB,GAAGA,KAAmBF,MAEtBf,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAWmC,EACXC,SAAUF,GAEd,CACA,OAAOA,IAETR,EAAMN,YAAc,QACpB,S,sJChCA,MAwFA,EAxFwBiB,KACpB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAsCvC,OApCAG,EAAAA,EAAAA,WAAU,KACeC,WACjB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,qBACLC,OAAO,yKAMPC,GAAG,WAAYN,EAAKO,IACpBC,MAAM,aAAc,CAAEC,WAAW,IAElCN,EACAO,QAAQP,MAAM,0BAA2BA,GAEzCZ,EAAWQ,GAEfL,GAAW,IAGfiB,IACD,IAEClB,GACOzB,EAAAA,EAAAA,KAAA,OAAAkB,SAAME,EAAE,sBAIfwB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAA3B,SAAA,EACNlB,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAMoC,SAAEE,EAAE,sBACxBpB,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAyC,UACAlB,EAAAA,EAAAA,KAAC8C,EAAAA,EAAG,CAAA5B,UACAlB,EAAAA,EAAAA,KAAC+C,EAAAA,EAAI,CAAA7B,UACDlB,EAAAA,EAAAA,KAAC+C,EAAAA,EAAKC,KAAI,CAAA9B,UACN0B,EAAAA,EAAAA,MAACpC,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACE,OAAK,EAACG,YAAU,EAAAG,SAAA,EACpClB,EAAAA,EAAAA,KAAA,SAAAkB,UACI0B,EAAAA,EAAAA,MAAA,MAAA1B,SAAA,EACIlB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,cACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,YACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,gBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,iBACPpB,EAAAA,EAAAA,KAAA,MAAAkB,SAAKE,EAAE,6BAGfpB,EAAAA,EAAAA,KAAA,SAAAkB,SACwB,IAAnBI,EAAQ2B,QACLjD,EAAAA,EAAAA,KAAA,MAAAkB,UACIlB,EAAAA,EAAAA,KAAA,MAAIkD,QAAQ,IAAIpE,UAAU,cAAaoC,SAAEE,EAAE,uBAG/CE,EAAQ6B,IAAIC,IAAM,IAAAC,EAAAC,EAAA,OACdV,EAAAA,EAAAA,MAAA,MAAA1B,SAAA,EACI0B,EAAAA,EAAAA,MAAA,MAAA1B,SAAA,CAAKkC,EAAOG,QAAQC,UAAU,EAAG,GAAG,UACpCxD,EAAAA,EAAAA,KAAA,MAAAkB,UAAiB,QAAZmC,EAAAD,EAAOK,aAAK,IAAAJ,OAAA,EAAZA,EAAcK,QAAS,SAC5B1D,EAAAA,EAAAA,KAAA,MAAAkB,SAAKkC,EAAOO,WAAa,SACzB3D,EAAAA,EAAAA,KAAA,MAAAkB,UAAIlB,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,GAA6B,aAAzBgD,EAAOQ,cAA+B,UAAY,UAAU1C,SAAEkC,EAAOQ,mBACpF5D,EAAAA,EAAAA,KAAA,MAAAkB,SAAK,IAAI2C,KAAiB,QAAbP,EAACF,EAAOK,aAAK,IAAAH,OAAA,EAAZA,EAAcQ,YAAYC,qBALnCX,EAAOG,2B,sFC7B5D,MAAMT,EAAmBpE,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGkF,IAEHjF,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRoF,IAjDG,SAAetF,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChB0E,EAAQ,GACRxE,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIuE,EACAC,EACA3B,SAHGvD,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCsE,OACAC,SACA3B,SACE5C,GAEJsE,EAAOtE,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDuE,GAAMD,EAAMlE,MAAc,IAATmE,EAAgB,GAAGrF,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASoE,KACvE,MAAT1B,GAAe/C,EAAQM,KAAK,QAAQD,KAAS0C,KACnC,MAAV2B,GAAgB1E,EAAQM,KAAK,SAASD,KAASqE,OAE9C,CAAC,IACHlF,EACHH,UAAWmB,IAAWnB,KAAcmF,KAAUxE,IAC7C,CACDV,KACAF,WACAoF,SAEJ,CAWOG,CAAOnF,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BgF,EACHpF,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYmF,EAAMhB,QAAUpE,OAGtDiE,EAAI5C,YAAc,MAClB,S,sFC1DA,MAAMmE,EAAwB3F,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPoF,EAASnE,YAAc,WACvB,UCdMoE,EAA0B5F,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqF,EAAWpE,YAAc,aACzB,U,cCZA,MAAMqE,EAA0B7F,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,eACtC2F,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBnE,IAClB,CAACA,IACL,OAAoBP,EAAAA,EAAAA,KAAK2E,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPtD,UAAuBlB,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,SAIvCgE,EAAWrE,YAAc,aACzB,UCvBM4E,EAAuBpG,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACTgC,EACA/B,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWa,EAAU,GAAGP,KAAUO,IAAYP,EAAQzB,MAC9DG,MAGP6F,EAAQ5E,YAAc,UACtB,UCjBM6E,EAA8BrG,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP8F,EAAe7E,YAAc,iBAC7B,UCdM8E,EAAwBtG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP+F,EAAS9E,YAAc,WACvB,U,cCbA,MAAM+E,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BzG,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYiG,KACbhG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPkG,EAAajF,YAAc,eAC3B,UChBMkF,EAAwB1G,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPmG,EAASlF,YAAc,WACvB,UCbMmF,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB5G,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYqG,KACbpG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqG,EAAUpF,YAAc,YACxB,UCPM6C,EAAoBrE,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACTsB,EAAE,KACFE,EAAI,OACJiF,EAAM,KACNC,GAAO,EAAK,SACZtE,EAEAnC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM4B,GAASpB,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWyB,EAAQH,GAAM,MAAMA,IAAME,GAAQ,QAAQA,IAAQiF,GAAU,UAAUA,KACvGrE,SAAUsE,GAAoBxF,EAAAA,EAAAA,KAAKqE,EAAU,CAC3CnD,SAAUA,IACPA,MAGT6B,EAAK7C,YAAc,OACnB,QAAeuF,OAAOC,OAAO3C,EAAM,CACjC4C,IAAKb,EACLc,MAAON,EACPO,SAAUV,EACVnC,KAAMqB,EACNyB,KAAMd,EACNe,KAAMX,EACNY,OAAQzB,EACR0B,OAAQ3B,EACR4B,WAAYnB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Badge.js", "../node_modules/react-bootstrap/esm/Table.js", "pages/agent/AgentMemberList.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst AgentMemberList = () => {\n    const { t } = useTranslation();\n    const [members, setMembers] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchMembers = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch customers referred by this agent\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .select(`\n                    user_id,\n                    real_name,\n                    verify_status,\n                    users ( email, created_at )\n                `)\n                .eq('agent_id', user.id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching members:', error);\n            } else {\n                setMembers(data);\n            }\n            setLoading(false);\n        };\n\n        fetchMembers();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_members')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_subordinates')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('user_id')}</th>\n                                        <th>{t('email')}</th>\n                                        <th>{t('real_name')}</th>\n                                        <th>{t('kyc_status')}</th>\n                                        <th>{t('registration_time')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {members.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"5\" className=\"text-center\">{t('no_subordinates')}</td>\n                                        </tr>\n                                    ) : (\n                                        members.map(member => (\n                                            <tr key={member.user_id}>\n                                                <td>{member.user_id.substring(0, 8)}...</td>\n                                                <td>{member.users?.email || 'N/A'}</td>\n                                                <td>{member.real_name || 'N/A'}</td>\n                                                <td><Badge bg={member.verify_status === 'approved' ? 'success' : 'warning'}>{member.verify_status}</Badge></td>\n                                                <td>{new Date(member.users?.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default AgentMemberList;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Badge", "bg", "pill", "text", "prefix", "Table", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "table", "responsiveClass", "children", "AgentMemberList", "t", "useTranslation", "members", "setMembers", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "order", "ascending", "console", "fetchMembers", "_jsxs", "Container", "Col", "Card", "Body", "length", "colSpan", "map", "member", "_member$users", "_member$users2", "user_id", "substring", "users", "email", "real_name", "verify_status", "Date", "created_at", "toLocaleString", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}