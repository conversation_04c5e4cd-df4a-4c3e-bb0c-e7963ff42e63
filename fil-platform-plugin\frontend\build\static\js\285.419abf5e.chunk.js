"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[285],{1072:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),o=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...c}=e;const l=(0,n.oU)(r,"row"),i=(0,n.gy)(),f=(0,n.Jm)(),m=`${l}-cols`,x=[];return i.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${m}${a}-${r}`)}),(0,o.jsx)(d,{ref:s,...c,className:t()(a,l,...x)})});c.displayName="Row";const l=c},1285:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),d=r(1072),n=r(8602),o=r(8628),c=r(4196),l=r(4063),i=r(4312),f=r(4117),m=r(579);const x=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[x,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("order_distributions").select("\n                    id,\n                    batch_id,\n                    order_id,\n                    customer_id,\n                    share_amount,\n                    reward_amount,\n                    fee_amount,\n                    progress,\n                    created_at\n                ").eq("customer_id",s.id).order("created_at",{ascending:!1});t?console.error("Error fetching earnings:",t):r(a),h(!1)})()},[]),x?(0,m.jsx)("div",{children:e("loading_earnings")}):(0,m.jsxs)(t.A,{children:[(0,m.jsx)("h2",{className:"mb-4",children:e("my_gains")}),(0,m.jsx)(d.A,{children:(0,m.jsx)(n.A,{children:(0,m.jsx)(o.A,{children:(0,m.jsx)(o.A.Body,{children:(0,m.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,m.jsx)("thead",{children:(0,m.jsxs)("tr",{children:[(0,m.jsx)("th",{children:e("gain_id")}),(0,m.jsx)("th",{children:e("order_id")}),(0,m.jsx)("th",{children:e("shares")}),(0,m.jsx)("th",{children:e("gain_amount")}),(0,m.jsx)("th",{children:e("fee")}),(0,m.jsx)("th",{children:e("progress")}),(0,m.jsx)("th",{children:e("time")})]})}),(0,m.jsx)("tbody",{children:0===s.length?(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:"7",className:"text-center",children:e("no_gains_record")})}):s.map(e=>(0,m.jsxs)("tr",{children:[(0,m.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,m.jsx)("td",{children:e.order_id?e.order_id.substring(0,8)+"...":"N/A"}),(0,m.jsx)("td",{children:e.share_amount}),(0,m.jsx)("td",{children:e.reward_amount}),(0,m.jsx)("td",{children:e.fee_amount}),(0,m.jsx)("td",{children:(0,m.jsxs)(l.A,{bg:1===e.progress?"success":"info",children:[100*e.progress,"%"]})}),(0,m.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id))})]})})})})})]})}},4063:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),o=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:c,className:l,as:i="span",...f}=e;const m=(0,n.oU)(r,"badge");return(0,o.jsx)(i,{ref:s,...f,className:t()(l,m,d&&"rounded-pill",c&&`text-${c}`,a&&`bg-${a}`)})});c.displayName="Badge";const l=c},4196:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),o=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:c,borderless:l,hover:i,size:f,variant:m,responsive:x,...h}=e;const u=(0,n.oU)(r,"table"),b=t()(a,u,m&&`${u}-${m}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,c&&`${u}-bordered`,l&&`${u}-borderless`,i&&`${u}-hover`),j=(0,o.jsx)("table",{...h,className:b,ref:s});if(x){let e=`${u}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,o.jsx)("div",{className:e,children:j})}return j});c.displayName="Table";const l=c},8602:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),o=r(579);const c=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:c,spans:l}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,n.oU)(r,"col");const o=(0,n.gy)(),c=(0,n.Jm)(),l=[],i=[];return o.forEach(e=>{const s=d[e];let a,t,n;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const o=e!==c?`-${e}`:"";a&&l.push(!0===a?`${r}${o}`:`${r}${o}-${a}`),null!=n&&i.push(`order${o}-${n}`),null!=t&&i.push(`offset${o}-${t}`)}),[{...d,className:t()(a,...l,...i)},{as:s,bsPrefix:r,spans:l}]}(e);return(0,o.jsx)(d,{...a,ref:s,className:t()(r,!l.length&&c)})});c.displayName="Col";const l=c},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),o=r(579);const c=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-body"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const l=c,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-footer"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});i.displayName="CardFooter";const f=i;var m=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...l}=e;const i=(0,n.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,o.jsx)(m.A.Provider,{value:f,children:(0,o.jsx)(c,{ref:s,...l,className:t()(a,i)})})});x.displayName="CardHeader";const h=x,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:c="img",...l}=e;const i=(0,n.oU)(r,"card-img");return(0,o.jsx)(c,{ref:s,className:t()(d?`${i}-${d}`:i,a),...l})});u.displayName="CardImg";const b=u,j=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});j.displayName="CardImgOverlay";const N=j,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...c}=e;return a=(0,n.oU)(a,"card-link"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});p.displayName="CardLink";const g=p;var $=r(4488);const y=(0,$.A)("h6"),v=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=y,...c}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});v.displayName="CardSubtitle";const w=v,_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...c}=e;return a=(0,n.oU)(a,"card-text"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});_.displayName="CardText";const P=_,A=(0,$.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=A,...c}=e;return a=(0,n.oU)(a,"card-title"),(0,o.jsx)(d,{ref:s,className:t()(r,a),...c})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:c,border:i,body:f=!1,children:m,as:x="div",...h}=e;const u=(0,n.oU)(r,"card");return(0,o.jsx)(x,{ref:s,...h,className:t()(a,u,d&&`bg-${d}`,c&&`text-${c}`,i&&`border-${i}`),children:f?(0,o.jsx)(l,{children:m}):m})});C.displayName="Card";const k=Object.assign(C,{Img:b,Title:U,Subtitle:w,Body:l,Link:g,Text:P,Header:h,Footer:f,ImgOverlay:N})}}]);
//# sourceMappingURL=285.419abf5e.chunk.js.map