import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const RouterDebug = () => {
    const location = useLocation();
    const navigate = useNavigate();

    const handleTestNavigation = () => {
        console.log('Current location:', location);
        console.log('Attempting to navigate to /wallet');
        navigate('/wallet');
    };

    return (
        <div style={{ 
            position: 'fixed', 
            top: '10px', 
            right: '10px', 
            background: 'rgba(0,0,0,0.8)', 
            color: 'white', 
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            zIndex: 9999
        }}>
            <div>Current Path: {location.pathname}</div>
            <div>Hash: {location.hash}</div>
            <div>Search: {location.search}</div>
            <button onClick={handleTestNavigation} style={{ marginTop: '5px' }}>
                Test Navigate to /wallet
            </button>
        </div>
    );
};

export default RouterDebug;
