{"version": 3, "file": "static/js/53.818c7207.chunk.js", "mappings": "uKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,sFCjCA,MAAMC,EAAqBzB,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTsB,EAAO,SACPC,EAAQ,WACRC,EAAU,MACVC,EAAK,KACLC,EAAI,QACJC,EAAO,WACPC,KACGzB,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmBuB,GAAW,GAAGvB,KAAqBuB,IAAWD,GAAQ,GAAGtB,KAAqBsB,IAAQJ,GAAW,GAAGlB,KAAwC,kBAAZkB,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGnB,aAA8BoB,GAAc,GAAGpB,eAAgCqB,GAAS,GAAGrB,WACxVyB,GAAqBX,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAI8B,EAAY,CACd,IAAIE,EAAkB,GAAG1B,eAIzB,MAH0B,kBAAfwB,IACTE,EAAkB,GAAGA,KAAmBF,MAEtBV,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAW8B,EACXC,SAAUF,GAEd,CACA,OAAOA,IAETR,EAAMD,YAAc,QACpB,S,sJChCA,MAoHA,EApHsBY,KAClB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,KACpCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KAE7CK,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfL,GAAW,GACX,MAAQO,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAR,GAAW,GAKf,MAAQO,KAAMI,EAAUC,MAAOC,SAAoBR,EAC9CS,KAAK,SACLC,OAAO,eACPC,GAAG,KAAMR,EAAKS,IACdC,SAEDL,EACAM,QAAQP,MAAM,8BAA+BC,GACtCF,GACPT,EAAcS,EAASS,aAI3B,MAAM,KAAEb,EAAI,MAAEK,SAAgBP,EACzBS,KAAK,SACLC,OAAO,yBACPC,GAAG,cAAeR,EAAKS,IACvBI,MAAM,aAAc,CAAEC,WAAW,IAElCV,EACAO,QAAQP,MAAM,4BAA6BA,GAE3Cf,EAAaU,GAEjBP,GAAW,IAGfuB,IACD,IAOH,OAAIxB,GACOpB,EAAAA,EAAAA,KAAA,OAAAa,SAAME,EAAE,4BAIf8B,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAjC,SAAA,EACNb,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM+B,SAAEE,EAAE,yBACxBf,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAACK,UAAU,OAAM+B,UACjBb,EAAAA,EAAAA,KAAC+C,EAAAA,EAAG,CAAAlC,UACAb,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAAnC,UACDgC,EAAAA,EAAAA,MAACG,EAAAA,EAAKC,KAAI,CAAApC,SAAA,EACNb,EAAAA,EAAAA,KAACgD,EAAAA,EAAKE,MAAK,CAAArC,SAAEE,EAAE,qBACff,EAAAA,EAAAA,KAAA,KAAGlB,UAAU,OAAM+B,UAACb,EAAAA,EAAAA,KAAA,UAAAa,SAASS,GAAc,UAC1CA,IACGtB,EAAAA,EAAAA,KAACmD,EAAAA,EAAM,CAAC1C,QAAQ,UAAU2C,QAnB9BC,KACpBC,UAAUC,UAAUC,UAAUlC,GAC9BmC,MAAM1C,EAAE,wBAiBmEF,SAC9CE,EAAE,uBAGXf,EAAAA,EAAAA,KAAA,KAAGlB,UAAU,kBAAiB+B,SAAEE,EAAE,wCAMlDf,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAoC,UACAb,EAAAA,EAAAA,KAAC+C,EAAAA,EAAG,CAAAlC,UACAb,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAAnC,UACDgC,EAAAA,EAAAA,MAACG,EAAAA,EAAKC,KAAI,CAAApC,SAAA,EACNb,EAAAA,EAAAA,KAACgD,EAAAA,EAAKE,MAAK,CAAArC,SAAEE,EAAE,2BACf8B,EAAAA,EAAAA,MAAC1C,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACE,OAAK,EAACG,YAAU,EAAAG,SAAA,EACpCb,EAAAA,EAAAA,KAAA,SAAAa,UACIgC,EAAAA,EAAAA,MAAA,MAAAhC,SAAA,EACIb,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,cACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,YACPf,EAAAA,EAAAA,KAAA,MAAAa,SAAKE,EAAE,6BAGff,EAAAA,EAAAA,KAAA,SAAAa,SAC0B,IAArBI,EAAUyC,QACP1D,EAAAA,EAAAA,KAAA,MAAAa,UACIb,EAAAA,EAAAA,KAAA,MAAI2D,QAAQ,IAAI7E,UAAU,cAAa+B,SAAEE,EAAE,4BAG/CE,EAAU2C,IAAIhF,IACViE,EAAAA,EAAAA,MAAA,MAAAhC,SAAA,EACIgC,EAAAA,EAAAA,MAAA,MAAAhC,SAAA,CAAKjC,EAAI0D,GAAGuB,UAAU,EAAG,GAAG,UAC5B7D,EAAAA,EAAAA,KAAA,MAAAa,SAAKjC,EAAIkF,SACT9D,EAAAA,EAAAA,KAAA,MAAAa,SAAK,IAAIkD,KAAKnF,EAAIoF,YAAYC,qBAHzBrF,EAAI0D,sB,sFC3DzD,MAAMS,EAAmBrE,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACGoF,IAEHnF,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACRsF,IAjDG,SAAexF,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChB4E,EAAQ,GACR1E,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIyE,EACAC,EACA3B,SAHGzD,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCwE,OACAC,SACA3B,SACE9C,GAEJwE,EAAOxE,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDyE,GAAMD,EAAMpE,MAAc,IAATqE,EAAgB,GAAGvF,IAAWiB,IAAU,GAAGjB,IAAWiB,KAASsE,KACvE,MAAT1B,GAAejD,EAAQM,KAAK,QAAQD,KAAS4C,KACnC,MAAV2B,GAAgB5E,EAAQM,KAAK,SAASD,KAASuE,OAE9C,CAAC,IACHpF,EACHH,UAAWmB,IAAWnB,KAAcqF,KAAU1E,IAC7C,CACDV,KACAF,WACAsF,SAEJ,CAWOG,CAAOrF,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/BkF,EACHtF,IAAKA,EACLE,UAAWmB,IAAWnB,GAAYqF,EAAMT,QAAU7E,OAGtDkE,EAAI7C,YAAc,MAClB,S,sFC1DA,MAAMqE,EAAwB7F,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPsF,EAASrE,YAAc,WACvB,UCdMsE,EAA0B9F,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPuF,EAAWtE,YAAc,aACzB,U,cCZA,MAAMuE,EAA0B/F,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,eACtC8F,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoB1E,EAAAA,EAAAA,KAAK8E,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACP9D,UAAuBb,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW4F,SAIvCD,EAAWvE,YAAc,aACzB,UCvBM+E,EAAuBvG,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACT2B,EACA1B,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWQ,EAAU,GAAGiE,KAAUjE,IAAYiE,EAAQ5F,MAC9DG,MAGPgG,EAAQ/E,YAAc,UACtB,UCjBMgF,EAA8BxG,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPiG,EAAehF,YAAc,iBAC7B,UCdMiF,EAAwBzG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPkG,EAASjF,YAAc,WACvB,U,cCbA,MAAMkF,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4B5G,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAYoG,KACbnG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPqG,EAAapF,YAAc,eAC3B,UChBMqF,EAAwB7G,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPsG,EAASrF,YAAc,WACvB,UCbMsF,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyB/G,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYwG,KACbvG,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPwG,EAAUvF,YAAc,YACxB,UCPM8C,EAAoBtE,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACT4G,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZhF,EAEA9B,GAAIC,EAAY,SACbC,GACJN,EACC,MAAM+F,GAASvF,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAW4F,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvG/E,SAAUgF,GAAoB7F,EAAAA,EAAAA,KAAKuE,EAAU,CAC3C1D,SAAUA,IACPA,MAGTmC,EAAK9C,YAAc,OACnB,QAAe4F,OAAOC,OAAO/C,EAAM,CACjCgD,IAAKf,EACL/B,MAAOuC,EACPQ,SAAUX,EACVrC,KAAMsB,EACN2B,KAAMf,EACNgB,KAAMZ,EACNa,OAAQ3B,EACR4B,OAAQ7B,EACR8B,WAAYpB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/Table.js", "pages/customer/RecommendPage.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst RecommendPage = () => {\n    const { t } = useTranslation();\n    const [referrals, setReferrals] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [inviteCode, setInviteCode] = useState('');\n\n    useEffect(() => {\n        const fetchReferrals = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch current user's invite code\n            const { data: userData, error: userError } = await supabase\n                .from('users')\n                .select('invite_code')\n                .eq('id', user.id)\n                .single();\n\n            if (userError) {\n                console.error('Error fetching invite code:', userError);\n            } else if (userData) {\n                setInviteCode(userData.invite_code);\n            }\n\n            // Fetch users referred by current user\n            const { data, error } = await supabase\n                .from('users')\n                .select('id, email, created_at')\n                .eq('referred_by', user.id)\n                .order('created_at', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching referrals:', error);\n            } else {\n                setReferrals(data);\n            }\n            setLoading(false);\n        };\n\n        fetchReferrals();\n    }, []);\n\n    const copyToClipboard = () => {\n        navigator.clipboard.writeText(inviteCode);\n        alert(t('invite_code_copied'));\n    };\n\n    if (loading) {\n        return <div>{t('loading_referral_data')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_recommendations')}</h2>\n            <Row className=\"mb-4\">\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('my_invite_code')}</Card.Title>\n                            <p className=\"lead\"><strong>{inviteCode || 'N/A'}</strong></p>\n                            {inviteCode && (\n                                <Button variant=\"primary\" onClick={copyToClipboard}>\n                                    {t('copy_invite_code')}\n                                </Button>\n                            )}\n                            <p className=\"mt-3 text-muted\">{t('share_invite_description')}</p>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('my_subordinate_users')}</Card.Title>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('user_id')}</th>\n                                        <th>{t('email')}</th>\n                                        <th>{t('registration_time')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {referrals.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"3\" className=\"text-center\">{t('no_subordinate_users')}</td>\n                                        </tr>\n                                    ) : (\n                                        referrals.map(ref => (\n                                            <tr key={ref.id}>\n                                                <td>{ref.id.substring(0, 8)}...</td>\n                                                <td>{ref.email}</td>\n                                                <td>{new Date(ref.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default RecommendPage;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "Table", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "table", "responsiveClass", "children", "RecommendPage", "t", "useTranslation", "referrals", "setReferrals", "useState", "loading", "setLoading", "inviteCode", "setInviteCode", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "userData", "error", "userError", "from", "select", "eq", "id", "single", "console", "invite_code", "order", "ascending", "fetchReferrals", "_jsxs", "Container", "Col", "Card", "Body", "Title", "<PERSON><PERSON>", "onClick", "copyToClipboard", "navigator", "clipboard", "writeText", "alert", "length", "colSpan", "map", "substring", "email", "Date", "created_at", "toLocaleString", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}