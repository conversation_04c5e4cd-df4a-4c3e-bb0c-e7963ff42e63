{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Button}from'react-bootstrap';import{useTranslation}from'react-i18next';import{Link}from'react-router-dom';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MakerDashboard=()=>{const{t}=useTranslation();const[makerProfile,setMakerProfile]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchMakerData=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch maker profile\nconst{data:profileData,error:profileError}=await supabase.from('maker_profiles').select('*').eq('user_id',user.id).single();if(profileError){console.error('Error fetching maker profile:',profileError);}else{setMakerProfile(profileData);}setLoading(false);};fetchMakerData();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_maker_dashboard')});}if(!makerProfile){return/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-warning\",children:t('not_maker')});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"h2\",{children:t('maker_dashboard')})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-primary text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('domain')}),/*#__PURE__*/_jsx(\"h3\",{children:makerProfile.domain||'N/A'})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-success text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('support_email')}),/*#__PURE__*/_jsx(\"h3\",{children:makerProfile.support_email||'N/A'})]})})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Card,{className:\"bg-info text-white mb-3\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('sms_signature')}),/*#__PURE__*/_jsx(\"h3\",{children:makerProfile.sms_signature||'N/A'})]})})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mt-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('product_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('manage_your_products')}),/*#__PURE__*/_jsx(Button,{as:Link,to:\"/maker/products\",variant:\"primary\",children:t('enter_product_list')})]})})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('order_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('all_orders')}),/*#__PURE__*/_jsx(Button,{as:Link,to:\"/maker/orders\",variant:\"success\",children:t('enter_order_list')})]})})})]})]});};export default MakerDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "useTranslation", "Link", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "MakerDashboard", "t", "makerProfile", "setMakerProfile", "loading", "setLoading", "fetchMakerData", "supabase", "data", "user", "auth", "getUser", "profileData", "error", "profileError", "from", "select", "eq", "id", "single", "console", "children", "className", "fluid", "md", "Body", "Title", "domain", "support_email", "sms_signature", "as", "to", "variant"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport { getSupabase } from '../../supabaseClient';\n\nconst MakerDashboard = () => {\n    const { t } = useTranslation();\n    const [makerProfile, setMakerProfile] = useState(null);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchMakerData = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch maker profile\n            const { data: profileData, error: profileError } = await supabase\n                .from('maker_profiles')\n                .select('*')\n                .eq('user_id', user.id)\n                .single();\n\n            if (profileError) {\n                console.error('Error fetching maker profile:', profileError);\n            } else {\n                setMakerProfile(profileData);\n            }\n            setLoading(false);\n        };\n\n        fetchMakerData();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_maker_dashboard')}</div>;\n    }\n\n    if (!makerProfile) {\n        return <div className=\"alert alert-warning\">{t('not_maker')}</div>;\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('maker_dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={4}>\n                    <Card className=\"bg-primary text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('domain')}</Card.Title>\n                            <h3>{makerProfile.domain || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-success text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('support_email')}</Card.Title>\n                            <h3>{makerProfile.support_email || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={4}>\n                    <Card className=\"bg-info text-white mb-3\">\n                        <Card.Body>\n                            <Card.Title>{t('sms_signature')}</Card.Title>\n                            <h3>{makerProfile.sms_signature || 'N/A'}</h3>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n            <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('product_management')}</h4>\n                            <p>{t('manage_your_products')}</p>\n                            <Button as={Link} to=\"/maker/products\" variant=\"primary\">{t('enter_product_list')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('order_management')}</h4>\n                            <p>{t('all_orders')}</p>\n                            <Button as={Link} to=\"/maker/orders\" variant=\"success\">{t('enter_order_list')}</Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MakerDashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,KAAQ,iBAAiB,CACnE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACS,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACY,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,WAAW,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAC5DQ,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAER,IAAI,CAACS,EAAE,CAAC,CACtBC,MAAM,CAAC,CAAC,CAEb,GAAIL,YAAY,CAAE,CACdM,OAAO,CAACP,KAAK,CAAC,+BAA+B,CAAEC,YAAY,CAAC,CAChE,CAAC,IAAM,CACHX,eAAe,CAACS,WAAW,CAAC,CAChC,CACAP,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAwB,QAAA,CAAMpB,CAAC,CAAC,yBAAyB,CAAC,CAAM,CAAC,CACpD,CAEA,GAAI,CAACC,YAAY,CAAE,CACf,mBAAOL,IAAA,QAAKyB,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEpB,CAAC,CAAC,WAAW,CAAC,CAAM,CAAC,CACtE,CAEA,mBACIF,KAAA,CAACX,SAAS,EAACmC,KAAK,MAAAF,QAAA,eACZxB,IAAA,CAACR,GAAG,EAACiC,SAAS,CAAC,MAAM,CAAAD,QAAA,cACjBxB,IAAA,CAACP,GAAG,EAAA+B,QAAA,cACAxB,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,CAC9B,CAAC,CACL,CAAC,cAENF,KAAA,CAACV,GAAG,EAAAgC,QAAA,eACAxB,IAAA,CAACP,GAAG,EAACkC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACPxB,IAAA,CAACN,IAAI,EAAC+B,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCtB,KAAA,CAACR,IAAI,CAACkC,IAAI,EAAAJ,QAAA,eACNxB,IAAA,CAACN,IAAI,CAACmC,KAAK,EAAAL,QAAA,CAAEpB,CAAC,CAAC,QAAQ,CAAC,CAAa,CAAC,cACtCJ,IAAA,OAAAwB,QAAA,CAAKnB,YAAY,CAACyB,MAAM,EAAI,KAAK,CAAK,CAAC,EAChC,CAAC,CACV,CAAC,CACN,CAAC,cACN9B,IAAA,CAACP,GAAG,EAACkC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACPxB,IAAA,CAACN,IAAI,EAAC+B,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxCtB,KAAA,CAACR,IAAI,CAACkC,IAAI,EAAAJ,QAAA,eACNxB,IAAA,CAACN,IAAI,CAACmC,KAAK,EAAAL,QAAA,CAAEpB,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CJ,IAAA,OAAAwB,QAAA,CAAKnB,YAAY,CAAC0B,aAAa,EAAI,KAAK,CAAK,CAAC,EACvC,CAAC,CACV,CAAC,CACN,CAAC,cACN/B,IAAA,CAACP,GAAG,EAACkC,EAAE,CAAE,CAAE,CAAAH,QAAA,cACPxB,IAAA,CAACN,IAAI,EAAC+B,SAAS,CAAC,yBAAyB,CAAAD,QAAA,cACrCtB,KAAA,CAACR,IAAI,CAACkC,IAAI,EAAAJ,QAAA,eACNxB,IAAA,CAACN,IAAI,CAACmC,KAAK,EAAAL,QAAA,CAAEpB,CAAC,CAAC,eAAe,CAAC,CAAa,CAAC,cAC7CJ,IAAA,OAAAwB,QAAA,CAAKnB,YAAY,CAAC2B,aAAa,EAAI,KAAK,CAAK,CAAC,EACvC,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,cAEN9B,KAAA,CAACV,GAAG,EAACiC,SAAS,CAAC,MAAM,CAAAD,QAAA,eACjBxB,IAAA,CAACP,GAAG,EAACkC,EAAE,CAAE,CAAE,CAACF,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC/BxB,IAAA,CAACN,IAAI,EAAA8B,QAAA,cACDtB,KAAA,CAACR,IAAI,CAACkC,IAAI,EAAAJ,QAAA,eACNxB,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,oBAAoB,CAAC,CAAK,CAAC,cAClCJ,IAAA,MAAAwB,QAAA,CAAIpB,CAAC,CAAC,sBAAsB,CAAC,CAAI,CAAC,cAClCJ,IAAA,CAACL,MAAM,EAACsC,EAAE,CAAEpC,IAAK,CAACqC,EAAE,CAAC,iBAAiB,CAACC,OAAO,CAAC,SAAS,CAAAX,QAAA,CAAEpB,CAAC,CAAC,oBAAoB,CAAC,CAAS,CAAC,EACpF,CAAC,CACV,CAAC,CACN,CAAC,cACNJ,IAAA,CAACP,GAAG,EAACkC,EAAE,CAAE,CAAE,CAACF,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC/BxB,IAAA,CAACN,IAAI,EAAA8B,QAAA,cACDtB,KAAA,CAACR,IAAI,CAACkC,IAAI,EAAAJ,QAAA,eACNxB,IAAA,OAAAwB,QAAA,CAAKpB,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,cAChCJ,IAAA,MAAAwB,QAAA,CAAIpB,CAAC,CAAC,YAAY,CAAC,CAAI,CAAC,cACxBJ,IAAA,CAACL,MAAM,EAACsC,EAAE,CAAEpC,IAAK,CAACqC,EAAE,CAAC,eAAe,CAACC,OAAO,CAAC,SAAS,CAAAX,QAAA,CAAEpB,CAAC,CAAC,kBAAkB,CAAC,CAAS,CAAC,EAChF,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}