
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const RecommendPage = () => {
    const { t } = useTranslation();
    const [referrals, setReferrals] = useState([]);
    const [loading, setLoading] = useState(true);
    const [inviteCode, setInviteCode] = useState('');

    useEffect(() => {
        const fetchReferrals = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch current user's invite code
            const { data: userData, error: userError } = await supabase
                .from('users')
                .select('invite_code')
                .eq('id', user.id)
                .single();

            if (userError) {
                console.error('Error fetching invite code:', userError);
            } else if (userData) {
                setInviteCode(userData.invite_code);
            }

            // Fetch users referred by current user
            const { data, error } = await supabase
                .from('users')
                .select('id, email, created_at')
                .eq('referred_by', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching referrals:', error);
            } else {
                setReferrals(data);
            }
            setLoading(false);
        };

        fetchReferrals();
    }, []);

    const copyToClipboard = () => {
        navigator.clipboard.writeText(inviteCode);
        alert(t('invite_code_copied'));
    };

    if (loading) {
        return <div>{t('loading_referral_data')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('my_recommendations')}</h2>
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('my_invite_code')}</Card.Title>
                            <p className="lead"><strong>{inviteCode || 'N/A'}</strong></p>
                            {inviteCode && (
                                <Button variant="primary" onClick={copyToClipboard}>
                                    {t('copy_invite_code')}
                                </Button>
                            )}
                            <p className="mt-3 text-muted">{t('share_invite_description')}</p>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('my_subordinate_users')}</Card.Title>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('user_id')}</th>
                                        <th>{t('email')}</th>
                                        <th>{t('registration_time')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {referrals.length === 0 ? (
                                        <tr>
                                            <td colSpan="3" className="text-center">{t('no_subordinate_users')}</td>
                                        </tr>
                                    ) : (
                                        referrals.map(ref => (
                                            <tr key={ref.id}>
                                                <td>{ref.id.substring(0, 8)}...</td>
                                                <td>{ref.email}</td>
                                                <td>{new Date(ref.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default RecommendPage;
