"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[592],{592:(e,s,a)=>{a.r(s),a.d(s,{default:()=>w});var r=a(5043),t=a(3519),n=a(1072),c=a(8602),o=a(8628),l=a(8139),d=a.n(l),i=(a(6440),a(1969)),f=a(927),m=a(7852),x=a(6618),h=a(2644),u=a(5901),N=a(579);const b=r.forwardRef((e,s)=>{let{bsPrefix:a,active:r,disabled:t,eventKey:n,className:c,variant:o,action:l,as:i,...f}=e;a=(0,m.oU)(a,"list-group-item");const[b,j]=(0,h.M)({key:(0,u.u)(n,f.href),active:r,...f}),p=(0,x.A)(e=>{if(t)return e.preventDefault(),void e.stopPropagation();b.onClick(e)});t&&void 0===f.tabIndex&&(f.tabIndex=-1,f["aria-disabled"]=!0);const v=i||(l?f.href?"a":"button":"div");return(0,N.jsx)(v,{ref:s,...f,...b,onClick:p,className:d()(c,a,j.isActive&&"active",t&&"disabled",o&&`${a}-${o}`,l&&`${a}-action`)})});b.displayName="ListGroupItem";const j=b,p=r.forwardRef((e,s)=>{const{className:a,bsPrefix:r,variant:t,horizontal:n,numbered:c,as:o="div",...l}=(0,i.Zw)(e,{activeKey:"onSelect"}),x=(0,m.oU)(r,"list-group");let h;return n&&(h=!0===n?"horizontal":`horizontal-${n}`),(0,N.jsx)(f.A,{ref:s,...l,as:o,className:d()(a,x,t&&`${x}-${t}`,h&&`${x}-${h}`,c&&`${x}-numbered`)})});p.displayName="ListGroup";const v=Object.assign(p,{Item:j});var y=a(4117),$=a(1283);const w=()=>{const{t:e}=(0,y.Bd)();return(0,N.jsxs)(t.A,{children:[(0,N.jsx)("h2",{className:"mb-4",children:e("my_account")}),(0,N.jsxs)(n.A,{children:[(0,N.jsx)(c.A,{md:6,children:(0,N.jsxs)(o.A,{children:[(0,N.jsx)(o.A.Header,{children:e("personal_info")}),(0,N.jsxs)(v,{variant:"flush",children:[(0,N.jsx)(v.Item,{children:(0,N.jsx)($.N_,{to:"/my/kyc",children:e("kyc_verification")})}),(0,N.jsx)(v.Item,{children:(0,N.jsx)($.N_,{to:"/my/change-login-pass",children:e("change_login_password")})}),(0,N.jsx)(v.Item,{children:(0,N.jsx)($.N_,{to:"/my/change-withdraw-pass",children:e("change_withdraw_password")})})]})]})}),(0,N.jsx)(c.A,{md:6,children:(0,N.jsxs)(o.A,{children:[(0,N.jsx)(o.A.Header,{children:e("my_recommendations")}),(0,N.jsx)(v,{variant:"flush",children:(0,N.jsx)(v.Item,{children:(0,N.jsx)($.N_,{to:"/my/recommend",children:e("view_my_recommendations")})})})]})})]})]})}},1072:(e,s,a)=>{a.d(s,{A:()=>d});var r=a(8139),t=a.n(r),n=a(5043),c=a(7852),o=a(579);const l=n.forwardRef((e,s)=>{let{bsPrefix:a,className:r,as:n="div",...l}=e;const d=(0,c.oU)(a,"row"),i=(0,c.gy)(),f=(0,c.Jm)(),m=`${d}-cols`,x=[];return i.forEach(e=>{const s=l[e];let a;delete l[e],null!=s&&"object"===typeof s?({cols:a}=s):a=s;const r=e!==f?`-${e}`:"";null!=a&&x.push(`${m}${r}-${a}`)}),(0,o.jsx)(n,{ref:s,...l,className:t()(r,d,...x)})});l.displayName="Row";const d=l},8602:(e,s,a)=>{a.d(s,{A:()=>d});var r=a(8139),t=a.n(r),n=a(5043),c=a(7852),o=a(579);const l=n.forwardRef((e,s)=>{const[{className:a,...r},{as:n="div",bsPrefix:l,spans:d}]=function(e){let{as:s,bsPrefix:a,className:r,...n}=e;a=(0,c.oU)(a,"col");const o=(0,c.gy)(),l=(0,c.Jm)(),d=[],i=[];return o.forEach(e=>{const s=n[e];let r,t,c;delete n[e],"object"===typeof s&&null!=s?({span:r,offset:t,order:c}=s):r=s;const o=e!==l?`-${e}`:"";r&&d.push(!0===r?`${a}${o}`:`${a}${o}-${r}`),null!=c&&i.push(`order${o}-${c}`),null!=t&&i.push(`offset${o}-${t}`)}),[{...n,className:t()(r,...d,...i)},{as:s,bsPrefix:a,spans:d}]}(e);return(0,o.jsx)(n,{...r,ref:s,className:t()(a,!d.length&&l)})});l.displayName="Col";const d=l},8628:(e,s,a)=>{a.d(s,{A:()=>k});var r=a(8139),t=a.n(r),n=a(5043),c=a(7852),o=a(579);const l=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n="div",...l}=e;return r=(0,c.oU)(r,"card-body"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});l.displayName="CardBody";const d=l,i=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n="div",...l}=e;return r=(0,c.oU)(r,"card-footer"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});i.displayName="CardFooter";const f=i;var m=a(1778);const x=n.forwardRef((e,s)=>{let{bsPrefix:a,className:r,as:l="div",...d}=e;const i=(0,c.oU)(a,"card-header"),f=(0,n.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,o.jsx)(m.A.Provider,{value:f,children:(0,o.jsx)(l,{ref:s,...d,className:t()(r,i)})})});x.displayName="CardHeader";const h=x,u=n.forwardRef((e,s)=>{let{bsPrefix:a,className:r,variant:n,as:l="img",...d}=e;const i=(0,c.oU)(a,"card-img");return(0,o.jsx)(l,{ref:s,className:t()(n?`${i}-${n}`:i,r),...d})});u.displayName="CardImg";const N=u,b=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n="div",...l}=e;return r=(0,c.oU)(r,"card-img-overlay"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});b.displayName="CardImgOverlay";const j=b,p=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n="a",...l}=e;return r=(0,c.oU)(r,"card-link"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});p.displayName="CardLink";const v=p;var y=a(4488);const $=(0,y.A)("h6"),w=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n=$,...l}=e;return r=(0,c.oU)(r,"card-subtitle"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});w.displayName="CardSubtitle";const g=w,P=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n="p",...l}=e;return r=(0,c.oU)(r,"card-text"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});P.displayName="CardText";const A=P,C=(0,y.A)("h5"),R=n.forwardRef((e,s)=>{let{className:a,bsPrefix:r,as:n=C,...l}=e;return r=(0,c.oU)(r,"card-title"),(0,o.jsx)(n,{ref:s,className:t()(a,r),...l})});R.displayName="CardTitle";const U=R,_=n.forwardRef((e,s)=>{let{bsPrefix:a,className:r,bg:n,text:l,border:i,body:f=!1,children:m,as:x="div",...h}=e;const u=(0,c.oU)(a,"card");return(0,o.jsx)(x,{ref:s,...h,className:t()(r,u,n&&`bg-${n}`,l&&`text-${l}`,i&&`border-${i}`),children:f?(0,o.jsx)(d,{children:m}):m})});_.displayName="Card";const k=Object.assign(_,{Img:N,Title:U,Subtitle:g,Body:d,Link:v,Text:A,Header:h,Footer:f,ImgOverlay:j})}}]);
//# sourceMappingURL=592.24acd47e.chunk.js.map