{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'bootstrap/dist/css/bootstrap.min.css';import'./i18n';// Initialize i18n\nimport App from'./App';import'./index.css';// Set the public path for dynamic imports\nimport{jsx as _jsx}from\"react/jsx-runtime\";if(window.wpData&&window.wpData.pluginUrl){__webpack_public_path__=window.wpData.pluginUrl;}const root=ReactDOM.createRoot(document.getElementById('fil-platform-root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsx", "_jsx", "window", "wpData", "pluginUrl", "__webpack_public_path__", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/index.js"], "sourcesContent": ["\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './i18n'; // Initialize i18n\nimport App from './App';\nimport './index.css';\n\n// Set the public path for dynamic imports\nif (window.wpData && window.wpData.pluginUrl) {\n  __webpack_public_path__ = window.wpData.pluginUrl;\n}\n\nconst root = ReactDOM.createRoot(document.getElementById('fil-platform-root'));\n\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,sCAAsC,CAC7C,MAAO,QAAQ,CAAE;AACjB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,aAAa,CAEpB;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,GAAIC,MAAM,CAACC,MAAM,EAAID,MAAM,CAACC,MAAM,CAACC,SAAS,CAAE,CAC5CC,uBAAuB,CAAGH,MAAM,CAACC,MAAM,CAACC,SAAS,CACnD,CAEA,KAAM,CAAAE,IAAI,CAAGR,QAAQ,CAACS,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAE9EH,IAAI,CAACI,MAAM,cACTT,IAAA,CAACJ,KAAK,CAACc,UAAU,EAAAC,QAAA,cACfX,IAAA,CAACF,GAAG,GAAE,CAAC,CACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}