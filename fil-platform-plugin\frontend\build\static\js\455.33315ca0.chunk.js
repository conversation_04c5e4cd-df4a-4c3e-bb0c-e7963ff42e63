"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[455],{1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...l}=e;const o=(0,n.oU)(r,"row"),i=(0,n.gy)(),f=(0,n.Jm)(),x=`${o}-cols`,h=[];return i.forEach(e=>{const s=l[e];let r;delete l[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&h.push(`${x}${a}-${r}`)}),(0,c.jsx)(d,{ref:s,...l,className:t()(a,o,...h)})});l.displayName="Row";const o=l},4063:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:l,className:o,as:i="span",...f}=e;const x=(0,n.oU)(r,"badge");return(0,c.jsx)(i,{ref:s,...f,className:t()(o,x,d&&"rounded-pill",l&&`text-${l}`,a&&`bg-${a}`)})});l.displayName="Badge";const o=l},4196:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:l,borderless:o,hover:i,size:f,variant:x,responsive:h,...m}=e;const u=(0,n.oU)(r,"table"),j=t()(a,u,x&&`${u}-${x}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,l&&`${u}-bordered`,o&&`${u}-borderless`,i&&`${u}-hover`),p=(0,c.jsx)("table",{...m,className:j,ref:s});if(h){let e=`${u}-responsive`;return"string"===typeof h&&(e=`${e}-${h}`),(0,c.jsx)("div",{className:e,children:p})}return p});l.displayName="Table";const o=l},8455:(e,s,r)=>{r.r(s),r.d(s,{default:()=>h});var a=r(5043),t=r(3519),d=r(1072),n=r(8602),c=r(8628),l=r(4196),o=r(4063),i=r(4312),f=r(4117),x=r(579);const h=()=>{const{t:e}=(0,f.Bd)(),[s,r]=(0,a.useState)([]),[h,m]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;m(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void m(!1);const{data:a,error:t}=await e.from("orders").select("\n                    id,\n                    shares,\n                    storage_cost,\n                    pledge_cost,\n                    total_rate,\n                    start_at,\n                    end_at,\n                    review_status,\n                    created_at,\n                    products ( name )\n                ").eq("customer_id",s.id).order("created_at",{ascending:!1});t?console.error("Error fetching orders:",t):r(a),m(!1)})()},[]),h?(0,x.jsx)("div",{children:e("loading_orders")}):(0,x.jsxs)(t.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("orders")}),(0,x.jsx)(d.A,{children:(0,x.jsx)(n.A,{children:(0,x.jsx)(c.A,{children:(0,x.jsx)(c.A.Body,{children:(0,x.jsxs)(l.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("order_id")}),(0,x.jsx)("th",{children:e("product_name")}),(0,x.jsx)("th",{children:e("shares")}),(0,x.jsx)("th",{children:e("storage_cost")}),(0,x.jsx)("th",{children:e("pledge_cost")}),(0,x.jsx)("th",{children:e("total_rate")}),(0,x.jsx)("th",{children:e("start_date")}),(0,x.jsx)("th",{children:e("end_date")}),(0,x.jsx)("th",{children:e("status")}),(0,x.jsx)("th",{children:e("created_at")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"10",className:"text-center",children:e("no_orders")})}):s.map(e=>{var s;return(0,x.jsxs)("tr",{children:[(0,x.jsxs)("td",{children:[e.id.substring(0,8),"..."]}),(0,x.jsx)("td",{children:(null===(s=e.products)||void 0===s?void 0:s.name)||"N/A"}),(0,x.jsx)("td",{children:e.shares}),(0,x.jsx)("td",{children:e.storage_cost}),(0,x.jsx)("td",{children:e.pledge_cost}),(0,x.jsx)("td",{children:e.total_rate}),(0,x.jsx)("td",{children:new Date(e.start_at).toLocaleDateString()}),(0,x.jsx)("td",{children:new Date(e.end_at).toLocaleDateString()}),(0,x.jsx)("td",{children:(0,x.jsx)(o.A,{bg:"approved"===e.review_status?"success":"warning",children:e.review_status})}),(0,x.jsx)("td",{children:new Date(e.created_at).toLocaleString()})]},e.id)})})]})})})})})]})}},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:l,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,n.oU)(r,"col");const c=(0,n.gy)(),l=(0,n.Jm)(),o=[],i=[];return c.forEach(e=>{const s=d[e];let a,t,n;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const c=e!==l?`-${e}`:"";a&&o.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=n&&i.push(`order${c}-${n}`),null!=t&&i.push(`offset${c}-${t}`)}),[{...d,className:t()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,c.jsx)(d,{...a,ref:s,className:t()(r,!o.length&&l)})});l.displayName="Col";const o=l},8628:(e,s,r)=>{r.d(s,{A:()=>S});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),c=r(579);const l=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,n.oU)(a,"card-body"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});l.displayName="CardBody";const o=l,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,n.oU)(a,"card-footer"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});i.displayName="CardFooter";const f=i;var x=r(1778);const h=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...o}=e;const i=(0,n.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,c.jsx)(x.A.Provider,{value:f,children:(0,c.jsx)(l,{ref:s,...o,className:t()(a,i)})})});h.displayName="CardHeader";const m=h,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:l="img",...o}=e;const i=(0,n.oU)(r,"card-img");return(0,c.jsx)(l,{ref:s,className:t()(d?`${i}-${d}`:i,a),...o})});u.displayName="CardImg";const j=u,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});p.displayName="CardImgOverlay";const N=p,b=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...l}=e;return a=(0,n.oU)(a,"card-link"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});b.displayName="CardLink";const v=b;var g=r(4488);const $=(0,g.A)("h6"),y=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=$,...l}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});y.displayName="CardSubtitle";const w=y,_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...l}=e;return a=(0,n.oU)(a,"card-text"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});_.displayName="CardText";const P=_,A=(0,g.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=A,...l}=e;return a=(0,n.oU)(a,"card-title"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...l})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:l,border:i,body:f=!1,children:x,as:h="div",...m}=e;const u=(0,n.oU)(r,"card");return(0,c.jsx)(h,{ref:s,...m,className:t()(a,u,d&&`bg-${d}`,l&&`text-${l}`,i&&`border-${i}`),children:f?(0,c.jsx)(o,{children:x}):x})});C.displayName="Card";const S=Object.assign(C,{Img:j,Title:U,Subtitle:w,Body:o,Link:v,Text:P,Header:m,Footer:f,ImgOverlay:N})}}]);
//# sourceMappingURL=455.33315ca0.chunk.js.map