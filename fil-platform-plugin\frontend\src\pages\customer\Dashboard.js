import React from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Link } from 'react-router-dom';

// Mock data, we will fetch this from Supabase later
const chartData = [
  { name: '7/1', fil: 0.2, usd: 1.1 },
  { name: '7/2', fil: 0.25, usd: 1.3 },
  { name: '7/3', fil: 0.22, usd: 1.2 },
  { name: '7/4', fil: 0.3, usd: 1.5 },
  { name: '7/5', fil: 0.28, usd: 1.4 },
  { name: '7/6', fil: 0.35, usd: 1.8 },
  { name: '7/7', fil: 0.4, usd: 2.0 },
];

const StatCard = ({ title, value, subValue, variant }) => (
    <Card className={`bg-${variant} text-white mb-3`}>
        <Card.Body>
            <Card.Title>{title}</Card.Title>
            <h3>{value}</h3>
            <p>{subValue}</p>
        </Card.Body>
    </Card>
);

const CustomerDashboard = () => {
    const { t } = useTranslation();

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('dashboard')}</h2>
                </Col>
            </Row>

            <Row>
                <Col md={3}>
                    <StatCard title={t('total_earnings')} value="12,345.67 FIL" subValue="≈ $50,123.45 USD" variant="primary" />
                </Col>
                <Col md={3}>
                    <StatCard title={t('yesterday_earnings')} value="123.45 FIL" subValue="≈ $501.23 USD" variant="success" />
                </Col>
                <Col md={3}>
                    <StatCard title={t('available_balance')} value="1,234.56 FIL" subValue="≈ $5,012.34 USD" variant="info" />
                </Col>
                <Col md={3}>
                    <StatCard title={t('power_pledge')} value="10,000.00 FIL" subValue="≈ $40,500.00 USD" variant="warning" />
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('earnings_trend')}</Card.Title>
                            <ResponsiveContainer width="100%" height={400}>
                                <LineChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="name" />
                                    <YAxis yAxisId="left" label={{ value: t('fil'), angle: -90, position: 'insideLeft' }} />
                                    <YAxis yAxisId="right" orientation="right" label={{ value: t('usd'), angle: -90, position: 'insideRight' }} />
                                    <Tooltip formatter={(value, name, props) => [value, t(name)]} />
                                    <Legend />
                                    <Line yAxisId="left" type="monotone" dataKey="fil" stroke="#8884d8" name={t('FIL_earnings')} />
                                    <Line yAxisId="right" type="monotone" dataKey="usd" stroke="#82ca9d" name={t('USD_estimate')} />
                                </LineChart>
                            </ResponsiveContainer>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

             <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('wallet_management')}</h4>
                            <p>{t('manage_your_digital_assets')}</p>
                            <Button as={Link} to="/wallet" variant="primary">{t('enter_wallet')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('buy_power')}</h4>
                            <p>{t('view_and_purchase_new_power_products')}</p>
                            <Button as={Link} to="/products" variant="success">{t('browse_products')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

        </Container>
    );
};

export default CustomerDashboard;