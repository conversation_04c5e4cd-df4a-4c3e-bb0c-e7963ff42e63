/*! For license information please see 141.14649523.chunk.js.LICENSE.txt */
(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[141],{108:(e,t,r)=>{"use strict";r.d(t,{u:()=>d});var n=r(8387),i=r(5043),a=r(7820),o=r.n(a),c=r(6307),l=r(155);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){f(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var d=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:d=0,minHeight:h,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:w={}}=e,x=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>x.current);var[P,A]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),j=(0,i.useCallback)((e,t)=>{A(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;j(r,n),null===(t=O.current)||void 0===t||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=x.current.getBoundingClientRect();return j(r,n),t.observe(x.current),()=>{t.disconnect()}},[j,v]);var S=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=P;if(e<0||t<0)return null;(0,l.R)((0,c._3)(u)||(0,c._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,l.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,c._3)(u)?e:u,a=(0,c._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,l.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,d,h,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({height:"100%",width:"100%",maxHeight:a,maxWidth:n},e.props.style)}))},[r,y,f,p,h,d,P,u]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},w),{},{width:u,height:f,minWidth:d,minHeight:h,maxHeight:p}),ref:x},S)})},116:(e,t,r)=>{"use strict";r.d(t,{eC:()=>i,gY:()=>n,hX:()=>c,iO:()=>a,lZ:()=>o,pH:()=>l});var n=e=>e.rootProps.barCategoryGap,i=e=>e.rootProps.stackOffset,a=e=>e.options.chartName,o=e=>e.rootProps.syncId,c=e=>e.rootProps.syncMethod,l=e=>e.options.eventEmitter},138:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!==e&&void 0!==e&&e.reversed?[t[1],t[0]]:t}},155:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},165:(e,t,r)=>{"use strict";r.d(t,{IZ:()=>l,Kg:()=>o,lY:()=>u,yy:()=>d});r(5043);function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){a(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function a(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var o=Math.PI/180,c=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-o*n)*r,y:t+Math.sin(-o*n)*r}),u=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},s=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=(r-i)/o,u=Math.acos(l);return n>a&&(u=2*Math.PI-u),{radius:o,angle:c(u),angleInRadian:u}},f=(e,t)=>{var{startAngle:r,endAngle:n}=t,i=Math.floor(r/360),a=Math.floor(n/360);return e+360*Math.min(i,a)},d=(e,t)=>{var{x:r,y:n}=e,{radius:a,angle:o}=s({x:r,y:n},t),{innerRadius:c,outerRadius:l}=t;if(a<c||a>l)return null;if(0===a)return null;var u,{startAngle:d,endAngle:h}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-360*a,endAngle:r-360*a}})(t),p=o;if(d<=h){for(;p>h;)p-=360;for(;p<d;)p+=360;u=p>=d&&p<=h}else{for(;p>d;)p-=360;for(;p<h;)p+=360;u=p>=h&&p<=d}return u?i(i({},t),{},{radius:a,angle:f(p,t)}):null}},224:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(6307),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null===e||void 0===e||null===(r=e[i])||void 0===r?void 0:r.value:void 0}},240:(e,t,r)=>{"use strict";r.d(t,{J9:()=>y,aS:()=>h,y$:()=>p});var n=r(7770),i=r.n(n),a=r(5043),o=r(2086),c=r(6307),l=r(7287),u=e=>"string"===typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,d=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,c.uy)(e)||((0,o.isFragment)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,s=e,t};function h(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>u(e)):[u(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var p=e=>!e||"object"!==typeof e||!("clipDot"in e)||Boolean(e.clipDot),y=(e,t,r)=>{if(!e||"function"===typeof e||"boolean"===typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!==typeof n&&"function"!==typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;((e,t,r,n)=>{var i,a=null!==(i=n&&(null===l.VU||void 0===l.VU?void 0:l.VU[n]))&&void 0!==i?i:[];return t.startsWith("data-")||"function"!==typeof e&&(n&&a.includes(t)||l.QQ.includes(t))||r&&l.j2.includes(t)})(null===(a=n)||void 0===a?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},317:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(5043),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},356:(e,t,r)=>{e.exports=r(9645).last},425:(e,t,r)=>{"use strict";r.d(t,{l3:()=>g,m7:()=>m});var n=r(5043),i=r(787),a=r(116);var o=new(r(8499)),c="recharts.syncEvent.tooltip",l="recharts.syncEvent.brush",u=r(1603),s=r(2768),f=r(1428),d=r(2711);function h(e){return e.tooltip.syncInteraction}var p=r(8796),y=r(7773),v=()=>{};function g(){var e=(0,i.j)();(0,n.useEffect)(()=>{e((0,u.dl)())},[e]),function(){var e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)(),l=(0,i.G)(a.hX),u=(0,i.G)(d.R4),f=(0,p.WX)(),h=(0,p.sk)(),y=(0,i.G)(e=>e.rootProps.className);(0,n.useEffect)(()=>{if(null==e)return v;var n=(n,i,a)=>{if(t!==a&&e===n)if("index"!==l){if(null!=u){var o;if("function"===typeof l){var c={activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate},d=l(u,c);o=u[d]}else"value"===l&&(o=u.find(e=>String(e.value)===i.payload.label));var{coordinate:p}=i.payload;if(null!=o&&!1!==i.payload.active&&null!=p&&null!=h){var{x:y,y:v}=p,g=Math.min(y,h.x+h.width),m=Math.min(v,h.y+h.height),b={x:"horizontal"===f?o.coordinate:g,y:"horizontal"===f?m:o.coordinate},w=(0,s.E1)({active:i.payload.active,coordinate:b,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label});r(w)}else r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}))}}else r(i)};return o.on(c,n),()=>{o.off(c,n)}},[y,r,t,e,l,u,f,h])}(),function(){var e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)();(0,n.useEffect)(()=>{if(null==e)return v;var n=(n,i,a)=>{t!==a&&e===n&&r((0,y.M)(i))};return o.on(l,n),()=>{o.off(l,n)}},[r,t,e])}()}function m(e,t,r,l,u,d){var p=(0,i.G)(r=>(0,f.dp)(r,e,t)),y=(0,i.G)(a.pH),v=(0,i.G)(a.lZ),g=(0,i.G)(a.hX),m=(0,i.G)(h),b=null===m||void 0===m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var e=(0,s.E1)({active:d,coordinate:r,dataKey:p,index:u,label:"number"===typeof l?String(l):l});o.emit(c,v,e,y)}},[b,r,p,u,l,y,v,g,d])}},528:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(5043),i=r(7770),a=r.n(i),o=r(8387);function c(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}var l=r(4020),u=r(1759),s=r(2647),f=r(6307),d=r(7287),h=r(240),p=r(6831),y=["viewBox"],v=["viewBox"];function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},g.apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function x(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=w(e,y),i=this.props,{viewBox:a}=i,o=w(i,v);return!c(r,a)||!c(n,o)||!c(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:c,y:l,width:u,height:s,orientation:d,tickSize:h,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=e.tickSize||h,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=l+ +!p*s)-v*g)-v*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=c+ +!p*u)-v*g)-v*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=c+ +p*u)+v*g)+v*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=l+ +p*s)+v*g)+v*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:c,mirror:l,axisLine:u}=this.props,s=b(b(b({},(0,h.J9)(this.props,!1)),(0,h.J9)(u,!1)),{},{fill:"none"});if("top"===c||"bottom"===c){var f=+("top"===c&&!l||"bottom"===c&&l);s=b(b({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===c&&!l||"right"===c&&l);s=b(b({},s),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(u,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"===typeof e)i=e(b(b({},t),{},{className:a}));else{var c="recharts-cartesian-axis-tick-value";"boolean"!==typeof e&&(c=(0,o.$)(c,e.className)),i=n.createElement(u.E,g({},t,{className:c}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:c,tick:u,tickFormatter:s,unit:f}=this.props,y=(0,p.f)(b(b({},this.props),{},{ticks:r}),e,t),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),w=(0,h.J9)(this.props,!1),x=(0,h.J9)(u,!1),P=b(b({},w),{},{fill:"none"},(0,h.J9)(i,!1)),A=y.map((e,t)=>{var{line:r,tick:h}=this.getTickLineCoord(e),p=b(b(b(b({textAnchor:v,verticalAnchor:m},w),{},{stroke:"none",fill:c},x),h),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(l.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",g({},P,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),u&&O.renderTickItem(u,p,"".concat("function"===typeof s?s(e.value,t):e.value).concat(f||"")))});return A.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},A):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:c}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(l.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;n===this.state.fontSize&&i===this.state.letterSpacing||this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,c),s.J.renderCallByParent(this.props))}}x(O,"displayName","CartesianAxis"),x(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},713:(e,t,r)=>{"use strict";r.d(t,{s:()=>se});var n=r(5043),i=r(7950),a=r(4483),o=r(8387),c=r(4794);Math.abs,Math.atan2;const l=Math.cos,u=(Math.max,Math.min,Math.sin),s=Math.sqrt,f=Math.PI,d=2*f;const h={draw(e,t){const r=s(t/f);e.moveTo(r,0),e.arc(0,0,r,0,d)}},p={draw(e,t){const r=s(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},y=s(1/3),v=2*y,g={draw(e,t){const r=s(t/v),n=r*y;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},m={draw(e,t){const r=s(t),n=-r/2;e.rect(n,n,r,r)}},b=u(f/10)/u(7*f/10),w=u(d/10)*b,x=-l(d/10)*b,O={draw(e,t){const r=s(.8908130915292852*t),n=w*r,i=x*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const t=d*a/5,o=l(t),c=u(t);e.lineTo(c*r,-o*r),e.lineTo(o*n-c*i,c*n+o*i)}e.closePath()}},P=s(3),A={draw(e,t){const r=-s(t/(3*P));e.moveTo(0,2*r),e.lineTo(-P*r,-r),e.lineTo(P*r,-r),e.closePath()}},j=-.5,S=s(3)/2,M=1/s(12),E=3*(M/2+1),_={draw(e,t){const r=s(t/E),n=r/2,i=r*M,a=n,o=r*M+r,c=-a,l=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(c,l),e.lineTo(j*n-S*i,S*n+j*i),e.lineTo(j*a-S*o,S*a+j*o),e.lineTo(j*c-S*l,S*c+j*l),e.lineTo(j*n+S*i,j*i-S*n),e.lineTo(j*a+S*o,j*o-S*a),e.lineTo(j*c+S*l,j*l-S*c),e.closePath()}};var k=r(3809),C=r(7371);s(3),s(3);var T=r(240),D=r(6307),N=["type","size","sizeType"];function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},I.apply(null,arguments)}function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){L(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function L(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var B={symbolCircle:h,symbolCross:p,symbolDiamond:g,symbolSquare:m,symbolStar:O,symbolTriangle:A,symbolWye:_},$=Math.PI/180,U=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=R(R({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,N)),{},{type:t,size:r,sizeType:i}),{className:c,cx:l,cy:u}=a,s=(0,T.J9)(a,!0);return l===+l&&u===+u&&r===+r?n.createElement("path",I({},s,{className:(0,o.$)("recharts-symbols",c),transform:"translate(".concat(l,", ").concat(u,")"),d:(()=>{var e=(e=>{var t="symbol".concat((0,D.Zb)(e));return B[t]||h})(t),n=function(e,t){let r=null,n=(0,C.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"===typeof e?e:(0,k.A)(e||h),t="function"===typeof t?t:(0,k.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"===typeof t?t:(0,k.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"===typeof e?e:(0,k.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i}().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return.5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*$;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,i,t));return n()})()})):null};U.registerSymbol=(e,t)=>{B["symbol".concat((0,D.Zb)(e))]=t};var F=r(7287);function K(){return K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},K.apply(null,arguments)}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function G(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var W=32;class V extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,i=16,a=W/6,o=W/3,c=e.inactive?r:e.color,l=null!==t&&void 0!==t?t:e.type;if("none"===l)return null;if("plainline"===l)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:i,x2:W,y2:i,className:"recharts-legend-icon"});if("line"===l)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(i,"h").concat(o,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*o,",").concat(i,"\n            H").concat(W,"M").concat(2*o,",").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(o,",").concat(i),className:"recharts-legend-icon"});if("rect"===l)return n.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(4,"h").concat(W,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var u=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){G(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete u.legendIcon,n.cloneElement(e.legendIcon,u)}return n.createElement(U,{fill:c,cx:i,cy:i,size:W,sizeType:"diameter",type:l})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:i,inactiveColor:a,iconType:l}=this.props,u={x:0,y:0,width:W,height:W},s={display:"horizontal"===r?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var d=e.formatter||i,h=(0,o.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var p=e.inactive?a:e.color,y=d?d(e.value,e,r):e.value;return n.createElement("li",K({className:h,style:s,key:"legend-item-".concat(r)},(0,F.XC)(this.props,e,r)),n.createElement(c.u,{width:t,height:t,viewBox:u,style:f,"aria-label":"".concat(y," legend icon")},this.renderIcon(e,l)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:p}},y))})}render(){var{payload:e,layout:t,align:r}=this.props;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===t?r:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}G(V,"displayName","Legend"),G(V,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var q=r(2598),Z=r(787),Y=r(5807);var X=r(982),J=r(8796),Q=r(2814),ee=["contextPayload"];function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},te.apply(null,arguments)}function re(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?re(Object(r),!0).forEach(function(t){ie(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):re(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ie(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ae(e){return e.value}function oe(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ee),i=(0,q.s)(t,e.payloadUniqBy,ae),a=ne(ne({},r),{},{payload:i});return n.isValidElement(e.content)?n.cloneElement(e.content,a):"function"===typeof e.content?n.createElement(e.content,a):n.createElement(V,a)}function ce(e){var t=(0,Z.j)();return(0,n.useEffect)(()=>{t((0,Q.h1)(e))},[t,e]),null}function le(e){var t=(0,Z.j)();return(0,n.useEffect)(()=>(t((0,Q.hx)(e)),()=>{t((0,Q.hx)({width:0,height:0}))}),[t,e]),null}function ue(e){var t=(0,Z.G)(Y.g0),r=(0,a.M)(),o=(0,J.Kp)(),{width:c,height:l,wrapperStyle:u,portal:s}=e,[f,d]=(0,X.V)([t]),h=(0,J.yi)(),p=(0,J.rY)(),y=h-(o.left||0)-(o.right||0),v=se.getWidthOrHeight(e.layout,l,c,y),g=s?u:ne(ne({position:"absolute",width:(null===v||void 0===v?void 0:v.width)||c||"auto",height:(null===v||void 0===v?void 0:v.height)||l||"auto"},function(e,t,r,n,i,a){var o,c,{layout:l,align:u,verticalAlign:s}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===u&&"vertical"===l?{left:((n||0)-a.width)/2}:"right"===u?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(c="middle"===s?{top:((i||0)-a.height)/2}:"bottom"===s?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),ne(ne({},o),c)}(u,e,o,h,p,f)),u),m=null!==s&&void 0!==s?s:r;if(null==m)return null;var b=n.createElement("div",{className:"recharts-legend-wrapper",style:g,ref:d},n.createElement(ce,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),n.createElement(le,{width:f.width,height:f.height}),n.createElement(oe,te({},e,v,{margin:o,chartWidth:h,chartHeight:p,contextPayload:t})));return(0,i.createPortal)(b,m)}class se extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,D.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(ue,this.props)}}ie(se,"displayName","Legend"),ie(se,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"})},760:(e,t,r)=>{"use strict";r.d(t,{h:()=>b});var n=r(5043),i=r(8387),a=r(528),o=r(8360),c=r(787),l=r(4954),u=r(4721),s=r(3987),f=r(2647),d=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},p.apply(null,arguments)}function y(e){var t=(0,c.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var v=e=>{var t,{yAxisId:r,className:h,width:y,label:v}=e,g=(0,n.useRef)(null),m=(0,n.useRef)(null),b=(0,c.G)(u.c2),w=(0,s.r)(),x=(0,c.j)(),O="yAxis",P=(0,c.G)(e=>(0,l.iV)(e,O,r,w)),A=(0,c.G)(e=>(0,l.wP)(e,r)),j=(0,c.G)(e=>(0,l.KR)(e,r)),S=(0,c.G)(e=>(0,l.Zi)(e,O,r,w));if((0,n.useLayoutEffect)(()=>{var e;if("auto"===y&&A&&!(0,f.Z)(v)&&!(0,n.isValidElement)(v)){var t=g.current,i=null===t||void 0===t||null===(e=t.tickRefs)||void 0===e?void 0:e.current,{tickSize:a,tickMargin:c}=t.props,l=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var c=r?r.getBoundingClientRect().width:0,l=o+(i+a)+c+(r?n:0);return Math.round(l)}return 0})({ticks:i,label:m.current,labelGapWithTick:5,tickSize:a,tickMargin:c});Math.round(A.width)!==Math.round(l)&&x((0,o.QG)({id:r,width:l}))}},[g,null===g||void 0===g||null===(t=g.current)||void 0===t||null===(t=t.tickRefs)||void 0===t?void 0:t.current,null===A||void 0===A?void 0:A.width,A,x,v,r,y]),null==A||null==j)return null;var{dangerouslySetInnerHTML:M,ticks:E}=e,_=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d);return n.createElement(a.u,p({},_,{ref:g,labelRef:m,scale:P,x:j.x,y:j.y,width:A.width,height:A.height,className:(0,i.$)("recharts-".concat(O," ").concat(O),h),viewBox:b,ticks:S}))},g=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(y,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter}),n.createElement(v,e))},m={allowDataOverflow:l.cd.allowDataOverflow,allowDecimals:l.cd.allowDecimals,allowDuplicatedCategory:l.cd.allowDuplicatedCategory,hide:!1,mirror:l.cd.mirror,orientation:l.cd.orientation,padding:l.cd.padding,reversed:l.cd.reversed,scale:l.cd.scale,tickCount:l.cd.tickCount,type:l.cd.type,width:l.cd.width,yAxisId:0};class b extends n.Component{render(){return n.createElement(g,this.props)}}h(b,"displayName","YAxis"),h(b,"defaultProps",m)},787:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>c});var n=r(8443),i=r(5043),a=r(7250),o=e=>e,c=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},l=()=>{},u=()=>l,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:u,t?t.store.getState:l,t?t.store.getState:l,t?e:l,s)}},870:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"===typeof e||"function"===typeof e)}},982:(e,t,r)=>{"use strict";r.d(t,{V:()=>a});var n=r(5043),i=1;function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),a=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),a={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(a.height-t.height)>i||Math.abs(a.left-t.left)>i||Math.abs(a.top-t.top)>i||Math.abs(a.width-t.width)>i)&&r({height:a.height,left:a.left,top:a.top,width:a.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,a]}},1072:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(8139),i=r.n(n),a=r(5043),o=r(7852),c=r(579);const l=a.forwardRef((e,t)=>{let{bsPrefix:r,className:n,as:a="div",...l}=e;const u=(0,o.oU)(r,"row"),s=(0,o.gy)(),f=(0,o.Jm)(),d=`${u}-cols`,h=[];return s.forEach(e=>{const t=l[e];let r;delete l[e],null!=t&&"object"===typeof t?({cols:r}=t):r=t;const n=e!==f?`-${e}`:"";null!=r&&h.push(`${d}${n}-${r}`)}),(0,c.jsx)(a,{ref:t,...l,className:i()(n,u,...h)})});l.displayName="Row";const u=l},1203:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8420);t.isArrayLike=function(e){return null!=e&&"function"!==typeof e&&n.isLength(e.length)}},1293:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4830);t.debounce=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"object"!==typeof r&&(r={});const{leading:i=!1,trailing:a=!0,maxWait:o}=r,c=Array(2);let l;i&&(c[0]="leading"),a&&(c[1]="trailing");let u=null;const s=n.debounce(function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];l=e.apply(this,r),u=null},t,{edges:c}),f=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return null!=o&&(null===u&&(u=Date.now()),Date.now()-u>=o)?(l=e.apply(this,r),u=Date.now(),s.cancel(),s.schedule(),l):(s.apply(this,r),l)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),l),f}},1393:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(2574),i=(e,t)=>{var r=null===e||void 0===e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=1/0;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},1428:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>z,aX:()=>B,dS:()=>I,dp:()=>T,fW:()=>j,pg:()=>N,r1:()=>_,u9:()=>R,yn:()=>L});var n=r(2099),i=r(3821),a=r.n(i),o=r(787),c=r(4804),l=r(6307),u=r(5340),s=r(2711),f=r(116),d=r(8796),h=r(4721),p=r(3859),y=r(224),v=r(4986),g=r(1393),m=r(2510),b=r(9602),w=r(7591),x=r(6096);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){A(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function A(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var j=()=>(0,o.G)(f.iO),S=(e,t)=>t,M=(e,t,r)=>r,E=(e,t,r,n)=>n;var _=(0,n.Mz)(s.R4,e=>a()(e,e=>e.coordinate)),k=(0,n.Mz)([x.J,S,M,E],v.i),C=(0,n.Mz)([k,s.n4],g.P),T=(e,t,r)=>{if(null!=t){var n=(0,x.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},D=(0,n.Mz)([x.J,S,M,E],b.q),N=(0,n.Mz)([p.Lp,p.A$,d.fz,h.GO,s.R4,E,D,w.x],m.o),I=(0,n.Mz)([k,N],(e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t}),z=(0,n.Mz)(s.R4,C,y.E);var R=(0,n.Mz)([D,C,u.LF,s.Dn,z,w.x,S],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:u,computedData:s,dataStartIndex:f,dataEndIndex:d}=r;return e.reduce((e,r)=>{var h,p,y,v,g,m,{dataDefinedOnItem:b,settings:w}=r,x=function(e,t){return null!=e?e:t}(b,u),O=(p=x,y=f,v=d,Array.isArray(p)&&p&&y+v!==0?p.slice(y,v+1):p),A=null!==(h=null===w||void 0===w?void 0:w.dataKey)&&void 0!==h?h:null===n||void 0===n?void 0:n.dataKey,j=null===w||void 0===w?void 0:w.nameKey;(g=null===n||void 0===n||!n.dataKey||null!==n&&void 0!==n&&n.allowDuplicatedCategory||!Array.isArray(O)||"axis"!==o?a(O,t,s,j):(0,l.eP)(O,n.dataKey,i),Array.isArray(g))?g.forEach(t=>{var r=P(P({},w),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,c.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,c.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,c.GF)({tooltipEntrySettings:w,dataKey:A,payload:g,value:(0,c.kr)(g,A),name:null!==(m=(0,c.kr)(g,j))&&void 0!==m?m:null===w||void 0===w?void 0:w.name}));return e},[])}}),L=(0,n.Mz)([k],e=>({isActive:e.active,activeIndex:e.index})),B=(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var u=(0,c.r4)(e.chartX,e.chartY,t,r,l);if(u){var s=(0,c.SW)(u,t),f=(0,c.gH)(s,o,a,n,i),d=(0,c.bk)(t,a,f,u);return{activeIndex:String(f),activeCoordinate:d}}}}},1444:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){const t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){const c=e[n];a?"\\"===c&&n+1<r?(n++,i+=e[n]):c===a?a="":i+=c:o?'"'===c||"'"===c?a=c:"]"===c?(o=!1,t.push(i),i=""):i+=c:"["===c?(o=!0,i&&(t.push(i),i="")):"."===c?i&&(t.push(i),i=""):i+=c,n++}return i&&t.push(i),t}},1603:(e,t,r)=>{"use strict";r.d(t,{dl:()=>u,lJ:()=>l,uN:()=>a});var n=r(2017),i=r(6307);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null===e||void 0===e?void 0:e[r]}}var o={chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},c=(0,n.Z0)({name:"options",initialState:o,reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=c.reducer,{createEventEmitter:u}=c.actions},1759:(e,t,r)=>{"use strict";r.d(t,{E:()=>k});var n=r(5043),i=r(8387),a=r(6307),o=r(6015),c=r(240),l=r(7213),u=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,h={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(h),y="NaN";class v{static parse(e){var t,[,r,n]=null!==(t=d.exec(e))&&void 0!==t?t:[];return new v(parseFloat(r),null!==n&&void 0!==n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),p.includes(t)&&(this.num=function(e,t){return e*h[t]}(e,t),this.unit="px")}add(e){return this.unit!==e.unit?new v(NaN,""):new v(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new v(NaN,""):new v(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new v(NaN,""):new v(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new v(NaN,""):new v(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}}function g(e){if(e.includes(y))return y;for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=u.exec(t))&&void 0!==r?r:[],o=v.parse(null!==n&&void 0!==n?n:""),c=v.parse(null!==a&&void 0!==a?a:""),l="*"===i?o.multiply(c):o.divide(c);if(l.isNaN())return y;t=t.replace(u,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,h,p]=null!==(f=s.exec(t))&&void 0!==f?f:[],g=v.parse(null!==d&&void 0!==d?d:""),m=v.parse(null!==p&&void 0!==p?p:""),b="+"===h?g.add(m):g.subtract(m);if(b.isNaN())return y;t=t.replace(s,b.toString())}return t}var m=/\(([^()]*)\)/;function b(e){var t=e.replace(/\s+/g,"");return t=function(e){for(var t,r=e;null!=(t=m.exec(r));){var[,n]=t;r=r.replace(m,g(n))}return r}(t),t=g(t)}function w(e){var t=function(e){try{return b(e)}catch(t){return y}}(e.slice(5,-1));return t===y?"":t}var x=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],O=["dx","dy","angle","className","breakAll"];function P(){return P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},P.apply(null,arguments)}function A(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,S=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];return(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(j)),{wordsWithComputedWidth:i.map(e=>({word:e,width:(0,l.P)(e,n).width})),spaceWidth:r?0:(0,l.P)("\xa0",n).width}}catch(o){return null}},M=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(j)}],E=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:c,maxLines:l}=e;if((t||r)&&!o.m.isSsr){var u=S({breakAll:c,children:n,style:i});if(!u)return M(n);var{wordsWithComputedWidth:s,spaceWidth:f}=u;return((e,t,r,n,i)=>{var{maxLines:o,children:c,style:l,breakAll:u}=e,s=(0,a.Et)(o),f=c,d=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((e,t)=>{var{word:a,width:o}=t,c=e[e.length-1];if(c&&(null==n||i||c.width+o+r<Number(n)))c.words.push(a),c.width+=o+r;else{var l={words:[a],width:o};e.push(l)}return e},[])},h=d(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i)return h;if(!(h.length>o||p(h).width>Number(n)))return h;for(var y,v=e=>{var t=f.slice(0,e),r=S({breakAll:u,style:l,children:t+"\u2026"}).wordsWithComputedWidth,i=d(r);return[i.length>o||p(i).width>Number(n),i]},g=0,m=f.length-1,b=0;g<=m&&b<=f.length-1;){var w=Math.floor((g+m)/2),x=w-1,[O,P]=v(x),[A]=v(w);if(O||A||(g=w+1),O&&A&&(m=w-1),!O&&A){y=P;break}b++}return y||h})({breakAll:c,children:n,maxLines:l,style:i},s,f,t,r)}return M(n)},_="#808080",k=(0,n.forwardRef)((e,t)=>{var{x:r=0,y:o=0,lineHeight:l="1em",capHeight:u="0.71em",scaleToFit:s=!1,textAnchor:f="start",verticalAnchor:d="end",fill:h=_}=e,p=A(e,x),y=(0,n.useMemo)(()=>E({breakAll:p.breakAll,children:p.children,maxLines:p.maxLines,scaleToFit:s,style:p.style,width:p.width}),[p.breakAll,p.children,p.maxLines,s,p.style,p.width]),{dx:v,dy:g,angle:m,className:b,breakAll:j}=p,S=A(p,O);if(!(0,a.vh)(r)||!(0,a.vh)(o))return null;var M,k=r+((0,a.Et)(v)?v:0),C=o+((0,a.Et)(g)?g:0);switch(d){case"start":M=w("calc(".concat(u,")"));break;case"middle":M=w("calc(".concat((y.length-1)/2," * -").concat(l," + (").concat(u," / 2))"));break;default:M=w("calc(".concat(y.length-1," * -").concat(l,")"))}var T=[];if(s){var D=y[0].width,{width:N}=p;T.push("scale(".concat((0,a.Et)(N)?N/D:1,")"))}return m&&T.push("rotate(".concat(m,", ").concat(k,", ").concat(C,")")),T.length&&(S.transform=T.join(" ")),n.createElement("text",P({},(0,c.J9)(S,!0),{ref:t,x:k,y:C,className:(0,i.$)("recharts-text",b),textAnchor:f,fill:h.includes("url")?_:h}),y.map((e,t)=>{var r=e.words.join(j?"":" ");return n.createElement("tspan",{x:k,dy:0===t?M:l,key:"".concat(r,"-").concat(t)},r)}))});k.displayName="Text"},2017:(e,t,r)=>{"use strict";r.d(t,{U1:()=>g,VP:()=>u,Nc:()=>ne,Z0:()=>A});var n=r(3923);function i(e){return t=>{let{dispatch:r,getState:n}=t;return t=>i=>"function"===typeof i?i(r,n,e):t(i)}}var a=i(),o=i,c=r(8343),l="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function u(e,t){function r(){if(t){let r=t(...arguments);if(!r)throw new Error(ie(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:arguments.length<=0?void 0:arguments[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}var s=class e extends Array{constructor(){super(...arguments),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return super.concat.apply(this,t)}prepend(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return 1===r.length&&Array.isArray(r[0])?new e(...r[0].concat(this)):new e(...r.concat(this))}};function f(e){return(0,c.a6)(e)?(0,c.jM)(e,()=>{}):e}function d(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var h=()=>function(e){const{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=e??{};let c=new s;return t&&("boolean"===typeof t?c.push(a):c.push(o(t.extraArgument))),c},p="RTK_autoBatch",y=e=>t=>{setTimeout(t,e)},v=e=>function(t){const{autoBatch:r=!0}=t??{};let n=new s(e);return r&&n.push(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"raf"};return t=>function(){const r=t(...arguments);let n=!0,i=!1,a=!1;const o=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===e.type?e.queueNotification:y(e.timeout),l=()=>{a=!1,i&&(i=!1,o.forEach(e=>e()))};return Object.assign({},r,{subscribe(e){const t=r.subscribe(()=>n&&e());return o.add(e),()=>{t(),o.delete(e)}},dispatch(e){try{return n=!e?.meta?.[p],i=!n,i&&(a||(a=!0,c(l))),r.dispatch(e)}finally{n=!0}}})}}("object"===typeof r?r:void 0)),n};function g(e){const t=h(),{reducer:r,middleware:i,devTools:a=!0,duplicateMiddlewareCheck:o=!0,preloadedState:c,enhancers:u}=e||{};let s,f;if("function"===typeof r)s=r;else{if(!(0,n.Qd)(r))throw new Error(ie(1));s=(0,n.HY)(r)}f="function"===typeof i?i(t):t();let d=n.Zz;a&&(d=l({trace:!1,..."object"===typeof a&&a}));const p=(0,n.Tw)(...f),y=v(p);const g=d(..."function"===typeof u?u(y):y());return(0,n.y$)(s,c,g)}function m(e){const t={},r=[];let n;const i={addCase(e,r){const n="string"===typeof e?e:e.type;if(!n)throw new Error(ie(28));if(n in t)throw new Error(ie(29));return t[n]=r,i},addMatcher:(e,t)=>(r.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(n=e,i)};return e(i),[t,r,n]}var b=function(){let e="",t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:21;for(;t--;)e+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return e};var w=Symbol.for("rtk-slice-createasyncthunk");function x(e,t){return`${e}/${t}`}function O(){let{creators:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=e?.asyncThunk?.[w];return function(e){const{name:r,reducerPath:n=r}=e;if(!r)throw new Error(ie(11));const i=("function"===typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name](){return e(...arguments)}}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},a=Object.keys(i),o={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},l={addCase(e,t){const r="string"===typeof e?e:e.type;if(!r)throw new Error(ie(12));if(r in o.sliceCaseReducersByType)throw new Error(ie(13));return o.sliceCaseReducersByType[r]=t,l},addMatcher:(e,t)=>(o.sliceMatchers.push({matcher:e,reducer:t}),l),exposeAction:(e,t)=>(o.actionCreators[e]=t,l),exposeCaseReducer:(e,t)=>(o.sliceCaseReducersByName[e]=t,l)};function s(){const[t={},r=[],n]="function"===typeof e.extraReducers?m(e.extraReducers):[e.extraReducers],i={...t,...o.sliceCaseReducersByType};return function(e,t){let r,[n,i,a]=m(t);if("function"===typeof e)r=()=>f(e());else{const t=f(e);r=()=>t}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r(),t=arguments.length>1?arguments[1]:void 0,o=[n[t.type],...i.filter(e=>{let{matcher:r}=e;return r(t)}).map(e=>{let{reducer:t}=e;return t})];return 0===o.filter(e=>!!e).length&&(o=[a]),o.reduce((e,r)=>{if(r){if((0,c.Qx)(e)){const n=r(e,t);return void 0===n?e:n}if((0,c.a6)(e))return(0,c.jM)(e,e=>r(e,t));{const n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of o.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}a.forEach(n=>{const a=i[n],o={reducerName:n,type:x(r,n),createNotation:"function"===typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(a)?function(e,t,r){let n,i,{type:a,reducerName:o,createNotation:c}=e;if("reducer"in t){if(c&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(t))throw new Error(ie(17));n=t.reducer,i=t.prepare}else n=t;r.addCase(a,n).exposeCaseReducer(o,n).exposeAction(o,i?u(a,i):u(a))}(o,a,l):function(e,t,r,n){let{type:i,reducerName:a}=e;if(!n)throw new Error(ie(18));const{payloadCreator:o,fulfilled:c,pending:l,rejected:u,settled:s,options:f}=t,d=n(i,o,f);r.exposeAction(a,d),c&&r.addCase(d.fulfilled,c);l&&r.addCase(d.pending,l);u&&r.addCase(d.rejected,u);s&&r.addMatcher(d.settled,s);r.exposeCaseReducer(a,{fulfilled:c||j,pending:l||j,rejected:u||j,settled:s||j})}(o,a,l,t)});const h=e=>e,p=new Map,y=new WeakMap;let v;function g(e,t){return v||(v=s()),v(e,t)}function b(){return v||(v=s()),v.getInitialState()}function w(t){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function n(e){let i=e[t];return"undefined"===typeof i&&r&&(i=d(y,n,b)),i}function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;const n=d(p,r,()=>new WeakMap);return d(n,t,()=>{const n={};for(const[i,a]of Object.entries(e.selectors??{}))n[i]=P(a,t,()=>d(y,t,b),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}const O={name:r,reducer:g,actions:o.actionCreators,caseReducers:o.sliceCaseReducersByName,getInitialState:b,...w(n),injectInto(e){let{reducerPath:t,...r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=t??n;return e.inject({reducerPath:i,reducer:g},r),{...O,...w(i,!0)}}};return O}}function P(e,t,r,n){function i(i){let a=t(i);"undefined"===typeof a&&n&&(a=r());for(var o=arguments.length,c=new Array(o>1?o-1:0),l=1;l<o;l++)c[l-1]=arguments[l];return e(a,...c)}return i.unwrapped=e,i}var A=O();function j(){}var S="listener",M="completed",E="cancelled",_=`task-${E}`,k=`task-${M}`,C=`${S}-${E}`,T=`${S}-${M}`,D=class{constructor(e){this.code=e,this.message=`task ${E} (reason: ${e})`}name="TaskAbortError";message},N=(e,t)=>{if("function"!==typeof e)throw new TypeError(ie(32))},I=()=>{},z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:I;return e.catch(t),e},R=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),L=(e,t)=>{const r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},B=e=>{if(e.aborted){const{reason:t}=e;throw new D(t)}};function $(e,t){let r=I;return new Promise((n,i)=>{const a=()=>i(new D(e.reason));e.aborted?a():(r=R(e,a),t.finally(()=>r()).then(n,i))}).finally(()=>{r=I})}var U=e=>t=>z($(e,t).then(t=>(B(e),t))),F=e=>{const t=U(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:K}=Object,H={},G="listenerMiddleware",W=(e,t)=>(r,n)=>{N(r);const i=new AbortController;var a;a=i,R(e,()=>L(a,e.reason));const o=(async(e,t)=>{try{return await Promise.resolve(),{status:"ok",value:await e()}}catch(r){return{status:r instanceof D?"cancelled":"rejected",error:r}}finally{t?.()}})(async()=>{B(e),B(i.signal);const t=await r({pause:U(i.signal),delay:F(i.signal),signal:i.signal});return B(i.signal),t},()=>L(i,k));return n?.autoJoin&&t.push(o.catch(I)),{result:U(e)(o),cancel(){L(i,_)}}},V=(e,t)=>(r,n)=>z((async(r,n)=>{B(t);let i=()=>{};const a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{const e=await $(t,Promise.race(a));return B(t),e}finally{i()}})(r,n)),q=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(!i)throw new Error(ie(21));return N(a),{predicate:i,type:t,effect:a}},Z=K(e=>{const{type:t,predicate:r,effect:n}=q(e);return{id:b(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw new Error(ie(22))}}},{withTypes:()=>Z}),Y=(e,t)=>{const{type:r,effect:n,predicate:i}=q(t);return Array.from(e.values()).find(e=>("string"===typeof r?e.type===r:e.predicate===i)&&e.effect===n)},X=e=>{e.pending.forEach(e=>{L(e,C)})},J=(e,t,r)=>{try{e(t,r)}catch(n){setTimeout(()=>{throw n},0)}},Q=K(u(`${G}/add`),{withTypes:()=>Q}),ee=u(`${G}/removeAll`),te=K(u(`${G}/remove`),{withTypes:()=>te}),re=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];console.error(`${G}/error`,...t)},ne=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=new Map,{extra:r,onError:i=re}=e;N(i);const a=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&X(e)}))(Y(t,e)??Z(e));K(a,{withTypes:()=>a});const o=e=>{const r=Y(t,e);return r&&(r.unsubscribe(),e.cancelActive&&X(r)),!!r};K(o,{withTypes:()=>o});const c=async(e,n,o,c)=>{const l=new AbortController,u=V(a,l.signal),s=[];try{e.pending.add(l),await Promise.resolve(e.effect(n,K({},o,{getOriginalState:c,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:F(l.signal),pause:U(l.signal),extra:r,signal:l.signal,fork:W(l.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==l&&(L(e,C),r.delete(e))})},cancel:()=>{L(l,C),e.pending.delete(l)},throwIfCancelled:()=>{B(l.signal)}})))}catch(f){f instanceof D||J(i,f,{raisedBy:"effect"})}finally{await Promise.all(s),L(l,T),e.pending.delete(l)}},l=(e=>()=>{e.forEach(X),e.clear()})(t);return{middleware:e=>r=>u=>{if(!(0,n.ve)(u))return r(u);if(Q.match(u))return a(u.payload);if(ee.match(u))return void l();if(te.match(u))return o(u.payload);let s=e.getState();const f=()=>{if(s===H)throw new Error(ie(23));return s};let d;try{if(d=r(u),t.size>0){const r=e.getState(),n=Array.from(t.values());for(const t of n){let n=!1;try{n=t.predicate(u,r,s)}catch(h){n=!1,J(i,h,{raisedBy:"predicate"})}n&&c(t,u,e,f)}}}finally{s=H}return d},startListening:a,stopListening:o,clearListeners:l}};Symbol.for("rtk-state-proxy-original");function ie(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},2086:(e,t,r)=>{"use strict";e.exports=r(5082)},2099:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>s});function n(e){if("function"!==typeof e)throw new TypeError(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"expected a function, instead received "+typeof e)}var i=e=>Array.isArray(e)?e:[e];function a(e){const t=Array.isArray(e[0])?e[0]:e;return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"expected all items to be functions, instead received the following types: ";if(!e.every(e=>"function"===typeof e)){const r=e.map(e=>"function"===typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}Symbol(),Object.getPrototypeOf({});var o="undefined"!==typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function c(){return{s:0,v:void 0,o:null,p:null}}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let i,a=0;function l(){let t=r;const{length:l}=arguments;for(let e=0,r=l;e<r;e++){const r=arguments[e];if("function"===typeof r||"object"===typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t=c(),e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t=c(),e.set(r,t)):t=n}}const u=t;let s;if(1===t.s)s=t.v;else if(s=e.apply(null,arguments),a++,n){const e=i?.deref?.()??i;null!=e&&n(e,s)&&(s=e,0!==a&&a--);i="object"===typeof s&&null!==s||"function"===typeof s?new o(s):s}return u.s=1,u.v=s,s}return l.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},l.resetResultsCount()},l.resultsCount=()=>a,l.resetResultsCount=()=>{a=0},l}function u(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];const c="function"===typeof e?{memoize:e,memoizeOptions:r}:e,u=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let o,u=0,s=0,f={},d=t.pop();"object"===typeof d&&(f=d,d=t.pop()),n(d,`createSelector expects an output function after the inputs, but received: [${typeof d}]`);const h={...c,...f},{memoize:p,memoizeOptions:y=[],argsMemoize:v=l,argsMemoizeOptions:g=[],devModeChecks:m={}}=h,b=i(y),w=i(g),x=a(t),O=p(function(){return u++,d.apply(null,arguments)},...b);const P=v(function(){s++;const e=function(e,t){const r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(x,arguments);return o=O.apply(null,e),o},...w);return Object.assign(P,{resultFunc:d,memoizedResultFunc:O,dependencies:x,dependencyRecomputations:()=>s,resetDependencyRecomputations:()=>{s=0},lastResult:()=>o,recomputations:()=>u,resetRecomputations:()=>{u=0},memoize:p,argsMemoize:v})};return Object.assign(u,{withTypes:()=>u}),u}var s=u(l),f=Object.assign(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;!function(e){if("object"!==typeof e)throw new TypeError(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"expected an object, instead received "+typeof e)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e),n=t(r.map(t=>e[t]),function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((e,t,n)=>(e[r[n]]=t,e),{})});return n},{withTypes:()=>f})},2104:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(1203),i=r(9921);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},2132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8268),i=r(870),a=r(4803),o=r(9972);function c(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return l(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(const[i,a]of t.entries()){if(!1===r(e.get(i),a,i,e,t,n))return!1}return!0}(e,t,r,n);if(t instanceof Set)return u(e,t,r,n);const i=Object.keys(t);if(null==e)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let o=0;o<i.length;o++){const c=i[o];if(!a.isPrimitive(e)&&!(c in e))return!1;if(void 0===t[c]&&void 0!==e[c])return!1;if(null===t[c]&&null!==e[c])return!1;if(!r(e[c],t[c],c,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":return Object.keys(t).length>0?c(e,{...t},r,n):o.eq(e,t);default:return i.isObject(e)?"string"!==typeof t||""===t:o.eq(e,t)}}function l(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;const i=new Set;for(let a=0;a<t.length;a++){const o=t[a];let c=!1;for(let l=0;l<e.length;l++){if(i.has(l))continue;let u=!1;if(r(e[l],o,a,e,t,n)&&(u=!0),u){i.add(l),c=!0;break}}if(!c)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&l([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!==typeof r?n.isMatch(e,t):c(e,t,function e(t,n,i,a,o,l){const u=r(t,n,i,a,o,l);return void 0!==u?Boolean(u):c(t,n,e,l)},new Map)},t.isSetMatch=u},2185:(e,t,r)=>{"use strict";r.d(t,{W:()=>b});var n=r(5043),i=r(8387),a=r(528),o=r(787),c=r(8360),l=r(4954),u=r(4721),s=r(3987),f=["children"],d=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},p.apply(null,arguments)}function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v(e){var t=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return y(e,f)},[e]),i=(0,o.G)(e=>(0,l.Rl)(e,r.id)),a=r===i;return(0,n.useEffect)(()=>(t((0,c.Vi)(r)),()=>{t((0,c.MC)(r))}),[r,t]),a?e.children:null}var g=e=>{var{xAxisId:t,className:r}=e,c=(0,o.G)(u.c2),f=(0,s.r)(),h="xAxis",v=(0,o.G)(e=>(0,l.iV)(e,h,t,f)),g=(0,o.G)(e=>(0,l.Zi)(e,h,t,f)),m=(0,o.G)(e=>(0,l.Lw)(e,t)),b=(0,o.G)(e=>(0,l.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:w,ticks:x}=e,O=y(e,d);return n.createElement(a.u,p({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(h," ").concat(h),r),viewBox:c,ticks:g}))},m=e=>{var t,r,i,a,o;return n.createElement(v,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter},n.createElement(g,e))};class b extends n.Component{render(){return n.createElement(m,this.props)}}h(b,"displayName","XAxis"),h(b,"defaultProps",{allowDataOverflow:l.PU.allowDataOverflow,allowDecimals:l.PU.allowDecimals,allowDuplicatedCategory:l.PU.allowDuplicatedCategory,height:l.PU.height,hide:!1,mirror:l.PU.mirror,orientation:l.PU.orientation,padding:l.PU.padding,reversed:l.PU.reversed,scale:l.PU.scale,tickCount:l.PU.tickCount,type:l.PU.type,xAxisId:0})},2204:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(5316),i=r(7312),a=r(3799);t.sortBy=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];const c=r.length;return c>1&&a.isIterateeCall(e,r[0],r[1])?r=[]:c>2&&a.isIterateeCall(r[0],r[1],r[2])&&(r=[r[0]]),n.orderBy(e,i.flatten(r),["asc"])}},2277:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>l,au:()=>c,xH:()=>i});var n=r(787),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function c(e,t){return o(t,i(e),a(e))}function l(e){return(0,n.G)(t=>c(t,e))}},2330:(e,t,r)=>{"use strict";var n=r(5043);var i="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},a=n.useState,o=n.useEffect,c=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(n){return!0}}var s="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return c(function(){i.value=r,i.getSnapshot=t,u(i)&&s({inst:i})},[e,r,t]),o(function(){return u(i)&&s({inst:i}),e(function(){u(i)&&s({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},2489:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},2510:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,c)=>{if(null!=a&&null!=c){var l=o[0],u=null==l?void 0:c(l.positions,a);if(null!=u)return u;var s=null===i||void 0===i?void 0:i[Number(a)];if(s)return"horizontal"===r?{x:s.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:s.coordinate}}}},2574:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"===typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},2598:(e,t,r)=>{"use strict";r.d(t,{s:()=>a});var n=r(4332),i=r.n(n);function a(e,t,r){return!0===t?i()(e,r):"function"===typeof t?i()(e,t):e}},2647:(e,t,r)=>{"use strict";r.d(t,{J:()=>P,Z:()=>m});var n=r(5043),i=r(8387),a=r(1759),o=r(240),c=r(6307),l=r(165),u=r(8796),s=["offset"],f=["labelRef"];function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(){return v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},v.apply(null,arguments)}var g=e=>{var{value:t,formatter:r}=e,n=(0,c.uy)(e.children)?t:e.children;return"function"===typeof r?r(n):n},m=e=>null!=e&&"function"===typeof e,b=(e,t,r)=>{var a,o,{position:u,viewBox:s,offset:f,className:d}=e,{cx:h,cy:p,innerRadius:y,outerRadius:g,startAngle:m,endAngle:b,clockWise:w}=s,x=(y+g)/2,O=((e,t)=>(0,c.sA)(t-e)*Math.min(Math.abs(t-e),360))(m,b),P=O>=0?1:-1;"insideStart"===u?(a=m+P*f,o=w):"insideEnd"===u?(a=b-P*f,o=!w):"end"===u&&(a=b+P*f,o=w),o=O<=0?o:!o;var A=(0,l.IZ)(h,p,x,a),j=(0,l.IZ)(h,p,x,a+359*(o?1:-1)),S="M".concat(A.x,",").concat(A.y,"\n    A").concat(x,",").concat(x,",0,1,").concat(o?0:1,",\n    ").concat(j.x,",").concat(j.y),M=(0,c.uy)(e.id)?(0,c.NF)("recharts-radial-line-"):e.id;return n.createElement("text",v({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",d)}),n.createElement("defs",null,n.createElement("path",{id:M,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(M)},t))},w=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:c,startAngle:u,endAngle:s}=t,f=(u+s)/2;if("outside"===n){var{x:d,y:h}=(0,l.IZ)(i,a,c+r,f);return{x:d,y:h,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(o+c)/2,{x:y,y:v}=(0,l.IZ)(i,a,p,f);return{x:y,y:v,textAnchor:"middle",verticalAnchor:"middle"}},x=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:u}=t,s=u>=0?1:-1,f=s*n,d=s>0?"end":"start",h=s>0?"start":"end",y=l>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return p(p({},{x:a+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return p(p({},{x:a+l/2,y:o+u+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(o+u),0),width:l}:{});if("left"===i){var b={x:a-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"};return p(p({},b),r?{width:Math.max(b.x-r.x,0),height:u}:{})}if("right"===i){var w={x:a+l+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:u}:{})}var x=r?{width:l,height:u}:{};return"insideLeft"===i?p({x:a+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},x):"insideRight"===i?p({x:a+l-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"},x):"insideTop"===i?p({x:a+l/2,y:o+f,textAnchor:"middle",verticalAnchor:h},x):"insideBottom"===i?p({x:a+l/2,y:o+u-f,textAnchor:"middle",verticalAnchor:d},x):"insideTopLeft"===i?p({x:a+v,y:o+f,textAnchor:m,verticalAnchor:h},x):"insideTopRight"===i?p({x:a+l-v,y:o+f,textAnchor:g,verticalAnchor:h},x):"insideBottomLeft"===i?p({x:a+v,y:o+u-f,textAnchor:m,verticalAnchor:d},x):"insideBottomRight"===i?p({x:a+l-v,y:o+u-f,textAnchor:g,verticalAnchor:d},x):i&&"object"===typeof i&&((0,c.Et)(i.x)||(0,c._3)(i.x))&&((0,c.Et)(i.y)||(0,c._3)(i.y))?p({x:a+(0,c.F4)(i.x,l),y:o+(0,c.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},x):p({x:a+l/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},x)},O=e=>"cx"in e&&(0,c.Et)(e.cx);function P(e){var t,{offset:r=5}=e,l=p({offset:r},d(e,s)),{viewBox:h,position:y,value:m,children:P,content:A,className:j="",textBreakAll:S,labelRef:M}=l,E=(0,u.sk)(),_=h||E;if(!_||(0,c.uy)(m)&&(0,c.uy)(P)&&!(0,n.isValidElement)(A)&&"function"!==typeof A)return null;if((0,n.isValidElement)(A)){var{labelRef:k}=l,C=d(l,f);return(0,n.cloneElement)(A,C)}if("function"===typeof A){if(t=(0,n.createElement)(A,l),(0,n.isValidElement)(t))return t}else t=g(l);var T=O(_),D=(0,o.J9)(l,!0);if(T&&("insideStart"===y||"insideEnd"===y||"end"===y))return b(l,t,D);var N=T?w(l):x(l,_);return n.createElement(a.E,v({ref:M,className:(0,i.$)("recharts-label",j)},D,N,{breakAll:S}),t)}P.displayName="Label";var A=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:u,outerRadius:s,x:f,y:d,top:h,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,c.Et)(y)&&(0,c.Et)(v)){if((0,c.Et)(f)&&(0,c.Et)(d))return{x:f,y:d,width:y,height:v};if((0,c.Et)(h)&&(0,c.Et)(p))return{x:h,y:p,width:y,height:v}}return(0,c.Et)(f)&&(0,c.Et)(d)?{x:f,y:d,width:0,height:0}:(0,c.Et)(t)&&(0,c.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:s||l||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0};P.parseViewBox=A,P.renderCallByParent=function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,l=A(e),u=(0,o.aS)(i,P).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));if(!r)return u;var s=((e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(P,v({key:"label-implicit"},i)):(0,c.vh)(e)?n.createElement(P,v({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===P?(0,n.cloneElement)(e,p({key:"label-implicit"},i)):n.createElement(P,v({key:"label-implicit",content:e},i)):m(e)?n.createElement(P,v({key:"label-implicit",content:e},i)):e&&"object"===typeof e?n.createElement(P,v({},e,{key:"label-implicit"},i)):null})(e.label,t||l,a);return[s,...u]}},2711:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ie,eE:()=>le,Xb:()=>ae,A2:()=>ne,yn:()=>ue,Dn:()=>P,gL:()=>q,fl:()=>Z,R4:()=>J,Re:()=>x,n4:()=>_});var n=r(2099),i=r(4954),a=r(8796),o=r(4804),c=r(5340),l=r(116),u=r(6307),s=r(138),f=r(2277),d=r(224),h=r(4986),p=r(1393),y=r(2510),v=r(3859),g=r(4721),m=r(9602),b=r(7591),w=r(6096),x=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},O=e=>e.tooltip.settings.axisId,P=e=>{var t=x(e),r=O(e);return(0,i.Hd)(e,t,r)},A=(0,n.Mz)([P,a.fz,i.um,l.iO,x],i.sr),j=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),S=(0,n.Mz)([x,O],i.eo),M=(0,n.Mz)([j,P,S],i.ec),E=(0,n.Mz)([M],i.rj),_=(0,n.Mz)([E,c.LF],i.Nk),k=(0,n.Mz)([_,P,M],i.fb),C=(0,n.Mz)([P],i.S5),T=(0,n.Mz)([_,M,l.eC],i.MK),D=(0,n.Mz)([T,c.LF,x],i.pM),N=(0,n.Mz)([M],i.IO),I=(0,n.Mz)([_,P,N,x],i.kz),z=(0,n.Mz)([i.Kr,x,O],i.P9),R=(0,n.Mz)([z,x],i.Oz),L=(0,n.Mz)([i.gT,x,O],i.P9),B=(0,n.Mz)([L,x],i.q),$=(0,n.Mz)([i.$X,x,O],i.P9),U=(0,n.Mz)([$,x],i.bb),F=(0,n.Mz)([R,U,B],i.yi),K=(0,n.Mz)([P,C,D,I,F],i.wL),H=(0,n.Mz)([P,a.fz,_,k,l.eC,x,K],i.tP),G=(0,n.Mz)([H,P,A],i.xp),W=(0,n.Mz)([P,H,G,x],i.g1),V=e=>{var t=x(e),r=O(e);return(0,i.D5)(e,t,r,!1)},q=(0,n.Mz)([P,V],s.I),Z=(0,n.Mz)([P,A,W,q],i.Qn),Y=(0,n.Mz)([a.fz,k,P,x],i.tF),X=(0,n.Mz)([a.fz,k,P,x],i.iv),J=(0,n.Mz)([a.fz,P,A,Z,V,Y,X,x],(e,t,r,n,i,a,c,l)=>{if(t){var{type:s}=t,f=(0,o._L)(e,l);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/d:0;return h="angleAxis"===l&&null!=i&&(null===i||void 0===i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*h:h,f&&c?c.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:a?a[e]:e,index:t,offset:h}))}}}),Q=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),ee=e=>e.tooltip.settings.trigger,te=e=>e.tooltip.settings.defaultIndex,re=(0,n.Mz)([w.J,Q,ee,te],h.i),ne=(0,n.Mz)([re,_],p.P),ie=(0,n.Mz)([J,ne],d.E),ae=(0,n.Mz)([re],e=>{if(e)return e.dataKey}),oe=(0,n.Mz)([w.J,Q,ee,te],m.q),ce=(0,n.Mz)([v.Lp,v.A$,a.fz,g.GO,J,te,oe,b.x],y.o),le=(0,n.Mz)([re,ce],(e,t)=>null!==e&&void 0!==e&&e.coordinate?e.coordinate:t),ue=(0,n.Mz)([re],e=>e.active)},2715:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9599),i=r(7330),a=r(3808),o=r(8845);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":return Array.isArray(e)&&2===e.length?o.matchesProperty(e[0],e[1]):a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},2768:(e,t,r)=>{"use strict";r.d(t,{E1:()=>m,En:()=>w,Ix:()=>u,Nt:()=>v,RD:()=>d,UF:()=>f,XB:()=>s,jF:()=>g,k_:()=>o,o4:()=>b,xS:()=>p});var n=r(2017),i=r(8343),a=r(5839),o={active:!1,index:null,dataKey:void 0,coordinate:void 0},c={itemInteraction:{click:o,hover:o},axisInteraction:{click:o,hover:o},keyboardInteraction:o,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},l=(0,n.Z0)({name:"tooltip",initialState:c,reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,a.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,a.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:u,removeTooltipEntrySettings:s,setTooltipSettingsState:f,setActiveMouseOverItemIndex:d,mouseLeaveItem:h,mouseLeaveChart:p,setActiveClickItemIndex:y,setMouseOverAxisIndex:v,setMouseClickAxisIndex:g,setSyncInteraction:m,setKeyboardInteraction:b}=l.actions,w=l.reducer},2777:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"===typeof e||e instanceof Symbol}},2814:(e,t,r)=>{"use strict";r.d(t,{CU:()=>f,Lx:()=>u,h1:()=>l,hx:()=>c,u3:()=>s});var n=r(2017),i=r(8343),a=r(5839),o=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,a.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,a.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:c,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:s}=o.actions,f=o.reducer},2872:(e,t,r)=>{"use strict";r.d(t,{N:()=>Fe,l:()=>Ue});var n=r(5043),i=r(8387),a=r(8471),o=r(7287),c=r(240);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},l.apply(null,arguments)}var u=e=>{var{cx:t,cy:r,r:a,className:u}=e,s=(0,i.$)("recharts-dot",u);return t===+t&&r===+r&&a===+a?n.createElement("circle",l({},(0,c.J9)(e,!1),(0,o._U)(e),{className:s,cx:t,cy:r,r:a})):null},s=r(4020),f=r(356),d=r.n(f),h=r(2647),p=r(4804),y=r(6307),v=["valueAccessor"],g=["data","dataKey","clockWise","id","textBreakAll"];function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},m.apply(null,arguments)}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var P=e=>Array.isArray(e.value)?d()(e.value):e.value;function A(e){var{valueAccessor:t=P}=e,r=O(e,v),{data:i,dataKey:a,clockWise:o,id:l,textBreakAll:u}=r,f=O(r,g);return i&&i.length?n.createElement(s.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,y.uy)(a)?t(e,r):(0,p.kr)(e&&e.payload,a),s=(0,y.uy)(l)?{}:{id:"".concat(l,"-").concat(r)};return n.createElement(h.J,m({},(0,c.J9)(e,!0),f,s,{parentViewBox:e.parentViewBox,value:i,textBreakAll:u,viewBox:h.J.parseViewBox((0,y.uy)(o)?e:w(w({},e),{},{clockWise:o})),key:"label-".concat(r),index:r}))})):null}A.displayName="LabelList",A.renderCallByParent=function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i}=e,a=(0,c.aS)(i,A).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return r?[function(e,t){return e?!0===e?n.createElement(A,{key:"labelList-implicit",data:t}):n.isValidElement(e)||(0,h.Z)(e)?n.createElement(A,{key:"labelList-implicit",data:t,content:e}):"object"===typeof e?n.createElement(A,m({data:t},e,{key:"labelList-implicit"})):null:null}(e.label,t),...a]:a};var j=r(787),S=r(9256);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k(e){var t=(0,j.j)();return(0,n.useEffect)(()=>{var r=E(E({},e),{},{stackId:(0,p.$8)(e.stackId)});return t((0,S.g5)(r)),()=>{t((0,S.Vi)(r))}},[t,e]),null}var C=r(3987),T=["children"];var D=()=>{},N=(0,n.createContext)({addErrorBar:D,removeErrorBar:D}),I={data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0},z=(0,n.createContext)(I);function R(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,T);return n.createElement(z.Provider,{value:r},t)}var L=e=>{var{children:t,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,data:c,stackId:l,hide:u,type:s,barSize:f}=e,[d,h]=n.useState([]),p=(0,n.useCallback)(e=>{h(t=>[...t,e])},[h]),y=(0,n.useCallback)(e=>{h(t=>t.filter(t=>t!==e))},[h]),v=(0,C.r)();return n.createElement(N.Provider,{value:{addErrorBar:p,removeErrorBar:y}},n.createElement(k,{type:s,data:c,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,errorBars:d,stackId:l,hide:u,barSize:f,isPanorama:v}),t)};function B(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(N);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var $=r(4954),U=r(6371),F=r(5654),K=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function H(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function G(){return G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},G.apply(null,arguments)}function W(e){var{direction:t,width:r,dataKey:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:u}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,K),d=(0,c.J9)(f,!1),{data:h,dataPointFormatter:p,xAxisId:y,yAxisId:v,errorBarOffset:g}=(0,n.useContext)(z),m=(e=>{var t=(0,C.r)();return(0,j.G)(r=>(0,$.Gx)(r,"xAxis",e,t))})(y),b=(e=>{var t=(0,C.r)();return(0,j.G)(r=>(0,$.Gx)(r,"yAxis",e,t))})(v);if(null==(null===m||void 0===m?void 0:m.scale)||null==(null===b||void 0===b?void 0:b.scale)||null==h)return null;if("x"===t&&"number"!==m.type)return null;var w=h.map(e=>{var{x:c,y:f,value:h,errorVal:y}=p(e,i,t);if(!y)return null;var v,w,x=[];if(Array.isArray(y)?[v,w]=y:v=w=y,"x"===t){var{scale:O}=m,P=f+g,A=P+r,j=P-r,S=O(h-v),M=O(h+w);x.push({x1:M,y1:A,x2:M,y2:j}),x.push({x1:S,y1:P,x2:M,y2:P}),x.push({x1:S,y1:A,x2:S,y2:j})}else if("y"===t){var{scale:E}=b,_=c+g,k=_-r,C=_+r,T=E(h-v),D=E(h+w);x.push({x1:k,y1:D,x2:C,y2:D}),x.push({x1:_,y1:T,x2:_,y2:D}),x.push({x1:k,y1:T,x2:C,y2:T})}var N="".concat(c+g,"px ").concat(f+g,"px");return n.createElement(s.W,G({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},d),x.map(e=>{var t=a?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(F.i,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:o,easing:u,isActive:a,duration:l,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:N}},n.createElement("line",G({},e,{style:t})))}))});return n.createElement(s.W,{className:"recharts-errorBars"},w)}var V=(0,n.createContext)(void 0);function q(e){var{direction:t,children:r}=e;return n.createElement(V.Provider,{value:t},r)}var Z={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function Y(e){var t=function(e){var t=(0,n.useContext)(V);return null!=e?e:null!=t?t:"x"}(e.direction),{width:r,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:c}=(0,U.e)(e,Z);return n.createElement(n.Fragment,null,n.createElement(B,{dataKey:e.dataKey,direction:t}),n.createElement(W,G({},e,{direction:t,width:r,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:c})))}class X extends n.Component{render(){return n.createElement(Y,this.props)}}H(X,"defaultProps",Z),H(X,"displayName","ErrorBar");var J=r(6015),Q=r(3374),ee=r(2711);function te(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function re(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?te(Object(r),!0).forEach(function(t){ne(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ne(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ie(e){var t,{points:r,mainColor:i,activeDot:a,itemDataKey:l}=e,f=(0,Q.E)(),d=(0,j.G)(ee.A2),h=(0,j.G)(ee.BZ);if(!d)return null;var p=f.dataKey;if(p&&!f.allowDuplicatedCategory){var v="function"===typeof p?e=>p(e.payload):"payload.".concat(p);t=(0,y.eP)(r,v,h)}else t=null===r||void 0===r?void 0:r[Number(d)];return(0,y.uy)(t)?null:(e=>{var{point:t,childIndex:r,mainColor:i,activeDot:a,dataKey:l}=e;if(!1===a||null==t.x||null==t.y)return null;var f,d=re(re({index:r,dataKey:l,cx:t.x,cy:t.y,r:4,fill:null!==i&&void 0!==i?i:"none",strokeWidth:2,stroke:"#fff",payload:t.payload,value:t.value},(0,c.J9)(a,!1)),(0,o._U)(a));return f=(0,n.isValidElement)(a)?(0,n.cloneElement)(a,d):"function"===typeof a?a(d):n.createElement(u,d),n.createElement(s.W,{className:"recharts-active-dot"},f)})({point:t,childIndex:Number(d),mainColor:i,dataKey:l,activeDot:a})}var ae=r(2768);function oe(e){var{fn:t,args:r}=e,i=(0,j.j)(),a=(0,C.r)();return(0,n.useEffect)(()=>{if(!a){var e=t(r);return i((0,ae.Ix)(e)),()=>{i((0,ae.XB)(e))}}},[t,r,i,a]),null}var ce=r(8796);function le(e,t){var r,n,i=(0,j.G)(t=>(0,$.Rl)(t,e)),a=(0,j.G)(e=>(0,$.sf)(e,t)),o=null!==(r=null===i||void 0===i?void 0:i.allowDataOverflow)&&void 0!==r?r:$.PU.allowDataOverflow,c=null!==(n=null===a||void 0===a?void 0:a.allowDataOverflow)&&void 0!==n?n:$.cd.allowDataOverflow;return{needClip:o||c,needClipX:o,needClipY:c}}function ue(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,ce.hj)(),{needClipX:o,needClipY:c,needClip:l}=le(t,r);if(!l)return null;var{left:u,top:s,width:f,height:d}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?u:u-f/2,y:c?s:s-d/2,width:o?f:2*f,height:c?d:2*d}))}var se=r(2099),fe=r(5340),de=(e,t,r,n)=>(0,$.Gx)(e,"xAxis",t,n),he=(e,t,r,n)=>(0,$.CR)(e,"xAxis",t,n),pe=(e,t,r,n)=>(0,$.Gx)(e,"yAxis",r,n),ye=(e,t,r,n)=>(0,$.CR)(e,"yAxis",r,n),ve=(0,se.Mz)([ce.fz,de,pe,he,ye],(e,t,r,n,i)=>(0,p._L)(e,"xAxis")?(0,p.Hj)(t,n,!1):(0,p.Hj)(r,i,!1)),ge=(0,se.Mz)([$.ld,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),me=(0,se.Mz)([ce.fz,de,pe,he,ye,ge,ve,fe.HS],(e,t,r,n,i,a,o,c)=>{var{chartData:l,dataStartIndex:u,dataEndIndex:s}=c;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var f,{dataKey:d,data:h}=a;if(null!=(f=null!=h&&h.length>0?h:null===l||void 0===l?void 0:l.slice(u,s+1)))return Ue({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:f})}}),be=r(2814),we=()=>{};function xe(e){var{legendPayload:t}=e,r=(0,j.j)(),i=(0,C.r)();return(0,n.useEffect)(()=>i?we:(r((0,be.Lx)(t)),()=>{r((0,be.u3)(t))}),[r,i,t]),null}var Oe=["type","layout","connectNulls","needClip"],Pe=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function Ae(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function je(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?je(Object(r),!0).forEach(function(t){Me(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):je(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Me(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ee(){return Ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ee.apply(null,arguments)}var _e=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,p.uM)(r,t),payload:e}]};function ke(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:c,unit:l}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:(0,p.uM)(o,t),hide:c,type:e.tooltipType,color:e.stroke,unit:l}}}var Ce=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function Te(e,t){for(var r=e.length%2!==0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}function De(e){var{clipPathId:t,points:r,props:a}=e,{dot:o,dataKey:l,needClip:f}=a;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(r,o))return null;var d=(0,c.y$)(o),h=(0,c.J9)(a,!1),p=(0,c.J9)(o,!0),y=r.map((e,t)=>{var a=Se(Se(Se({key:"dot-".concat(t),r:3},h),p),{},{index:t,cx:e.x,cy:e.y,dataKey:l,value:e.value,payload:e.payload,points:r});return function(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"===typeof e)r=e(t);else{var a=(0,i.$)("recharts-line-dot","boolean"!==typeof e?e.className:"");r=n.createElement(u,Ee({},t,{className:a}))}return r}(o,a)}),v={clipPath:f?"url(#clipPath-".concat(d?"":"dots-").concat(t,")"):null};return n.createElement(s.W,Ee({className:"recharts-line-dots",key:"dots"},v),y)}function Ne(e){var{clipPathId:t,pathRef:r,points:i,strokeDasharray:o,props:l,showLabels:u}=e,{type:s,layout:f,connectNulls:d,needClip:h}=l,p=Ae(l,Oe),y=Se(Se({},(0,c.J9)(p,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:h?"url(#clipPath-".concat(t,")"):null,points:i,type:s,layout:f,connectNulls:d,strokeDasharray:null!==o&&void 0!==o?o:l.strokeDasharray});return n.createElement(n.Fragment,null,(null===i||void 0===i?void 0:i.length)>1&&n.createElement(a.I,Ee({},y,{pathRef:r})),n.createElement(De,{points:i,clipPathId:t,props:l}),u&&A.renderCallByParent(l,i))}function Ie(e){var{clipPathId:t,props:r,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:o}=e,{points:c,strokeDasharray:l,isAnimationActive:u,animationBegin:s,animationDuration:f,animationEasing:d,animateNewValues:h,width:p,height:v,onAnimationEnd:g,onAnimationStart:m}=r,b=a.current,w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,y.NF)(t)),i=(0,n.useRef)(e);return i.current!==e&&(r.current=(0,y.NF)(t),i.current=e),r.current}(r,"recharts-line-"),[x,O]=(0,n.useState)(!1),P=(0,n.useCallback)(()=>{"function"===typeof g&&g(),O(!1)},[g]),A=(0,n.useCallback)(()=>{"function"===typeof m&&m(),O(!0)},[m]),j=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(t){return 0}}(i.current),S=o.current;return n.createElement(F.i,{begin:s,duration:f,isActive:u,easing:d,from:{t:0},to:{t:1},onAnimationEnd:P,onAnimationStart:A,key:w},e=>{var u,{t:s}=e,f=(0,y.Dj)(S,j+S),d=Math.min(f(s),j);if(l){var g="".concat(l).split(/[,\s]+/gim).map(e=>parseFloat(e));u=((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return Ce(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,c=[],l=0,u=0;l<r.length;u+=r[l],++l)if(u+r[l]>a){c=[...r.slice(0,l),a-u];break}var s=c.length%2===0?[0,o]:[o];return[...Te(r,i),...c,...s].map(e=>"".concat(e,"px")).join(", ")})(d,j,g)}else u=Ce(j,d);if(b){var m=b.length/c.length,w=1===s?c:c.map((e,t)=>{var r=Math.floor(t*m);if(b[r]){var n=b[r],i=(0,y.Dj)(n.x,e.x),a=(0,y.Dj)(n.y,e.y);return Se(Se({},e),{},{x:i(s),y:a(s)})}if(h){var o=(0,y.Dj)(2*p,e.x),c=(0,y.Dj)(v/2,e.y);return Se(Se({},e),{},{x:o(s),y:c(s)})}return Se(Se({},e),{},{x:e.x,y:e.y})});return a.current=w,n.createElement(Ne,{props:r,points:w,clipPathId:t,pathRef:i,showLabels:!x,strokeDasharray:u})}return s>0&&j>0&&(a.current=c,o.current=d),n.createElement(Ne,{props:r,points:c,clipPathId:t,pathRef:i,showLabels:!x,strokeDasharray:u})})}function ze(e){var{clipPathId:t,props:r}=e,{points:i,isAnimationActive:a}=r,o=(0,n.useRef)(null),c=(0,n.useRef)(0),l=(0,n.useRef)(null),u=o.current;return a&&i&&i.length&&u!==i?n.createElement(Ie,{props:r,clipPathId:t,previousPointsRef:o,longestAnimatedLengthRef:c,pathRef:l}):n.createElement(Ne,{props:r,points:i,clipPathId:t,pathRef:l,showLabels:!0})}var Re=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:(0,p.kr)(e.payload,t)});class Le extends n.Component{constructor(){super(...arguments),Me(this,"id",(0,y.NF)("recharts-line-"))}render(){var e,{hide:t,dot:r,points:a,className:o,xAxisId:l,yAxisId:u,top:f,left:d,width:h,height:p,id:v,needClip:g,layout:m}=this.props;if(t)return null;var b=(0,i.$)("recharts-line",o),w=(0,y.uy)(v)?this.id:v,{r:x=3,strokeWidth:O=2}=null!==(e=(0,c.J9)(r,!1))&&void 0!==e?e:{r:3,strokeWidth:2},P=(0,c.y$)(r),A=2*x+O;return n.createElement(n.Fragment,null,n.createElement(s.W,{className:b},g&&n.createElement("defs",null,n.createElement(ue,{clipPathId:w,xAxisId:l,yAxisId:u}),!P&&n.createElement("clipPath",{id:"clipPath-dots-".concat(w)},n.createElement("rect",{x:d-A/2,y:f-A/2,width:h+A,height:p+A}))),n.createElement(ze,{props:this.props,clipPathId:w}),n.createElement(q,{direction:"horizontal"===m?"y":"x"},n.createElement(R,{xAxisId:l,yAxisId:u,data:a,dataPointFormatter:Re,errorBarOffset:0},this.props.children))),n.createElement(ie,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var Be={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!J.m.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function $e(e){var t=(0,U.e)(e,Be),{activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:c,connectNulls:l,dot:u,hide:s,isAnimationActive:f,label:d,legendType:h,xAxisId:p,yAxisId:y}=t,v=Ae(t,Pe),{needClip:g}=le(p,y),{height:m,width:b,left:w,top:x}=(0,ce.hj)(),O=(0,ce.WX)(),P=(0,C.r)(),A=(0,n.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),S=(0,j.G)(e=>me(e,p,y,P,A));return"horizontal"!==O&&"vertical"!==O?null:n.createElement(Le,Ee({},v,{connectNulls:l,dot:u,activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:c,isAnimationActive:f,hide:s,label:d,legendType:h,xAxisId:p,yAxisId:y,points:S,layout:O,height:m,width:b,left:w,top:x,needClip:g}))}function Ue(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:c,displayedData:l}=e;return l.map((e,l)=>{var u=(0,p.kr)(e,o);return"horizontal"===t?{x:(0,p.nb)({axis:r,ticks:i,bandSize:c,entry:e,index:l}),y:(0,y.uy)(u)?null:n.scale(u),value:u,payload:e}:{x:(0,y.uy)(u)?null:r.scale(u),y:(0,p.nb)({axis:n,ticks:a,bandSize:c,entry:e,index:l}),value:u,payload:e}})}class Fe extends n.PureComponent{render(){return n.createElement(L,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(xe,{legendPayload:_e(this.props)}),n.createElement(oe,{fn:ke,args:this.props}),n.createElement($e,this.props))}}Me(Fe,"displayName","Line"),Me(Fe,"defaultProps",Be)},2998:(e,t,r)=>{"use strict";r.d(t,{b:()=>yt});var n=r(5043),i=r(1603);r(7237);function a(e){e()}var o={notify(){},get:()=>[]};function c(e,t){let r,n=o,i=0,c=!1;function l(){f.onStateChange&&f.onStateChange()}function u(){i++,r||(r=t?t.addNestedSub(l):e.subscribe(l),n=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){a(()=>{let t=e;for(;t;)t.callback(),t=t.next})},get(){const t=[];let r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(r){let n=!0;const i=t={callback:r,next:null,prev:t};return i.prev?i.prev.next=i:e=i,function(){n&&null!==e&&(n=!1,i.next?i.next.prev=i.prev:t=i.prev,i.prev?i.prev.next=i.next:e=i.next)}}}}())}function s(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=o)}const f={addNestedSub:function(e){u();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),s())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:l,isSubscribed:function(){return c},trySubscribe:function(){c||(c=!0,u())},tryUnsubscribe:function(){c&&(c=!1,s())},getListeners:()=>n};return f}var l=(()=>!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement))(),u=(()=>"undefined"!==typeof navigator&&"ReactNative"===navigator.product)(),s=(()=>l||u?n.useLayoutEffect:n.useEffect)();Object.defineProperty,Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var f=Symbol.for("react-redux-context"),d="undefined"!==typeof globalThis?globalThis:{};function h(){if(!n.createContext)return{};const e=d[f]??=new Map;let t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}var p=h();var y=function(e){const{children:t,context:r,serverState:i,store:a}=e,o=n.useMemo(()=>{const e=c(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),l=n.useMemo(()=>a.getState(),[a]);s(()=>{const{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,l]);const u=r||p;return n.createElement(u.Provider,{value:o},t)};var v=r(3923),g=r(2017),m=r(2768),b=r(7773),w=(0,g.Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:x,setLayout:O,setChartSize:P,setScale:A}=w.actions,j=w.reducer,S=r(2099),M=r(8796),E=r(2711),_=r(4721),k=r(1428),C=r(6178),T=(0,S.Mz)([(e,t)=>t,M.fz,C.D0,E.Re,E.gL,E.R4,k.r1,_.GO],k.aX),D=r(2277),N=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},I=(0,g.VP)("mouseClick"),z=(0,g.Nc)();z.startListening({actionCreator:I,effect:(e,t)=>{var r=e.payload,n=T(t.getState(),N(r));null!=(null===n||void 0===n?void 0:n.activeIndex)&&t.dispatch((0,m.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var R=(0,g.VP)("mouseMove"),L=(0,g.Nc)();function B(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}L.startListening({actionCreator:R,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=(0,D.au)(n,n.tooltip.settings.shared),a=T(n,N(r));"axis"===i&&(null!=(null===a||void 0===a?void 0:a.activeIndex)?t.dispatch((0,m.Nt)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch((0,m.xS)()))}});var $=r(8360),U=r(9256),F=r(8343),K=(0,g.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,F.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,F.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,F.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:H,removeDot:G,addArea:W,removeArea:V,addLine:q,removeLine:Z}=K.actions,Y=K.reducer,X={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},J=(0,g.Z0)({name:"brush",initialState:X,reducers:{setBrushSettings:(e,t)=>null==t.payload?X:t.payload}}),{setBrushSettings:Q}=J.actions,ee=J.reducer,te=r(2814),re={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},ne=(0,g.Z0)({name:"rootProps",initialState:re,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:re.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),ie=ne.reducer,{updateOptions:ae}=ne.actions,oe=r(5839),ce=(0,g.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,oe.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,oe.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:le,removeRadiusAxis:ue,addAngleAxis:se,removeAngleAxis:fe}=ce.actions,de=ce.reducer,he=(0,g.Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:pe}=he.actions,ye=he.reducer,ve=r(4954),ge=r(1393),me=(0,g.VP)("keyDown"),be=(0,g.VP)("focus"),we=(0,g.Nc)();we.startListening({actionCreator:me,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number((0,ge.P)(n,(0,E.n4)(r))),o=(0,E.R4)(r);if("Enter"!==i){var c=a+("ArrowRight"===i?1:-1)*("left-to-right"===(0,ve._y)(r)?1:-1);if(!(null==o||c>=o.length||c<0)){var l=(0,k.pg)(r,"axis","hover",String(c));t.dispatch((0,m.o4)({active:!0,activeIndex:c.toString(),activeDataKey:void 0,activeCoordinate:l}))}}else{var u=(0,k.pg)(r,"axis","hover",String(n.index));t.dispatch((0,m.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:u}))}}}}}),we.startListening({actionCreator:be,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=(0,k.pg)(r,"axis","hover",String("0"));t.dispatch((0,m.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var xe=(0,g.VP)("externalEvent"),Oe=(0,g.Nc)();Oe.startListening({actionCreator:xe,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,E.eE)(r),activeDataKey:(0,E.Xb)(r),activeIndex:(0,E.A2)(r),activeLabel:(0,E.BZ)(r),activeTooltipIndex:(0,E.A2)(r),isTooltipActive:(0,E.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}});var Pe=r(4779),Ae=r(7591),je=r(6096),Se=(0,S.Mz)([je.J],e=>e.tooltipItemPayloads),Me=(0,S.Mz)([Se,Ae.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),Ee=(0,g.VP)("touchMove"),_e=(0,g.Nc)();_e.startListening({actionCreator:Ee,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=(0,D.au)(n,n.tooltip.settings.shared);if("axis"===i){var a=T(n,N({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));null!=(null===a||void 0===a?void 0:a.activeIndex)&&t.dispatch((0,m.Nt)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,c=r.touches[0],l=document.elementFromPoint(c.clientX,c.clientY);if(!l||!l.getAttribute)return;var u=l.getAttribute(Pe.F0),s=null!==(o=l.getAttribute(Pe.um))&&void 0!==o?o:void 0,f=Me(t.getState(),u,s);t.dispatch((0,m.RD)({activeDataKey:s,activeIndex:u,activeCoordinate:f}))}}});var ke=(0,v.HY)({brush:ee,cartesianAxis:$.CA,chartData:b.LV,graphicalItems:U.iZ,layout:j,legend:te.CU,options:i.lJ,polarAxis:de,polarOptions:ye,referenceElements:Y,rootProps:ie,tooltip:m.En}),Ce=r(3987),Te=r(7250);function De(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,Ce.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,g.U1)({reducer:ke,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([z.middleware,L.middleware,we.middleware,Oe.middleware,_e.middleware]),devTools:{serialize:{replacer:B},name:"recharts-".concat(t)}})}(t,i));var c=Te.E;return n.createElement(y,{context:c,store:o.current},r)}var Ne=r(787),Ie=e=>{var{chartData:t}=e,r=(0,Ne.j)(),i=(0,Ce.r)();return(0,n.useEffect)(()=>i?()=>{}:(r((0,b.hq)(t)),()=>{r((0,b.hq)(void 0))}),[t,r,i]),null};function ze(e){var{layout:t,width:r,height:i,margin:a}=e,o=(0,Ne.j)(),c=(0,Ce.r)();return(0,n.useEffect)(()=>{c||(o(O(t)),o(P({width:r,height:i})),o(x(a)))},[o,c,t,r,i,a]),null}function Re(e){var t=(0,Ne.j)();return(0,n.useEffect)(()=>{t(ae(e))},[t,e]),null}var Le=r(240),Be=r(9949),$e=r(4794),Ue=r(7886),Fe=r(2574),Ke=["children"];function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},He.apply(null,arguments)}var Ge={width:"100%",height:"100%"},We=(0,n.forwardRef)((e,t)=>{var r=(0,M.yi)(),i=(0,M.rY)(),a=(0,Be.$)();if(!(0,Fe.F)(r)||!(0,Fe.F)(i))return null;var o,c,{children:l,otherAttributes:u,title:s,desc:f}=e;return o="number"===typeof u.tabIndex?u.tabIndex:a?0:void 0,c="string"===typeof u.role?u.role:a?"application":void 0,n.createElement($e.u,He({},u,{title:s,desc:f,role:c,tabIndex:o,width:r,height:i,style:Ge,ref:t}),l)}),Ve=e=>{var{children:t}=e,r=(0,Ne.G)(Ue.U);if(!r)return null;var{width:i,height:a,y:o,x:c}=r;return n.createElement($e.u,{width:i,height:a,x:c,y:o},t)},qe=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ke);return(0,Ce.r)()?n.createElement(Ve,null,r):n.createElement(We,He({ref:t},i),r)}),Ze=r(8387),Ye=r(425),Xe=r(3859);var Je=r(317),Qe=r(4483);function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(t){rt(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var nt=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:c,onDoubleClick:l,onMouseDown:u,onMouseEnter:s,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:g,width:b}=e,w=(0,Ne.j)(),[x,O]=(0,n.useState)(null),[P,j]=(0,n.useState)(null);(0,Ye.l3)();var S=function(){var e=(0,Ne.j)(),[t,r]=(0,n.useState)(null),i=(0,Ne.G)(Xe.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,Fe.H)(r)&&r!==i&&e(A(r))}},[t,e,i]),r}(),M=(0,n.useCallback)(e=>{S(e),"function"===typeof t&&t(e),O(e),j(e)},[S,t,O,j]),E=(0,n.useCallback)(e=>{w(I(e)),w(xe({handler:o,reactEvent:e}))},[w,o]),_=(0,n.useCallback)(e=>{w(R(e)),w(xe({handler:s,reactEvent:e}))},[w,s]),k=(0,n.useCallback)(e=>{w((0,m.xS)()),w(xe({handler:f,reactEvent:e}))},[w,f]),C=(0,n.useCallback)(e=>{w(R(e)),w(xe({handler:d,reactEvent:e}))},[w,d]),T=(0,n.useCallback)(()=>{w(be())},[w]),D=(0,n.useCallback)(e=>{w(me(e.key))},[w]),N=(0,n.useCallback)(e=>{w(xe({handler:c,reactEvent:e}))},[w,c]),z=(0,n.useCallback)(e=>{w(xe({handler:l,reactEvent:e}))},[w,l]),L=(0,n.useCallback)(e=>{w(xe({handler:u,reactEvent:e}))},[w,u]),B=(0,n.useCallback)(e=>{w(xe({handler:h,reactEvent:e}))},[w,h]),$=(0,n.useCallback)(e=>{w(xe({handler:v,reactEvent:e}))},[w,v]),U=(0,n.useCallback)(e=>{w(Ee(e)),w(xe({handler:y,reactEvent:e}))},[w,y]),F=(0,n.useCallback)(e=>{w(xe({handler:p,reactEvent:e}))},[w,p]);return n.createElement(Je.$.Provider,{value:x},n.createElement(Qe.t.Provider,{value:P},n.createElement("div",{className:(0,Ze.$)("recharts-wrapper",i),style:tt({position:"relative",cursor:"default",width:b,height:a},g),role:"application",onClick:E,onContextMenu:N,onDoubleClick:z,onFocus:T,onKeyDown:D,onMouseDown:L,onMouseEnter:_,onMouseLeave:k,onMouseMove:C,onMouseUp:B,onTouchEnd:F,onTouchMove:U,onTouchStart:$,ref:M},r)))}),it=r(6307),at=(0,n.createContext)(void 0),ot=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,it.NF)("recharts"),"-clip")),i=(0,M.hj)();if(null==i)return null;var{left:a,top:o,height:c,width:l}=i;return n.createElement(at.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:c,width:l}))),t)},ct=["children","className","width","height","style","compact","title","desc"];var lt=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,width:a,height:o,style:c,compact:l,title:u,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ct),d=(0,Le.J9)(f,!1);return l?n.createElement(qe,{otherAttributes:d,title:u,desc:s},r):n.createElement(nt,{className:i,style:c,width:a,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(qe,{otherAttributes:d,title:u,desc:s,ref:t},n.createElement(ot,null,r)))}),ut=r(6371),st=["width","height"];function ft(){return ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ft.apply(null,arguments)}var dt={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},ht=(0,n.forwardRef)(function(e,t){var r,i=(0,ut.e)(e.categoricalChartProps,dt),{width:a,height:o}=i,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,st);if(!(0,Fe.F)(a)||!(0,Fe.F)(o))return null;var{chartName:l,defaultTooltipEventType:u,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,categoricalChartProps:d}=e,h={chartName:l,defaultTooltipEventType:u,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,eventEmitter:void 0};return n.createElement(De,{preloadedState:{options:h},reduxStoreName:null!==(r=d.id)&&void 0!==r?r:l},n.createElement(Ie,{chartData:d.data}),n.createElement(ze,{width:a,height:o,layout:i.layout,margin:i.margin}),n.createElement(Re,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(lt,ft({},c,{width:a,height:o,ref:t})))}),pt=["axis"],yt=(0,n.forwardRef)((e,t)=>n.createElement(ht,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:pt,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},3272:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"===typeof e||"symbol"===typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},3374:(e,t,r)=>{"use strict";r.d(t,{E:()=>u,O:()=>s});var n=r(787),i=r(4804),a=r(2711);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){l(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function l(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var u=()=>(0,n.G)(a.Dn),s=()=>{var e=u(),t=(0,n.G)(a.R4),r=(0,n.G)(a.fl);return(0,i.Hj)(c(c({},e),{},{scale:r}),t)}},3757:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},3799:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4704),i=r(1203),a=r(870),o=r(9972);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"===typeof t&&i.isArrayLike(r)&&n.isIndex(t)&&t<r.length||"string"===typeof t&&t in r)&&o.eq(r[t],e))}},3808:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8268),i=r(5261);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},3809:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},3821:(e,t,r)=>{e.exports=r(2204).sortBy},3859:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},3923:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>u,Qd:()=>c,Tw:()=>f,Zz:()=>s,ve:()=>d,y$:()=>l});var i=(()=>"function"===typeof Symbol&&Symbol.observable||"@@observable")(),a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function c(e){if("object"!==typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function l(e,t,r){if("function"!==typeof e)throw new Error(n(2));if("function"===typeof t&&"function"===typeof r||"function"===typeof r&&"function"===typeof arguments[3])throw new Error(n(0));if("function"===typeof t&&"undefined"===typeof r&&(r=t,t=void 0),"undefined"!==typeof r){if("function"!==typeof r)throw new Error(n(1));return r(l)(e,t)}let a=e,u=t,s=new Map,f=s,d=0,h=!1;function p(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function y(){if(h)throw new Error(n(3));return u}function v(e){if("function"!==typeof e)throw new Error(n(4));if(h)throw new Error(n(5));let t=!0;p();const r=d++;return f.set(r,e),function(){if(t){if(h)throw new Error(n(6));t=!1,p(),f.delete(r),s=null}}}function g(e){if(!c(e))throw new Error(n(7));if("undefined"===typeof e.type)throw new Error(n(8));if("string"!==typeof e.type)throw new Error(n(17));if(h)throw new Error(n(9));try{h=!0,u=a(u,e)}finally{h=!1}return(s=f).forEach(e=>{e()}),e}g({type:o.INIT});return{dispatch:g,subscribe:v,getState:y,replaceReducer:function(e){if("function"!==typeof e)throw new Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){const e=v;return{subscribe(t){if("object"!==typeof t||null===t)throw new Error(n(11));function r(){const e=t;e.next&&e.next(y())}r();return{unsubscribe:e(r)}},[i](){return this}}}}}function u(e){const t=Object.keys(e),r={};for(let n=0;n<t.length;n++){const i=t[n];0,"function"===typeof e[i]&&(r[i]=e[i])}const i=Object.keys(r);let a;try{!function(e){Object.keys(e).forEach(t=>{const r=e[t];if("undefined"===typeof r(void 0,{type:o.INIT}))throw new Error(n(12));if("undefined"===typeof r(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw new Error(n(13))})}(r)}catch(c){a=c}return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(a)throw a;let o=!1;const c={};for(let a=0;a<i.length;a++){const l=i[a],u=r[l],s=e[l],f=u(s,t);if("undefined"===typeof f){t&&t.type;throw new Error(n(14))}c[l]=f,o=o||f!==s}return o=o||i.length!==Object.keys(e).length,o?c:e}}function s(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?e=>e:1===t.length?t[0]:t.reduce((e,t)=>function(){return e(t(...arguments))})}function f(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>(r,i)=>{const a=e(r,i);let o=()=>{throw new Error(n(15))};const c={getState:a.getState,dispatch:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return o(e,...r)}},l=t.map(e=>e(c));return o=s(...l)(a.dispatch),{...a,dispatch:o}}}function d(e){return c(e)&&"type"in e&&"string"===typeof e.type}},3987:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(5043),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},4020:(e,t,r)=>{"use strict";r.d(t,{W:()=>l});var n=r(5043),i=r(8387),a=r(240),o=["children","className"];function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},c.apply(null,arguments)}var l=n.forwardRef((e,t)=>{var{children:r,className:l}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",l);return n.createElement("g",c({className:s},(0,a.J9)(u,!0),{ref:t}),r)})},4208:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){const r=new Map;for(let n=0;n<e.length;n++){const i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},4250:(e,t,r)=>{e.exports=r(8182).isEqual},4332:(e,t,r)=>{e.exports=r(5053).uniqBy},4385:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!==typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},4483:(e,t,r)=>{"use strict";r.d(t,{M:()=>a,t:()=>i});var n=r(5043),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},4704:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_SAFE_INTEGER;switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},4721:(e,t,r)=>{"use strict";r.d(t,{Ds:()=>y,GO:()=>p,c2:()=>v});var n=r(2099),i=r(7770),a=r.n(i),o=r(5807),c=r(4804),l=r(3859),u=r(8184),s=r(4779);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){h(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p=(0,n.Mz)([l.Lp,l.A$,l.HK,e=>e.brush.height,u.h,u.W,o.ff,o.dc],(e,t,r,n,i,o,l,u)=>{var f=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"===typeof t.width?t.width:s.tQ;return d(d({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),h=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:d(d({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=d(d({},h),f),y=p.bottom;p.bottom+=n;var v=e-(p=(0,c.s0)(p,l,u)).left-p.right,g=t-p.top-p.bottom;return d(d({brushBottom:y},p),{},{width:Math.max(v,0),height:Math.max(g,0)})}),y=(0,n.Mz)(p,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),v=(0,n.Mz)(l.Lp,l.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},4779:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},4794:(e,t,r)=>{"use strict";r.d(t,{u:()=>l});var n=r(5043),i=r(8387),a=r(240),o=["children","width","height","viewBox","className","style","title","desc"];function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},c.apply(null,arguments)}var l=(0,n.forwardRef)((e,t)=>{var{children:r,width:l,height:u,viewBox:s,className:f,style:d,title:h,desc:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),v=s||{width:l,height:u,x:0,y:0},g=(0,i.$)("recharts-surface",f);return n.createElement("svg",c({},(0,a.J9)(y,!0,"svg"),{className:g,width:l,height:u,style:d,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:t}),n.createElement("title",null,h),n.createElement("desc",null,p),r)})},4803:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!==typeof e&&"function"!==typeof e}},4804:(e,t,r)=>{"use strict";r.d(t,{qx:()=>T,IH:()=>C,s0:()=>w,gH:()=>b,SW:()=>L,YB:()=>j,bk:()=>R,Hj:()=>D,nb:()=>_,PW:()=>O,Mk:()=>k,$8:()=>E,yy:()=>M,Rh:()=>P,GF:()=>N,uM:()=>I,kr:()=>m,r4:()=>z,_L:()=>x});var n=r(3821),i=r.n(n),a=r(7770),o=r.n(a);function c(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],c=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<c;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var l=r(9236),u=r(3809);function s(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){const t=[];return t.key=e,t}var h=r(6307),p=r(165);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e,t,r){return(0,h.uy)(e)||(0,h.uy)(t)?r:(0,h.vh)(t)?o()(e,t,r):"function"===typeof t?t(e):r}var b=(e,t,r,n,i)=>{var a,o=-1,c=null!==(a=null===t||void 0===t?void 0:t.length)&&void 0!==a?a:0;if(c<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&Math.abs(Math.abs(i[1]-i[0])-360)<=1e-6)for(var l=0;l<c;l++){var u=l>0?r[l-1].coordinate:r[c-1].coordinate,s=r[l].coordinate,f=l>=c-1?r[0].coordinate:r[l+1].coordinate,d=void 0;if((0,h.sA)(s-u)!==(0,h.sA)(f-s)){var p=[];if((0,h.sA)(f-s)===(0,h.sA)(i[1]-i[0])){d=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+u)/2),p[1]=Math.max(y,(y+u)/2)}else{d=u;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>g[0]&&e<=g[1]||e>=p[0]&&e<=p[1]){({index:o}=r[l]);break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[l]);break}}}else if(t)for(var w=0;w<c;w++)if(0===w&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<c-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===c-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:o}=t[w]);break}return o},w=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:c}=t;if(("vertical"===c||"horizontal"===c&&"middle"===o)&&"center"!==a&&(0,h.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===c||"vertical"===c&&"center"===a)&&"middle"!==o&&(0,h.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,O=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},P=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:c,isCategorical:l,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:d,axisType:p}=e;if(!o)return null;var y="scaleBand"===c&&o.bandwidth?o.bandwidth()/2:2,v=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return v="angleAxis"===p&&a&&a.length>=2?2*(0,h.sA)(a[0]-a[1])*v:v,t&&(f||d)?(f||d||[]).map((e,t)=>{var r=n?n.indexOf(e):e;return{coordinate:o(r)+v,value:e,offset:v,index:t}}).filter(e=>!(0,h.M8)(e.coordinate)):l&&u?u.map((e,t)=>({coordinate:o(e)+v,value:e,index:t,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+v,value:e,offset:v,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+v,value:n?n[e]:e,index:t,offset:v}))},A=1e-4,j=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-A,a=Math.max(n[0],n[1])+A,o=e(t[0]),c=e(t[r-1]);(o<i||o>a||c<i||c>a)&&e.domain([t[0],t[r-1]])}},S={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var c=(0,h.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];c>=0?(e[o][r][0]=i,e[o][r][1]=i+c,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+c,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}c(e,t)}},none:c,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}c(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,u=0,s=0;l<i;++l){for(var f=e[t[l]],d=f[o][1]||0,h=(d-(f[o-1][1]||0))/2,p=0;p<l;++p){var y=e[t[p]];h+=(y[o][1]||0)-(y[o-1][1]||0)}u+=d,s+=h*d}r[o-1][1]+=r[o-1][0]=a,u&&(a-=s/u)}r[o-1][1]+=r[o-1][0]=a,c(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,h.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},M=(e,t,r)=>{var n=S[r],i=function(){var e=(0,u.A)([]),t=s,r=c,n=f;function i(i){var a,o,c=Array.from(e.apply(this,arguments),d),u=c.length,s=-1;for(const e of i)for(a=0,++s;a<u;++a)(c[a][s]=[0,+n(e,c[a].key,s,i)]).data=e;for(a=0,o=(0,l.A)(t(c));a<u;++a)c[o[a]].index=a;return r(c,o),c}return i.keys=function(t){return arguments.length?(e="function"===typeof t?t:(0,u.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"===typeof e?e:(0,u.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"===typeof e?e:(0,u.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?c:e,i):r},i}().keys(t).value((e,t)=>+m(e,t,0)).order(s).offset(n);return i(e)};function E(e){return null==e?void 0:String(e)}function _(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!(0,h.uy)(i[t.dataKey])){var c=(0,h.eP)(r,"value",i[t.dataKey]);if(c)return c.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var l=m(i,(0,h.uy)(o)?t.dataKey:o);return(0,h.uy)(l)?null:t.scale(l)}var k=(e,t,r)=>{var n;if(null!=e)return[(n=Object.keys(e).reduce((n,i)=>{var a=e[i],{stackedData:o}=a,c=o.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(h.Et);return[Math.min(...t),Math.max(...t)]})(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(c[0],n[0]),Math.max(c[1],n[1])]},[1/0,-1/0]))[0]===1/0?0:n[0],n[1]===-1/0?0:n[1]]},C=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,T=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,D=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,c=1,l=a.length;c<l;c++){var u=a[c],s=a[c-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function N(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:a})}function I(e,t){return e?String(e):"string"===typeof t?t:void 0}function z(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var R=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var c=i.coordinate,{angle:l}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,c,l)),{},{angle:l,radius:c})}return{x:0,y:0}},L=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},4830:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t){let r,{signal:n,edges:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=null;const o=null!=i&&i.includes("leading"),c=null==i||i.includes("trailing"),l=()=>{null!==a&&(e.apply(r,a),r=void 0,a=null)};let u=null;const s=()=>{null!=u&&clearTimeout(u),u=setTimeout(()=>{u=null,c&&l(),d()},t)},f=()=>{null!==u&&(clearTimeout(u),u=null)},d=()=>{f(),r=void 0,a=null},h=function(){if(n?.aborted)return;r=this;for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];a=t;const c=null==u;s(),o&&c&&l()};return h.schedule=s,h.cancel=d,h.flush=()=>{f(),l()},n?.addEventListener("abort",d,{once:!0}),h}},4879:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6770);t.toFinite=function(e){if(!e)return 0===e?e:0;if((e=n.toNumber(e))===1/0||e===-1/0){return(e<0?-1:1)*Number.MAX_VALUE}return e===e?e:0}},4954:(e,t,r)=>{"use strict";r.d(t,{kz:()=>Ta,fb:()=>Pa,q:()=>Wa,tP:()=>to,g1:()=>lo,iv:()=>No,Nk:()=>xa,pM:()=>ka,Oz:()=>Ha,tF:()=>To,rj:()=>ba,ec:()=>ya,bb:()=>qa,xp:()=>oo,wL:()=>Ja,sr:()=>no,Qn:()=>ao,MK:()=>Ea,IO:()=>ga,P9:()=>La,S5:()=>Ia,PU:()=>na,cd:()=>aa,eo:()=>da,yi:()=>za,ZB:()=>zo,D5:()=>go,iV:()=>bo,Hd:()=>sa,Gx:()=>Bo,_y:()=>Uo,um:()=>fa,gT:()=>$a,Kr:()=>Ra,$X:()=>Fa,Zi:()=>Ro,CR:()=>Lo,ld:()=>ha,L$:()=>_o,Rl:()=>ia,Lw:()=>So,KR:()=>ko,sf:()=>oa,wP:()=>Co});var n={};r.r(n),r.d(n,{scaleBand:()=>v,scaleDiverging:()=>fi,scaleDivergingLog:()=>di,scaleDivergingPow:()=>pi,scaleDivergingSqrt:()=>yi,scaleDivergingSymlog:()=>hi,scaleIdentity:()=>it,scaleImplicit:()=>p,scaleLinear:()=>nt,scaleLog:()=>ht,scaleOrdinal:()=>y,scalePoint:()=>m,scalePow:()=>Ot,scaleQuantile:()=>Nt,scaleQuantize:()=>It,scaleRadial:()=>jt,scaleSequential:()=>ii,scaleSequentialLog:()=>ai,scaleSequentialPow:()=>ci,scaleSequentialQuantile:()=>ui,scaleSequentialSqrt:()=>li,scaleSequentialSymlog:()=>oi,scaleSqrt:()=>Pt,scaleSymlog:()=>gt,scaleThreshold:()=>zt,scaleTime:()=>ei,scaleUtc:()=>ti,tickFormat:()=>tt});var i=r(2099),a=r(9859),o=r.n(a);function c(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function l(e,t){switch(arguments.length){case 0:break;case 1:"function"===typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"===typeof t?this.interpolator(t):this.range(t)}return this}class u extends Map{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:h;if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[r,n]of e)this.set(r,n)}get(e){return super.get(s(this,e))}has(e){return super.has(s(this,e))}set(e,t){return super.set(f(this,e),t)}delete(e){return super.delete(d(this,e))}}Set;function s(e,t){let{_intern:r,_key:n}=e;const i=n(t);return r.has(i)?r.get(i):t}function f(e,t){let{_intern:r,_key:n}=e;const i=n(t);return r.has(i)?r.get(i):(r.set(i,t),t)}function d(e,t){let{_intern:r,_key:n}=e;const i=n(t);return r.has(i)&&(t=r.get(i),r.delete(i)),t}function h(e){return null!==e&&"object"===typeof e?e.valueOf():e}const p=Symbol("implicit");function y(){var e=new u,t=[],r=[],n=p;function i(i){let a=e.get(i);if(void 0===a){if(n!==p)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();t=[],e=new u;for(const n of r)e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return y(t,r).unknown(n)},c.apply(i,arguments),i}function v(){var e,t,r=y().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,s=0,f=.5;function d(){var r=n().length,c=o<a,d=c?o:a,h=c?a:o;e=(h-d)/Math.max(1,r-u+2*s),l&&(e=Math.floor(e)),d+=(h-d-e*(r-u))*f,t=e*(1-u),l&&(d=Math.round(d),t=Math.round(t));var p=function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=new Array(i);++n<i;)a[n]=e+n*r;return a}(r).map(function(t){return d+e*t});return i(c?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),d()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,d()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,l=!0,d()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,d()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,s=+e),d()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),d()):u},r.paddingOuter=function(e){return arguments.length?(s=+e,d()):s},r.align=function(e){return arguments.length?(f=Math.max(0,Math.min(1,e)),d()):f},r.copy=function(){return v(n(),[a,o]).round(l).paddingInner(u).paddingOuter(s).align(f)},c.apply(d(),arguments)}function g(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return g(t())},e}function m(){return g(v.apply(null,arguments).paddingInner(1))}const b=Math.sqrt(50),w=Math.sqrt(10),x=Math.sqrt(2);function O(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=b?10:a>=w?5:a>=x?2:1;let c,l,u;return i<0?(u=Math.pow(10,-i)/o,c=Math.round(e*u),l=Math.round(t*u),c/u<e&&++c,l/u>t&&--l,u=-u):(u=Math.pow(10,i)*o,c=Math.round(e/u),l=Math.round(t/u),c*u<e&&++c,l*u>t&&--l),l<c&&.5<=r&&r<2?O(e,t,2*r):[c,l,u]}function P(e,t,r){if(!((r=+r)>0))return[];if((e=+e)===(t=+t))return[e];const n=t<e,[i,a,o]=n?O(t,e,r):O(e,t,r);if(!(a>=i))return[];const c=a-i+1,l=new Array(c);if(n)if(o<0)for(let u=0;u<c;++u)l[u]=(a-u)/-o;else for(let u=0;u<c;++u)l[u]=(a-u)*o;else if(o<0)for(let u=0;u<c;++u)l[u]=(i+u)/-o;else for(let u=0;u<c;++u)l[u]=(i+u)*o;return l}function A(e,t,r){return O(e=+e,t=+t,r=+r)[2]}function j(e,t,r){r=+r;const n=(t=+t)<(e=+e),i=n?A(t,e,r):A(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function S(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function M(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function E(e){let t,r,n;function i(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length;if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<0?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=S,r=(t,r)=>S(e(t),r),n=(t,r)=>e(t)-r):(t=e===S||e===M?e:_,r=e,n=e),{left:i,center:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const a=i(e,t,r,(arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length)-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length;if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<=0?i=t+1:a=t}while(i<a)}return i}}}function _(){return 0}function k(e){return null===e?NaN:+e}const C=E(S),T=C.right,D=(C.left,E(k).center,T);function N(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function I(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function z(){}var R=.7,L=1/R,B="\\s*([+-]?\\d+)\\s*",$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",U="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",F=/^#([0-9a-f]{3,8})$/,K=new RegExp(`^rgb\\(${B},${B},${B}\\)$`),H=new RegExp(`^rgb\\(${U},${U},${U}\\)$`),G=new RegExp(`^rgba\\(${B},${B},${B},${$}\\)$`),W=new RegExp(`^rgba\\(${U},${U},${U},${$}\\)$`),V=new RegExp(`^hsl\\(${$},${U},${U}\\)$`),q=new RegExp(`^hsla\\(${$},${U},${U},${$}\\)$`),Z={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Y(){return this.rgb().formatHex()}function X(){return this.rgb().formatRgb()}function J(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=F.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?Q(t):3===r?new re(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?ee(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?ee(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=K.exec(e))?new re(t[1],t[2],t[3],1):(t=H.exec(e))?new re(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=G.exec(e))?ee(t[1],t[2],t[3],t[4]):(t=W.exec(e))?ee(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=V.exec(e))?le(t[1],t[2]/100,t[3]/100,1):(t=q.exec(e))?le(t[1],t[2]/100,t[3]/100,t[4]):Z.hasOwnProperty(e)?Q(Z[e]):"transparent"===e?new re(NaN,NaN,NaN,0):null}function Q(e){return new re(e>>16&255,e>>8&255,255&e,1)}function ee(e,t,r,n){return n<=0&&(e=t=r=NaN),new re(e,t,r,n)}function te(e,t,r,n){return 1===arguments.length?((i=e)instanceof z||(i=J(i)),i?new re((i=i.rgb()).r,i.g,i.b,i.opacity):new re):new re(e,t,r,null==n?1:n);var i}function re(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ne(){return`#${ce(this.r)}${ce(this.g)}${ce(this.b)}`}function ie(){const e=ae(this.opacity);return`${1===e?"rgb(":"rgba("}${oe(this.r)}, ${oe(this.g)}, ${oe(this.b)}${1===e?")":`, ${e})`}`}function ae(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function oe(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ce(e){return((e=oe(e))<16?"0":"")+e.toString(16)}function le(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new se(e,t,r,n)}function ue(e){if(e instanceof se)return new se(e.h,e.s,e.l,e.opacity);if(e instanceof z||(e=J(e)),!e)return new se;if(e instanceof se)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,c=a-i,l=(a+i)/2;return c?(o=t===a?(r-n)/c+6*(r<n):r===a?(n-t)/c+2:(t-r)/c+4,c/=l<.5?a+i:2-a-i,o*=60):c=l>0&&l<1?0:o,new se(o,c,l,e.opacity)}function se(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function fe(e){return(e=(e||0)%360)<0?e+360:e}function de(e){return Math.max(0,Math.min(1,e||0))}function he(e,t,r){return 255*(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)}function pe(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}N(z,J,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Y,formatHex:Y,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ue(this).formatHsl()},formatRgb:X,toString:X}),N(re,te,I(z,{brighter(e){return e=null==e?L:Math.pow(L,e),new re(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?R:Math.pow(R,e),new re(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new re(oe(this.r),oe(this.g),oe(this.b),ae(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ne,formatHex:ne,formatHex8:function(){return`#${ce(this.r)}${ce(this.g)}${ce(this.b)}${ce(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:ie,toString:ie})),N(se,function(e,t,r,n){return 1===arguments.length?ue(e):new se(e,t,r,null==n?1:n)},I(z,{brighter(e){return e=null==e?L:Math.pow(L,e),new se(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?R:Math.pow(R,e),new se(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new re(he(e>=240?e-240:e+120,i,n),he(e,i,n),he(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new se(fe(this.h),de(this.s),de(this.l),ae(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ae(this.opacity);return`${1===e?"hsl(":"hsla("}${fe(this.h)}, ${100*de(this.s)}%, ${100*de(this.l)}%${1===e?")":`, ${e})`}`}}));const ye=e=>()=>e;function ve(e,t){return function(r){return e+r*t}}function ge(e){return 1===(e=+e)?me:function(t,r){return r-t?function(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}(t,r,e):ye(isNaN(t)?r:t)}}function me(e,t){var r=t-e;return r?ve(e,r):ye(isNaN(e)?t:e)}const be=function e(t){var r=ge(t);function n(e,t){var n=r((e=te(e)).r,(t=te(t)).r),i=r(e.g,t.g),a=r(e.b,t.b),o=me(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return n.gamma=e,n}(1);function we(e){return function(t){var r,n,i=t.length,a=new Array(i),o=new Array(i),c=new Array(i);for(r=0;r<i;++r)n=te(t[r]),a[r]=n.r||0,o[r]=n.g||0,c[r]=n.b||0;return a=e(a),o=e(o),c=e(c),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=c(e),n+""}}}we(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,c=n<t-1?e[n+2]:2*a-i;return pe((r-n/t)*t,o,i,a,c)}}),we(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],c=e[(n+2)%t];return pe((r-n/t)*t,i,a,o,c)}});function xe(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=new Array(i),o=new Array(n);for(r=0;r<i;++r)a[r]=_e(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}function Oe(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Pe(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function Ae(e,t){var r,n={},i={};for(r in null!==e&&"object"===typeof e||(e={}),null!==t&&"object"===typeof t||(t={}),t)r in e?n[r]=_e(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}var je=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Se=new RegExp(je.source,"g");function Me(e,t){var r,n,i,a=je.lastIndex=Se.lastIndex=0,o=-1,c=[],l=[];for(e+="",t+="";(r=je.exec(e))&&(n=Se.exec(t));)(i=n.index)>a&&(i=t.slice(a,i),c[o]?c[o]+=i:c[++o]=i),(r=r[0])===(n=n[0])?c[o]?c[o]+=n:c[++o]=n:(c[++o]=null,l.push({i:o,x:Pe(r,n)})),a=Se.lastIndex;return a<t.length&&(i=t.slice(a),c[o]?c[o]+=i:c[++o]=i),c.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var r,n=0;n<t;++n)c[(r=l[n]).i]=r.x(e);return c.join("")})}function Ee(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}}function _e(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ye(t):("number"===i?Pe:"string"===i?(r=J(t))?(t=r,be):Me:t instanceof J?be:t instanceof Date?Oe:(n=t,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(t)?xe:"function"!==typeof t.valueOf&&"function"!==typeof t.toString||isNaN(t)?Ae:Pe:Ee))(e,t)}function ke(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function Ce(e){return+e}var Te=[0,1];function De(e){return e}function Ne(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r});var r}function Ie(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Ne(i,n),a=r(o,a)):(n=Ne(n,i),a=r(a,o)),function(e){return a(n(e))}}function ze(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Ne(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=D(e,t,1,n)-1;return a[r](i[r](t))}}function Re(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Le(){var e,t,r,n,i,a,o=Te,c=Te,l=_e,u=De;function s(){var e=Math.min(o.length,c.length);return u!==De&&(u=function(e,t){var r;return e>t&&(r=e,e=t,t=r),function(r){return Math.max(e,Math.min(t,r))}}(o[0],o[e-1])),n=e>2?ze:Ie,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),c,l)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(c,o.map(e),Pe)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,Ce),s()):o.slice()},f.range=function(e){return arguments.length?(c=Array.from(e),s()):c.slice()},f.rangeRound=function(e){return c=Array.from(e),l=ke,s()},f.clamp=function(e){return arguments.length?(u=!!e||De,s()):u!==De},f.interpolate=function(e){return arguments.length?(l=e,s()):l},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function Be(){return Le()(De,De)}var $e,Ue=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Fe(e){if(!(t=Ue.exec(e)))throw new Error("invalid format: "+e);var t;return new Ke({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function Ke(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function He(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Ge(e){return(e=He(Math.abs(e)))?e[1]:NaN}function We(e,t){var r=He(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}Fe.prototype=Ke.prototype,Ke.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Ve={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>We(100*e,t),r:We,s:function(e,t){var r=He(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-($e=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+He(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function qe(e){return e}var Ze,Ye,Xe,Je=Array.prototype.map,Qe=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function et(e){var t,r,n=void 0===e.grouping||void 0===e.thousands?qe:(t=Je.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,c=t[0],l=0;i>0&&c>0&&(l+c+1>n&&(c=Math.max(1,n-l)),a.push(e.substring(i-=c,i+c)),!((l+=c+1)>n));)c=t[o=(o+1)%t.length];return a.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",o=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?qe:function(e){return function(t){return t.replace(/[0-9]/g,function(t){return e[+t]})}}(Je.call(e.numerals,String)),l=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"\u2212":e.minus+"",s=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=Fe(e)).fill,r=e.align,f=e.sign,d=e.symbol,h=e.zero,p=e.width,y=e.comma,v=e.precision,g=e.trim,m=e.type;"n"===m?(y=!0,m="g"):Ve[m]||(void 0===v&&(v=12),g=!0,m="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",w="$"===d?a:/[%p]/.test(m)?l:"",x=Ve[m],O=/[defgprs%]/.test(m);function P(e){var i,a,l,d=b,P=w;if("c"===m)P=x(e)+P,e="";else{var A=(e=+e)<0||1/e<0;if(e=isNaN(e)?s:x(Math.abs(e),v),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),A&&0===+e&&"+"!==f&&(A=!1),d=(A?"("===f?f:u:"-"===f||"("===f?"":f)+d,P=("s"===m?Qe[8+$e/3]:"")+P+(A&&"("===f?")":""),O)for(i=-1,a=e.length;++i<a;)if(48>(l=e.charCodeAt(i))||l>57){P=(46===l?o+e.slice(i+1):e.slice(i))+P,e=e.slice(0,i);break}}y&&!h&&(e=n(e,1/0));var j=d.length+e.length+P.length,S=j<p?new Array(p-j+1).join(t):"";switch(y&&h&&(e=n(S+e,S.length?p-P.length:1/0),S=""),r){case"<":e=d+e+P+S;break;case"=":e=d+S+e+P;break;case"^":e=S.slice(0,j=S.length>>1)+d+e+P+S.slice(j);break;default:e=S+d+e+P}return c(e)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),P.toString=function(){return e+""},P}return{format:f,formatPrefix:function(e,t){var r=f(((e=Fe(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(Ge(t)/3))),i=Math.pow(10,-n),a=Qe[8+n/3];return function(e){return r(i*e)+a}}}}function tt(e,t,r,n){var i,a=j(e,t,r);switch((n=Fe(null==n?",f":n)).type){case"s":var o=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Ge(t)/3)))-Ge(Math.abs(e)))}(a,o))||(n.precision=i),Xe(n,o);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Ge(t)-Ge(e))+1}(a,Math.max(Math.abs(e),Math.abs(t))))||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=function(e){return Math.max(0,-Ge(Math.abs(e)))}(a))||(n.precision=i-2*("%"===n.type))}return Ye(n)}function rt(e){var t=e.domain;return e.ticks=function(e){var r=t();return P(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return tt(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,c=a.length-1,l=a[o],u=a[c],s=10;for(u<l&&(i=l,l=u,u=i,i=o,o=c,c=i);s-- >0;){if((i=A(l,u,r))===n)return a[o]=l,a[c]=u,t(a);if(i>0)l=Math.floor(l/i)*i,u=Math.ceil(u/i)*i;else{if(!(i<0))break;l=Math.ceil(l*i)/i,u=Math.floor(u*i)/i}n=i}return e},e}function nt(){var e=Be();return e.copy=function(){return Re(e,nt())},c.apply(e,arguments),rt(e)}function it(e){var t;function r(e){return null==e||isNaN(e=+e)?t:e}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(e=Array.from(t,Ce),r):e.slice()},r.unknown=function(e){return arguments.length?(t=e,r):t},r.copy=function(){return it(e).unknown(t)},e=arguments.length?Array.from(e,Ce):[0,1],rt(r)}function at(e,t){var r,n=0,i=(e=e.slice()).length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function ot(e){return Math.log(e)}function ct(e){return Math.exp(e)}function lt(e){return-Math.log(-e)}function ut(e){return-Math.exp(-e)}function st(e){return isFinite(e)?+("1e"+e):e<0?0:e}function ft(e){return(t,r)=>-e(-t,r)}function dt(e){const t=e(ot,ct),r=t.domain;let n,i,a=10;function o(){return n=function(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}(a),i=function(e){return 10===e?st:e===Math.E?Math.exp:t=>Math.pow(e,t)}(a),r()[0]<0?(n=ft(n),i=ft(i),e(lt,ut)):e(ot,ct),t}return t.base=function(e){return arguments.length?(a=+e,o()):a},t.domain=function(e){return arguments.length?(r(e),o()):r()},t.ticks=e=>{const t=r();let o=t[0],c=t[t.length-1];const l=c<o;l&&([o,c]=[c,o]);let u,s,f=n(o),d=n(c);const h=null==e?10:+e;let p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),o>0){for(;f<=d;++f)for(u=1;u<a;++u)if(s=f<0?u/i(-f):u*i(f),!(s<o)){if(s>c)break;p.push(s)}}else for(;f<=d;++f)for(u=a-1;u>=1;--u)if(s=f>0?u/i(-f):u*i(f),!(s<o)){if(s>c)break;p.push(s)}2*p.length<h&&(p=P(o,c,h))}else p=P(f,d,Math.min(d-f,h)).map(i);return l?p.reverse():p},t.tickFormat=(e,r)=>{if(null==e&&(e=10),null==r&&(r=10===a?"s":","),"function"!==typeof r&&(a%1||null!=(r=Fe(r)).precision||(r.trim=!0),r=Ye(r)),e===1/0)return r;const o=Math.max(1,a*e/t.ticks().length);return e=>{let t=e/i(Math.round(n(e)));return t*a<a-.5&&(t*=a),t<=o?r(e):""}},t.nice=()=>r(at(r(),{floor:e=>i(Math.floor(n(e))),ceil:e=>i(Math.ceil(n(e)))})),t}function ht(){const e=dt(Le()).domain([1,10]);return e.copy=()=>Re(e,ht()).base(e.base()),c.apply(e,arguments),e}function pt(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function yt(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function vt(e){var t=1,r=e(pt(t),yt(t));return r.constant=function(r){return arguments.length?e(pt(t=+r),yt(t)):t},rt(r)}function gt(){var e=vt(Le());return e.copy=function(){return Re(e,gt()).constant(e.constant())},c.apply(e,arguments)}function mt(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function bt(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function wt(e){return e<0?-e*e:e*e}function xt(e){var t=e(De,De),r=1;return t.exponent=function(t){return arguments.length?1===(r=+t)?e(De,De):.5===r?e(bt,wt):e(mt(r),mt(1/r)):r},rt(t)}function Ot(){var e=xt(Le());return e.copy=function(){return Re(e,Ot()).exponent(e.exponent())},c.apply(e,arguments),e}function Pt(){return Ot.apply(null,arguments).exponent(.5)}function At(e){return Math.sign(e)*e*e}function jt(){var e,t=Be(),r=[0,1],n=!1;function i(r){var i=function(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}(t(r));return isNaN(i)?e:n?Math.round(i):i}return i.invert=function(e){return t.invert(At(e))},i.domain=function(e){return arguments.length?(t.domain(e),i):t.domain()},i.range=function(e){return arguments.length?(t.range((r=Array.from(e,Ce)).map(At)),i):r.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(n=!!e,i):n},i.clamp=function(e){return arguments.length?(t.clamp(e),i):t.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return jt(t.domain(),r).round(n).clamp(t.clamp()).unknown(e)},c.apply(i,arguments),rt(i)}function St(e,t){let r;if(void 0===t)for(const n of e)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function Mt(e,t){let r;if(void 0===t)for(const n of e)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function Et(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S;if(e===S)return _t;if("function"!==typeof e)throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}function _t(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function kt(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/0,i=arguments.length>4?arguments[4]:void 0;if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=void 0===i?_t:Et(i);n>r;){if(n-r>600){const a=n-r+1,o=t-r+1,c=Math.log(a),l=.5*Math.exp(2*c/3),u=.5*Math.sqrt(c*l*(a-l)/a)*(o-a/2<0?-1:1);kt(e,t,Math.max(r,Math.floor(t-o*l/a+u)),Math.min(n,Math.floor(t+(a-o)*l/a+u)),i)}const a=e[t];let o=r,c=n;for(Ct(e,r,t),i(e[n],a)>0&&Ct(e,r,n);o<c;){for(Ct(e,o,c),++o,--c;i(e[o],a)<0;)++o;for(;i(e[c],a)>0;)--c}0===i(e[r],a)?Ct(e,r,c):(++c,Ct(e,c,n)),c<=t&&(r=c+1),t<=c&&(n=c-1)}return e}function Ct(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function Tt(e,t,r){if(e=Float64Array.from(function*(e,t){if(void 0===t)for(let r of e)null!=r&&(r=+r)>=r&&(yield r);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,r)),(n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return Mt(e);if(t>=1)return St(e);var n,i=(n-1)*t,a=Math.floor(i),o=St(kt(e,a).subarray(0,a+1));return o+(Mt(e.subarray(a+1))-o)*(i-a)}}function Dt(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:k;if((n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}function Nt(){var e,t=[],r=[],n=[];function i(){var e=0,i=Math.max(1,r.length);for(n=new Array(i-1);++e<i;)n[e-1]=Dt(t,e/i);return a}function a(t){return null==t||isNaN(t=+t)?e:r[D(n,t)]}return a.invertExtent=function(e){var i=r.indexOf(e);return i<0?[NaN,NaN]:[i>0?n[i-1]:t[0],i<n.length?n[i]:t[t.length-1]]},a.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(S),i()},a.range=function(e){return arguments.length?(r=Array.from(e),i()):r.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return n.slice()},a.copy=function(){return Nt().domain(t).range(r).unknown(e)},c.apply(a,arguments)}function It(){var e,t=0,r=1,n=1,i=[.5],a=[0,1];function o(t){return null!=t&&t<=t?a[D(i,t,0,n)]:e}function l(){var e=-1;for(i=new Array(n);++e<n;)i[e]=((e+1)*r-(e-n)*t)/(n+1);return o}return o.domain=function(e){return arguments.length?([t,r]=e,t=+t,r=+r,l()):[t,r]},o.range=function(e){return arguments.length?(n=(a=Array.from(e)).length-1,l()):a.slice()},o.invertExtent=function(e){var o=a.indexOf(e);return o<0?[NaN,NaN]:o<1?[t,i[0]]:o>=n?[i[n-1],r]:[i[o-1],i[o]]},o.unknown=function(t){return arguments.length?(e=t,o):o},o.thresholds=function(){return i.slice()},o.copy=function(){return It().domain([t,r]).range(a).unknown(e)},c.apply(rt(o),arguments)}function zt(){var e,t=[.5],r=[0,1],n=1;function i(i){return null!=i&&i<=i?r[D(t,i,0,n)]:e}return i.domain=function(e){return arguments.length?(t=Array.from(e),n=Math.min(t.length,r.length-1),i):t.slice()},i.range=function(e){return arguments.length?(r=Array.from(e),n=Math.min(t.length,r.length-1),i):r.slice()},i.invertExtent=function(e){var n=r.indexOf(e);return[t[n-1],t[n]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return zt().domain(t).range(r).unknown(e)},c.apply(i,arguments)}Ze=et({thousands:",",grouping:[3],currency:["$",""]}),Ye=Ze.format,Xe=Ze.formatPrefix;const Rt=1e3,Lt=6e4,Bt=36e5,$t=864e5,Ut=6048e5,Ft=2592e6,Kt=31536e6,Ht=new Date,Gt=new Date;function Wt(e,t,r,n){function i(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{const t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{const o=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return o;let c;do{o.push(c=new Date(+r)),t(r,a),e(r)}while(c<r&&r<n);return o},i.filter=r=>Wt(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(Ht.setTime(+t),Gt.setTime(+n),e(Ht),e(Gt),Math.floor(r(Ht,Gt))),i.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?i.filter(n?t=>n(t)%e===0:t=>i.count(0,t)%e===0):i:null)),i}const Vt=Wt(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Vt.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?Wt(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Vt:null);Vt.range;const qt=Wt(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*Rt)},(e,t)=>(t-e)/Rt,e=>e.getUTCSeconds()),Zt=(qt.range,Wt(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Rt)},(e,t)=>{e.setTime(+e+t*Lt)},(e,t)=>(t-e)/Lt,e=>e.getMinutes())),Yt=(Zt.range,Wt(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Lt)},(e,t)=>(t-e)/Lt,e=>e.getUTCMinutes())),Xt=(Yt.range,Wt(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Rt-e.getMinutes()*Lt)},(e,t)=>{e.setTime(+e+t*Bt)},(e,t)=>(t-e)/Bt,e=>e.getHours())),Jt=(Xt.range,Wt(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Bt)},(e,t)=>(t-e)/Bt,e=>e.getUTCHours())),Qt=(Jt.range,Wt(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Lt)/$t,e=>e.getDate()-1)),er=(Qt.range,Wt(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/$t,e=>e.getUTCDate()-1)),tr=(er.range,Wt(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/$t,e=>Math.floor(e/$t)));tr.range;function rr(e){return Wt(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Lt)/Ut)}const nr=rr(0),ir=rr(1),ar=rr(2),or=rr(3),cr=rr(4),lr=rr(5),ur=rr(6);nr.range,ir.range,ar.range,or.range,cr.range,lr.range,ur.range;function sr(e){return Wt(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/Ut)}const fr=sr(0),dr=sr(1),hr=sr(2),pr=sr(3),yr=sr(4),vr=sr(5),gr=sr(6),mr=(fr.range,dr.range,hr.range,pr.range,yr.range,vr.range,gr.range,Wt(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear()),e=>e.getMonth())),br=(mr.range,Wt(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear()),e=>e.getUTCMonth())),wr=(br.range,Wt(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear()));wr.every=e=>isFinite(e=Math.floor(e))&&e>0?Wt(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null;wr.range;const xr=Wt(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());xr.every=e=>isFinite(e=Math.floor(e))&&e>0?Wt(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null;xr.range;function Or(e,t,r,n,i,a){const o=[[qt,1,Rt],[qt,5,5e3],[qt,15,15e3],[qt,30,3e4],[a,1,Lt],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,Bt],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,$t],[n,2,1728e5],[r,1,Ut],[t,1,Ft],[t,3,7776e6],[e,1,Kt]];function c(t,r,n){const i=Math.abs(r-t)/n,a=E(e=>{let[,,t]=e;return t}).right(o,i);if(a===o.length)return e.every(j(t/Kt,r/Kt,n));if(0===a)return Vt.every(Math.max(j(t,r,n),1));const[c,l]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return c.every(l)}return[function(e,t,r){const n=t<e;n&&([e,t]=[t,e]);const i=r&&"function"===typeof r.range?r:c(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},c]}const[Pr,Ar]=Or(xr,br,fr,tr,Jt,Yt),[jr,Sr]=Or(wr,mr,nr,Qt,Xt,Zt);function Mr(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Er(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function _r(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var kr,Cr,Tr,Dr={"-":"",_:" ",0:"0"},Nr=/^\s*\d+/,Ir=/^%/,zr=/[\\^$*+?|[\]().{}]/g;function Rr(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Lr(e){return e.replace(zr,"\\$&")}function Br(e){return new RegExp("^(?:"+e.map(Lr).join("|")+")","i")}function $r(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function Ur(e,t,r){var n=Nr.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function Fr(e,t,r){var n=Nr.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function Kr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Hr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function Gr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Wr(e,t,r){var n=Nr.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Vr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function qr(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Zr(e,t,r){var n=Nr.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function Yr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Xr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function Jr(e,t,r){var n=Nr.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Qr(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function en(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function tn(e,t,r){var n=Nr.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=Nr.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function nn(e,t,r){var n=Nr.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function an(e,t,r){var n=Ir.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function on(e,t,r){var n=Nr.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function cn(e,t,r){var n=Nr.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ln(e,t){return Rr(e.getDate(),t,2)}function un(e,t){return Rr(e.getHours(),t,2)}function sn(e,t){return Rr(e.getHours()%12||12,t,2)}function fn(e,t){return Rr(1+Qt.count(wr(e),e),t,3)}function dn(e,t){return Rr(e.getMilliseconds(),t,3)}function hn(e,t){return dn(e,t)+"000"}function pn(e,t){return Rr(e.getMonth()+1,t,2)}function yn(e,t){return Rr(e.getMinutes(),t,2)}function vn(e,t){return Rr(e.getSeconds(),t,2)}function gn(e){var t=e.getDay();return 0===t?7:t}function mn(e,t){return Rr(nr.count(wr(e)-1,e),t,2)}function bn(e){var t=e.getDay();return t>=4||0===t?cr(e):cr.ceil(e)}function wn(e,t){return e=bn(e),Rr(cr.count(wr(e),e)+(4===wr(e).getDay()),t,2)}function xn(e){return e.getDay()}function On(e,t){return Rr(ir.count(wr(e)-1,e),t,2)}function Pn(e,t){return Rr(e.getFullYear()%100,t,2)}function An(e,t){return Rr((e=bn(e)).getFullYear()%100,t,2)}function jn(e,t){return Rr(e.getFullYear()%1e4,t,4)}function Sn(e,t){var r=e.getDay();return Rr((e=r>=4||0===r?cr(e):cr.ceil(e)).getFullYear()%1e4,t,4)}function Mn(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Rr(t/60|0,"0",2)+Rr(t%60,"0",2)}function En(e,t){return Rr(e.getUTCDate(),t,2)}function _n(e,t){return Rr(e.getUTCHours(),t,2)}function kn(e,t){return Rr(e.getUTCHours()%12||12,t,2)}function Cn(e,t){return Rr(1+er.count(xr(e),e),t,3)}function Tn(e,t){return Rr(e.getUTCMilliseconds(),t,3)}function Dn(e,t){return Tn(e,t)+"000"}function Nn(e,t){return Rr(e.getUTCMonth()+1,t,2)}function In(e,t){return Rr(e.getUTCMinutes(),t,2)}function zn(e,t){return Rr(e.getUTCSeconds(),t,2)}function Rn(e){var t=e.getUTCDay();return 0===t?7:t}function Ln(e,t){return Rr(fr.count(xr(e)-1,e),t,2)}function Bn(e){var t=e.getUTCDay();return t>=4||0===t?yr(e):yr.ceil(e)}function $n(e,t){return e=Bn(e),Rr(yr.count(xr(e),e)+(4===xr(e).getUTCDay()),t,2)}function Un(e){return e.getUTCDay()}function Fn(e,t){return Rr(dr.count(xr(e)-1,e),t,2)}function Kn(e,t){return Rr(e.getUTCFullYear()%100,t,2)}function Hn(e,t){return Rr((e=Bn(e)).getUTCFullYear()%100,t,2)}function Gn(e,t){return Rr(e.getUTCFullYear()%1e4,t,4)}function Wn(e,t){var r=e.getUTCDay();return Rr((e=r>=4||0===r?yr(e):yr.ceil(e)).getUTCFullYear()%1e4,t,4)}function Vn(){return"+0000"}function qn(){return"%"}function Zn(e){return+e}function Yn(e){return Math.floor(+e/1e3)}function Xn(e){return new Date(e)}function Jn(e){return e instanceof Date?+e:+new Date(+e)}function Qn(e,t,r,n,i,a,o,c,l,u){var s=Be(),f=s.invert,d=s.domain,h=u(".%L"),p=u(":%S"),y=u("%I:%M"),v=u("%I %p"),g=u("%a %d"),m=u("%b %d"),b=u("%B"),w=u("%Y");function x(e){return(l(e)<e?h:c(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:w)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,Jn)):d().map(Xn)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?x:u(t)},s.nice=function(e){var r=d();return e&&"function"===typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(at(r,e)):s},s.copy=function(){return Re(s,Qn(e,t,r,n,i,a,o,c,l,u))},s}function ei(){return c.apply(Qn(jr,Sr,wr,mr,nr,Qt,Xt,Zt,qt,Cr).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function ti(){return c.apply(Qn(Pr,Ar,xr,br,fr,er,Jt,Yt,qt,Tr).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ri(){var e,t,r,n,i,a=0,o=1,c=De,l=!1;function u(t){return null==t||isNaN(t=+t)?i:c(0===r?.5:(t=(n(t)-e)*r,l?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,c=e(r,n),u):[c(0),c(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(l=!!e,u):l},u.interpolator=function(e){return arguments.length?(c=e,u):c},u.range=s(_e),u.rangeRound=s(ke),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function ni(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function ii(){var e=rt(ri()(De));return e.copy=function(){return ni(e,ii())},l.apply(e,arguments)}function ai(){var e=dt(ri()).domain([1,10]);return e.copy=function(){return ni(e,ai()).base(e.base())},l.apply(e,arguments)}function oi(){var e=vt(ri());return e.copy=function(){return ni(e,oi()).constant(e.constant())},l.apply(e,arguments)}function ci(){var e=xt(ri());return e.copy=function(){return ni(e,ci()).exponent(e.exponent())},l.apply(e,arguments)}function li(){return ci.apply(null,arguments).exponent(.5)}function ui(){var e=[],t=De;function r(r){if(null!=r&&!isNaN(r=+r))return t((D(e,r,1)-1)/(e.length-1))}return r.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(S),r},r.interpolator=function(e){return arguments.length?(t=e,r):t},r.range=function(){return e.map((r,n)=>t(n/(e.length-1)))},r.quantiles=function(t){return Array.from({length:t+1},(r,n)=>Tt(e,n/t))},r.copy=function(){return ui(t).domain(e)},l.apply(r,arguments)}function si(){var e,t,r,n,i,a,o,c=0,l=.5,u=1,s=1,f=De,d=!1;function h(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=_e);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([c,l,u]=o,e=a(c=+c),t=a(l=+l),r=a(u=+u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h):[c,l,u]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(_e),h.rangeRound=p(ke),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(c),t=o(l),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h}}function fi(){var e=rt(si()(De));return e.copy=function(){return ni(e,fi())},l.apply(e,arguments)}function di(){var e=dt(si()).domain([.1,1,10]);return e.copy=function(){return ni(e,di()).base(e.base())},l.apply(e,arguments)}function hi(){var e=vt(si());return e.copy=function(){return ni(e,hi()).constant(e.constant())},l.apply(e,arguments)}function pi(){var e=xt(si());return e.copy=function(){return ni(e,pi()).exponent(e.exponent())},l.apply(e,arguments)}function yi(){return pi.apply(null,arguments).exponent(.5)}!function(e){kr=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,c=e.months,l=e.shortMonths,u=Br(i),s=$r(i),f=Br(a),d=$r(a),h=Br(o),p=$r(o),y=Br(c),v=$r(c),g=Br(l),m=$r(l),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return l[e.getMonth()]},B:function(e){return c[e.getMonth()]},c:null,d:ln,e:ln,f:hn,g:An,G:Sn,H:un,I:sn,j:fn,L:dn,m:pn,M:yn,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:Zn,s:Yn,S:vn,u:gn,U:mn,V:wn,w:xn,W:On,x:null,X:null,y:Pn,Y:jn,Z:Mn,"%":qn},w={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return l[e.getUTCMonth()]},B:function(e){return c[e.getUTCMonth()]},c:null,d:En,e:En,f:Dn,g:Hn,G:Wn,H:_n,I:kn,j:Cn,L:Tn,m:Nn,M:In,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:Zn,s:Yn,S:zn,u:Rn,U:Ln,V:$n,w:Un,W:Fn,x:null,X:null,y:Kn,Y:Gn,Z:Vn,"%":qn},x={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return A(e,t,r,n)},d:Xr,e:Xr,f:nn,g:Vr,G:Wr,H:Qr,I:Qr,j:Jr,L:rn,m:Yr,M:en,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:Zr,Q:on,s:cn,S:tn,u:Fr,U:Kr,V:Hr,w:Ur,W:Gr,x:function(e,t,n){return A(e,r,t,n)},X:function(e,t,r){return A(e,n,t,r)},y:Vr,Y:Wr,Z:qr,"%":an};function O(e,t){return function(r){var n,i,a,o=[],c=-1,l=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++c<u;)37===e.charCodeAt(c)&&(o.push(e.slice(l,c)),null!=(i=Dr[n=e.charAt(++c)])?n=e.charAt(++c):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),l=c+1);return o.push(e.slice(l,c)),o.join("")}}function P(e,t){return function(r){var n,i,a=_r(1900,void 0,1);if(A(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(t&&!("Z"in a)&&(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(i=(n=Er(_r(a.y,0,1))).getUTCDay(),n=i>4||0===i?dr.ceil(n):dr(n),n=er.offset(n,7*(a.V-1)),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(i=(n=Mr(_r(a.y,0,1))).getDay(),n=i>4||0===i?ir.ceil(n):ir(n),n=Qt.offset(n,7*(a.V-1)),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?Er(_r(a.y,0,1)).getUTCDay():Mr(_r(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,Er(a)):Mr(a)}}function A(e,t,r,n){for(var i,a,o=0,c=t.length,l=r.length;o<c;){if(n>=l)return-1;if(37===(i=t.charCodeAt(o++))){if(i=t.charAt(o++),!(a=x[i in Dr?t.charAt(o++):i])||(n=a(e,r,n))<0)return-1}else if(i!=r.charCodeAt(n++))return-1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}(e),Cr=kr.format,kr.parse,Tr=kr.utcFormat,kr.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var vi=r(8796),gi=r(4804),mi=r(5340),bi=r(6307),wi=r(2574);function xi(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,wi.H)(t)&&(0,wi.H)(r))return!0}return!1}function Oi(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var Pi=r(8210),Ai=r.n(Pi),ji=e=>e,Si={"@@functional/placeholder":!0},Mi=e=>e===Si,Ei=e=>function t(){return 0===arguments.length||1===arguments.length&&Mi(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},_i=(e,t)=>1===e?t:Ei(function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==Si).length;return a>=e?t(...n):_i(e-a,Ei(function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];var a=n.map(e=>Mi(e)?r.shift():e);return t(...a,...r)}))}),ki=e=>_i(e.length,e),Ci=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},Ti=ki((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),Di=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),Ni=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])})?r:(t=i,r=e(...i))}};function Ii(e){return 0===e?1:Math.floor(new(Ai())(e).abs().log(10).toNumber())+1}function zi(e,t,r){for(var n=new(Ai())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}ki((e,t,r)=>{var n=+e;return n+r*(+t-n)}),ki((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)}),ki((e,t,r)=>{var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});var Ri=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},Li=(e,t,r)=>{if(e.lte(0))return new(Ai())(0);var n=Ii(e.toNumber()),i=new(Ai())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,c=new(Ai())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return t?new(Ai())(c.toNumber()):new(Ai())(Math.ceil(c.toNumber()))},Bi=(e,t,r)=>{var n=new(Ai())(1),i=new(Ai())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(Ai())(10).pow(Ii(e)-1),i=new(Ai())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(Ai())(Math.floor(e)))}else 0===e?i=new(Ai())(Math.floor((t-1)/2)):r||(i=new(Ai())(Math.floor(e)));var o=Math.floor((t-1)/2);return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return ji;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}}(Ti(e=>i.add(new(Ai())(e-o).mul(n)).toNumber()),Ci)(0,t)},$i=function(e,t,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(Ai())(0),tickMin:new(Ai())(0),tickMax:new(Ai())(0)};var a,o=Li(new(Ai())(t).sub(e).div(r-1),n,i);a=e<=0&&t>=0?new(Ai())(0):(a=new(Ai())(e).add(t).div(2)).sub(new(Ai())(a).mod(o));var c=Math.ceil(a.sub(e).div(o).toNumber()),l=Math.ceil(new(Ai())(t).sub(a).div(o).toNumber()),u=c+l+1;return u>r?$i(e,t,r,n,i+1):(u<r&&(l=t>0?l+(r-u):l,c=t>0?c:c+(r-u)),{step:o,tickMin:a.sub(new(Ai())(c).mul(o)),tickMax:a.add(new(Ai())(l).mul(o))})};var Ui=Ni(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(n,2),[o,c]=Ri([t,r]);if(o===-1/0||c===1/0){var l=c===1/0?[o,...Ci(0,n-1).map(()=>1/0)]:[...Ci(0,n-1).map(()=>-1/0),c];return t>r?Di(l):l}if(o===c)return Bi(o,n,i);var{step:u,tickMin:s,tickMax:f}=$i(o,c,a,i,0),d=zi(s,f.add(new(Ai())(.1).mul(u)),u);return t>r?Di(d):d}),Fi=Ni(function(e,t){var[r,n]=e,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],[a,o]=Ri([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var c=Math.max(t,2),l=Li(new(Ai())(o).sub(a).div(c-1),i,0),u=[...zi(new(Ai())(a),new(Ai())(o).sub(new(Ai())(.99).mul(l)),l),o];return r>n?Di(u):u}),Ki=r(3859),Hi=r(8184),Gi=r(4721),Wi=r(7886),Vi=r(116),qi=r(6178),Zi=(e,t)=>t,Yi=(e,t,r)=>r,Xi=r(138),Ji=r(4779);function Qi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ea(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Qi(Object(r),!0).forEach(function(t){ta(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qi(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ta(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ra=[0,"auto"],na={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},ia=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?na:r},aa={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ra,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:Ji.tQ},oa=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?aa:r},ca={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},la=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?ca:r},ua=(e,t,r)=>{switch(t){case"xAxis":return ia(e,r);case"yAxis":return oa(e,r);case"zAxis":return la(e,r);case"angleAxis":return(0,qi.Be)(e,r);case"radiusAxis":return(0,qi.Gl)(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},sa=(e,t,r)=>{switch(t){case"xAxis":return ia(e,r);case"yAxis":return oa(e,r);case"angleAxis":return(0,qi.Be)(e,r);case"radiusAxis":return(0,qi.Gl)(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},fa=e=>e.graphicalItems.countOfBars>0;function da(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var ha=e=>e.graphicalItems.cartesianItems,pa=(0,i.Mz)([Zi,Yi],da),ya=(e,t,r)=>e.filter(r).filter(e=>!0===(null===t||void 0===t?void 0:t.includeHidden)||!e.hide),va=(0,i.Mz)([ha,ua,pa],ya),ga=e=>e.filter(e=>void 0===e.stackId),ma=(0,i.Mz)([va],ga),ba=e=>e.map(e=>e.data).filter(Boolean).flat(1),wa=(0,i.Mz)([va],ba),xa=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},Oa=(0,i.Mz)([wa,mi.HS],xa),Pa=(e,t,r)=>null!=(null===t||void 0===t?void 0:t.dataKey)?e.map(e=>({value:(0,gi.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,gi.kr)(e,t)}))):e.map(e=>({value:e})),Aa=(0,i.Mz)([Oa,ua,va],Pa);function ja(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function Sa(e){return e.filter(e=>(0,bi.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,bi.M8)(e))}function Ma(e,t,r){return!r||"number"!==typeof t||(0,bi.M8)(t)?[]:r.length?Sa(r.flatMap(r=>{var n,i,a=(0,gi.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,wi.H)(n)&&(0,wi.H)(i))return[t-n,t+i]})):[]}var Ea=(e,t,r)=>{var n=t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{});return Object.fromEntries(Object.entries(n).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,gi.yy)(e,a,r),graphicalItems:i}]}))},_a=(0,i.Mz)([Oa,va,Vi.eC],Ea),ka=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,gi.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},Ca=(0,i.Mz)([_a,mi.LF,Zi],ka),Ta=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null===(i=r.errorBars)||void 0===i?void 0:i.filter(e=>ja(n,e)),c=(0,gi.kr)(e,null!==(a=t.dataKey)&&void 0!==a?a:r.dataKey);return{value:c,errorDomain:Ma(e,c,o)}})).filter(Boolean):null!=(null===t||void 0===t?void 0:t.dataKey)?e.map(e=>({value:(0,gi.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),Da=(0,i.Mz)(Oa,ua,ma,Zi,Ta);function Na(e){var{value:t}=e;if((0,bi.vh)(t)||t instanceof Date)return t}var Ia=e=>{var t;if(null==e||!("domain"in e))return ra;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=Sa(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null===e||void 0===e?void 0:e.domain)&&void 0!==t?t:ra},za=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},Ra=e=>e.referenceElements.dots,La=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),Ba=(0,i.Mz)([Ra,Zi,Yi],La),$a=e=>e.referenceElements.areas,Ua=(0,i.Mz)([$a,Zi,Yi],La),Fa=e=>e.referenceElements.lines,Ka=(0,i.Mz)([Fa,Zi,Yi],La),Ha=(e,t)=>{var r=Sa(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},Ga=(0,i.Mz)(Ba,Zi,Ha),Wa=(e,t)=>{var r=Sa(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},Va=(0,i.Mz)([Ua,Zi],Wa),qa=(e,t)=>{var r=Sa(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},Za=(0,i.Mz)(Ka,Zi,qa),Ya=(0,i.Mz)(Ga,Za,Va,(e,t,r)=>za(e,r,t)),Xa=(0,i.Mz)([ua],Ia),Ja=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!==typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,wi.H)(i))r=i;else if("function"===typeof i)return;if((0,wi.H)(a))n=a;else if("function"===typeof a)return;var o=[r,n];if(xi(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"===typeof e&&null!=t)try{var n=e(t,r);if(xi(n))return Oi(n,t,r)}catch(h){}if(Array.isArray(e)&&2===e.length){var i,a,[o,c]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,bi.Et)(o))i=o;else if("function"===typeof o)try{null!=t&&(i=o(null===t||void 0===t?void 0:t[0]))}catch(p){}else if("string"===typeof o&&gi.IH.test(o)){var l=gi.IH.exec(o);if(null==l||null==t)i=void 0;else{var u=+l[1];i=t[0]-u}}else i=null===t||void 0===t?void 0:t[0];if("auto"===c)null!=t&&(a=Math.max(...t));else if((0,bi.Et)(c))a=c;else if("function"===typeof c)try{null!=t&&(a=c(null===t||void 0===t?void 0:t[1]))}catch(y){}else if("string"===typeof c&&gi.qx.test(c)){var s=gi.qx.exec(c);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null===t||void 0===t?void 0:t[1];var d=[i,a];if(xi(d))return null==t?d:Oi(d,t,r)}}}(t,za(r,i,(e=>{var t=Sa(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},Qa=(0,i.Mz)([ua,Xa,Ca,Da,Ya],Ja),eo=[0,1],to=(e,t,r,n,i,a,c)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,s=(0,gi._L)(t,a);return s&&null==l?o()(0,r.length):"category"===u?((e,t,r)=>{var n=e.map(Na).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,bi.CG)(n))?o()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===i?eo:c}},ro=(0,i.Mz)([ua,vi.fz,Oa,Aa,Vi.eC,Zi,Qa],to),no=(e,t,r,i,a)=>{if(null!=e){var{scale:o,type:c}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===c&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!r)?"point":"category"===c?"band":"linear";if("string"===typeof o){var l="scale".concat((0,bi.Zb)(o));return l in n?l:"point"}}},io=(0,i.Mz)([ua,vi.fz,fa,Vi.iO,Zi],no);function ao(e,t,r,i){if(null!=r&&null!=i){if("function"===typeof e.scale)return e.scale.copy().domain(r).range(i);var a=function(e){if(null!=e){if(e in n)return n[e]();var t="scale".concat((0,bi.Zb)(e));return t in n?n[t]():void 0}}(t);if(null!=a){var o=a.domain(r).range(i);return(0,gi.YB)(o),o}}}var oo=(e,t,r)=>{var n=Ia(t);if("auto"===r||"linear"===r)return null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&xi(e)?Ui(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&xi(e)?Fi(e,t.tickCount,t.allowDecimals):void 0},co=(0,i.Mz)([ro,sa,io],oo),lo=(e,t,r,n)=>{if("angleAxis"!==n&&"number"===(null===e||void 0===e?void 0:e.type)&&xi(t)&&Array.isArray(r)&&r.length>0){var i=t[0],a=r[0],o=t[1],c=r[r.length-1];return[Math.min(i,a),Math.max(o,c)]}return t},uo=(0,i.Mz)([ua,ro,co,Zi],lo),so=(0,i.Mz)(Aa,ua,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(Sa(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++){var o=n[a+1]-n[a];r=Math.min(r,o)}return r/i}}),fo=(0,i.Mz)(so,vi.fz,Vi.gY,Gi.GO,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,wi.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,bi.F4)(r,e*a),c=e*a/2;return c-o-(c-o)/a*o}return 0}),ho=(0,i.Mz)(ia,(e,t)=>{var r=ia(e,t);return null==r||"string"!==typeof r.padding?0:fo(e,"xAxis",t,r.padding)},(e,t)=>{var r,n;if(null==e)return{left:0,right:0};var{padding:i}=e;return"string"===typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}}),po=(0,i.Mz)(oa,(e,t)=>{var r=oa(e,t);return null==r||"string"!==typeof r.padding?0:fo(e,"yAxis",t,r.padding)},(e,t)=>{var r,n;if(null==e)return{top:0,bottom:0};var{padding:i}=e;return"string"===typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}}),yo=(0,i.Mz)([Gi.GO,ho,Wi.U,Wi.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),vo=(0,i.Mz)([Gi.GO,vi.fz,po,Wi.U,Wi.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),go=(e,t,r,n)=>{var i;switch(t){case"xAxis":return yo(e,r,n);case"yAxis":return vo(e,r,n);case"zAxis":return null===(i=la(e,r))||void 0===i?void 0:i.range;case"angleAxis":return(0,qi.Cv)(e);case"radiusAxis":return(0,qi.Dc)(e,r);default:return}},mo=(0,i.Mz)([ua,go],Xi.I),bo=(0,i.Mz)([ua,io,uo,mo],ao);(0,i.Mz)(va,Zi,(e,t)=>e.flatMap(e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]}).filter(e=>ja(t,e)));function wo(e,t){return e.id<t.id?-1:e.id>t.id?1:0}var xo=(e,t)=>t,Oo=(e,t,r)=>r,Po=(0,i.Mz)(Hi.h,xo,Oo,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(wo)),Ao=(0,i.Mz)(Hi.W,xo,Oo,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(wo)),jo=(e,t)=>({width:e.width,height:t.height}),So=(0,i.Mz)(Gi.GO,ia,jo),Mo=(0,i.Mz)(Ki.A$,Gi.GO,Po,xo,Oo,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var c=jo(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var l="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(l)*c.height,a+=(l?-1:1)*c.height}),o}),Eo=(0,i.Mz)(Ki.Lp,Gi.GO,Ao,xo,Oo,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var c=((e,t)=>({width:"number"===typeof t.width?t.width:Ji.tQ,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var l="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(l)*c.width,a+=(l?-1:1)*c.width}),o}),_o=(e,t)=>{var r=(0,Gi.GO)(e),n=ia(e,t);if(null!=n){var i=Mo(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},ko=(e,t)=>{var r=(0,Gi.GO)(e),n=oa(e,t);if(null!=n){var i=Eo(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},Co=(0,i.Mz)(Gi.GO,oa,(e,t)=>({width:"number"===typeof t.width?t.width:Ji.tQ,height:e.height})),To=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,c=(0,gi._L)(e,n),l=t.map(e=>e.value);return o&&c&&"category"===a&&i&&(0,bi.CG)(l)?l:void 0}},Do=(0,i.Mz)([vi.fz,Aa,ua,Zi],To),No=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;return!(0,gi._L)(e,n)||"number"!==i&&"auto"===a?void 0:t.map(e=>e.value)}},Io=(0,i.Mz)([vi.fz,Aa,sa,Zi],No),zo=(0,i.Mz)([vi.fz,(e,t,r)=>{switch(t){case"xAxis":return ia(e,r);case"yAxis":return oa(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},io,bo,Do,Io,go,co,Zi],(e,t,r,n,i,a,o,c,l)=>{if(null==t)return null;var u=(0,gi._L)(e,l);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:l,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:c,range:o,realScaleType:r,scale:n}}),Ro=(0,i.Mz)([vi.fz,sa,io,bo,co,go,Do,Io,Zi],(e,t,r,n,i,a,o,c,l)=>{if(null!=t&&null!=n){var u=(0,gi._L)(e,l),{type:s,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"===typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===l&&null!=a&&a.length>=2?2*(0,bi.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>{var r=o?o.indexOf(e):e;return{index:t,coordinate:n(r)+p,value:e,offset:p}}).filter(e=>!(0,bi.M8)(e.coordinate)):u&&c?c.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),Lo=(0,i.Mz)([vi.fz,sa,bo,go,Do,Io,Zi],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var c=(0,gi._L)(e,o),{tickCount:l}=t,u=0;return u="angleAxis"===o&&(null===n||void 0===n?void 0:n.length)>=2?2*(0,bi.sA)(n[0]-n[1])*u:u,c&&a?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(l).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}}),Bo=(0,i.Mz)(ua,bo,(e,t)=>{if(null!=e&&null!=t)return ea(ea({},e),{},{scale:t})}),$o=(0,i.Mz)([ua,io,ro,mo],ao),Uo=((0,i.Mz)((e,t,r)=>la(e,r),$o,(e,t)=>{if(null!=e&&null!=t)return ea(ea({},e),{},{scale:t})}),(0,i.Mz)([vi.fz,Hi.h,Hi.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}))},4986:(e,t,r)=>{"use strict";r.d(t,{i:()=>c});var n=r(2768);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){o(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var c=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var c=!0===e.settings.active;if(null!=o.index){if(c)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},5053:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4208),i=r(9599),a=r(2104),o=r(2715);t.uniqBy=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.identity;return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},5082:(e,t)=>{"use strict";var r=60103,n=60106,i=60107,a=60108,o=60114,c=60109,l=60110,u=60112,s=60113,f=60120,d=60115,h=60116,p=60121,y=60122,v=60117,g=60129,m=60131;if("function"===typeof Symbol&&Symbol.for){var b=Symbol.for;r=b("react.element"),n=b("react.portal"),i=b("react.fragment"),a=b("react.strict_mode"),o=b("react.profiler"),c=b("react.provider"),l=b("react.context"),u=b("react.forward_ref"),s=b("react.suspense"),f=b("react.suspense_list"),d=b("react.memo"),h=b("react.lazy"),p=b("react.block"),y=b("react.server.block"),v=b("react.fundamental"),g=b("react.debug_trace_mode"),m=b("react.legacy_hidden")}function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case o:case a:case s:case f:return e;default:switch(e=e&&e.$$typeof){case l:case u:case h:case d:case c:return e;default:return t}}case n:return t}}}t.isFragment=function(e){return w(e)===i}},5261:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6433);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},5303:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(2489),i=r(4704),a=r(6321),o=r(1444);t.has=function(e,t){let r;if(r=Array.isArray(t)?t:"string"===typeof t&&n.isDeepKey(t)&&null==e?.[t]?o.toPath(t):[t],0===r.length)return!1;let c=e;for(let n=0;n<r.length;n++){const e=r[n];if(null==c||!Object.hasOwn(c,e)){if(!((Array.isArray(c)||a.isArguments(c))&&i.isIndex(e)&&e<c.length))return!1}c=c[e]}return!0}},5316:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9709),i=r(7043),a=r(1444);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));const c=(e,t)=>{let r=e;for(let n=0;n<t.length&&null!=r;++n)r=r[t[n]];return r},l=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"===typeof e||Array.isArray(e)||i.isKey(e)?e:{key:e,path:a.toPath(e)}));return e.map(e=>({original:e,criteria:l.map(t=>((e,t)=>null==t||null==e?t:"object"===typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:c(t,e.path):"function"===typeof e?e(t):Array.isArray(e)?c(t,e):"object"===typeof t?t[e]:t)(t,e))})).slice().sort((e,t)=>{for(let i=0;i<l.length;i++){const a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},5340:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i});var n=r(2099),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},5414:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(1293);t.throttle=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"object"!==typeof r&&(r={});const{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,trailing:a,maxWait:t})}},5508:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6433),i=r(7599);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,c)=>{const l=t?.(r,a,o,c);if(null!=l)return l;if("object"===typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{const t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{const t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},5654:(e,t,r)=>{"use strict";r.d(t,{i:()=>D});var n=r(5043),i=r(4250),a=r.n(i);var o=1e-4,c=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),u=(e,t)=>r=>{var n=c(e,t);return l(n,r)},s=function(){for(var e,t,r,n,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var f=a[0].split("(");"cubic-bezier"===f[0]&&4===f[1].split(")")[0].split(",").length&&([e,r,t,n]=f[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var d,h,p=u(e,t),y=u(r,n),v=(d=e,h=t,e=>{var t=[...c(d,h).map((e,t)=>e*t).slice(1),0];return l(t,e)}),g=e=>e>1?1:e<0?0:e,m=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=p(r)-t,a=v(r);if(Math.abs(i-t)<o||a<o)return y(r);r=g(r-i/a)}return y(r)};return m.isStepper=!1,m},f=e=>{if("string"===typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var c=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return Math.abs(l-i)<o&&Math.abs(c)<o?[i,0]:[l,c]};return i.isStepper=!0,i.dt=n,i}();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"===typeof e?e:null};function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){p(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var y=(e,t)=>Object.keys(t).reduce((r,n)=>h(h({},r),{},{[n]:e(n,t[n])}),{});function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var b=(e,t,r)=>e+(t-e)*r,w=e=>{var{from:t,to:r}=e;return t!==r},x=(e,t,r)=>{var n=y((t,r)=>{if(w(r)){var[n,i]=e(r.from,r.to,r.velocity);return g(g({},r),{},{from:n,velocity:i})}return r},t);return r<1?y((e,t)=>w(t)?g(g({},t),{},{velocity:b(t.velocity,n[e].velocity,r),from:b(t.from,n[e].from,r)}):t,t):x(e,n,r-1)};function O(e,t,r,n,i,a){var o,c=n.reduce((r,n)=>g(g({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),l=null,u=n=>{o||(o=n);var s=(n-o)/r.dt;c=x(r,c,s),i(g(g(g({},e),t),y((e,t)=>t.from,c))),o=n,Object.values(c).filter(w).length&&(l=a.setTimeout(u))};return()=>(l=a.setTimeout(u),()=>{l()})}const P=(e,t,r,n,i,a)=>{var o,c,l=(o=e,c=t,[Object.keys(o),Object.keys(c)].reduce((e,t)=>e.filter(e=>t.includes(e))));return!0===r.isStepper?O(e,t,r,l,i,a):function(e,t,r,n,i,a,o){var c,l=null,u=i.reduce((r,n)=>g(g({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{c||(c=i);var f=(i-c)/n,d=y((e,t)=>b(...t,r(f)),u);if(a(g(g(g({},e),t),d)),f<1)l=o.setTimeout(s);else{var h=y((e,t)=>b(...t,r(1)),u);a(g(g(g({},e),t),h))}};return()=>(l=o.setTimeout(s),()=>{l()})}(e,t,r,n,l,i,a)};class A{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"===typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var j=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function S(){return S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},S.apply(null,arguments)}function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k(){return function(e){var t=()=>null,r=!1,n=null,i=a=>{if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,[c,...l]=o;return"number"===typeof c?void(n=e.setTimeout(i.bind(null,l),c)):(i(c),void(n=e.setTimeout(i.bind(null,l))))}"object"===typeof a&&t(a),"function"===typeof a&&a()}};return{stop:()=>{r=!0},start:e=>{r=!1,n&&(n(),n=null),i(e)},subscribe:e=>(t=e,()=>{t=()=>null}),getTimeoutController:()=>e}}(new A)}class C extends n.PureComponent{constructor(e,t){super(e,t),_(this,"mounted",!1),_(this,"manager",null),_(this,"stopJSAnimation",null),_(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:c,animationManager:l}=this.props;if(this.manager=l,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||c<=0)return this.state={style:{}},void("function"===typeof o&&(this.state={style:a}));if(i){if("function"===typeof o)return void(this.state={style:i});this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:c}=this.props,{style:l}=this.state;if(r)if(t){if(!(a()(e.to,o)&&e.canBegin&&e.isActive)){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=u||i?c:e.to;if(this.state&&l){var f={style:n?{[n]:s}:s};(n&&l[n]!==s||!n&&l!==s)&&this.setState(f)}this.runAnimation(E(E({},this.props),{},{from:s,begin:0}))}}else{var d={style:n?{[n]:o}:o};this.state&&l&&(n&&l[n]!==o||!n&&l!==o)&&this.setState(d)}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:c}=e,l=P(t,r,f(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([c,a,()=>{this.stopJSAnimation=l()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:c,children:l}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"!==typeof a&&"function"!==typeof l&&"spring"!==a){var u=n?{[n]:i}:i,s=((e,t,r)=>e.map(e=>{return"".concat((n=e,n.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())))," ").concat(t,"ms ").concat(r);var n}).join(","))(Object.keys(u),r,a);this.manager.start([o,t,E(E({},u),{},{transition:s}),r,c])}else this.runJSAnimation(e)}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:c,from:l,to:u,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,j),v=n.Children.count(t),g=this.state.style;if("function"===typeof t)return t(g);if(!c||0===v||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,E(E({},y),{},{style:E(E({},t),g),className:r}))};return 1===v?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}}_(C,"displayName","Animate"),_(C,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var T=(0,n.createContext)(null);function D(e){var t,r,i=(0,n.useContext)(T);return n.createElement(C,S({},e,{animationManager:null!==(t=null!==(r=e.animationManager)&&void 0!==r?r:i)&&void 0!==t?t:k()}))}},5807:(e,t,r)=>{"use strict";r.d(t,{dc:()=>c,ff:()=>o,g0:()=>l});var n=r(2099),i=r(3821),a=r.n(i),o=e=>e.legend.settings,c=e=>e.legend.size,l=(0,n.Mz)([e=>e.legend.payload,o],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?a()(n,r):n})},5839:(e,t,r)=>{"use strict";r.d(t,{h4:()=>G});var n=Symbol.for("immer-nothing"),i=Symbol.for("immer-draftable"),a=Symbol.for("immer-state");function o(e){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function l(e){return!!e&&!!e[a]}function u(e){return!!e&&(f(e)||Array.isArray(e)||!!e[i]||!!e.constructor?.[i]||v(e)||g(e))}var s=Object.prototype.constructor.toString();function f(e){if(!e||"object"!==typeof e)return!1;const t=c(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===s}function d(e,t){0===h(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function h(e){const t=e[a];return t?t.type_:Array.isArray(e)?1:v(e)?2:g(e)?3:0}function p(e,t){return 2===h(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function y(e,t,r){const n=h(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function v(e){return e instanceof Map}function g(e){return e instanceof Set}function m(e){return e.copy_||e.base_}function b(e,t){if(v(e))return new Map(e);if(g(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=f(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[a];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(c(e),t)}{const t=c(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function w(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return O(e)||l(e)||!u(e)||(h(e)>1&&(e.set=e.add=e.clear=e.delete=x),Object.freeze(e),t&&Object.entries(e).forEach(e=>{let[t,r]=e;return w(r,!0)})),e}function x(){o(2)}function O(e){return Object.isFrozen(e)}var P,A={};function j(e){const t=A[e];return t||o(0),t}function S(){return P}function M(e,t){t&&(j("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function E(e){_(e),e.drafts_.forEach(C),e.drafts_=null}function _(e){e===P&&(P=e.parent_)}function k(e){return P={drafts_:[],parent_:P,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function C(e){const t=e[a];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[a].modified_&&(E(t),o(4)),u(e)&&(e=D(t,e),t.parent_||I(t,e)),t.patches_&&j("Patches").generateReplacementPatches_(r[a].base_,e,t.patches_,t.inversePatches_)):e=D(t,r,[]),E(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==n?e:void 0}function D(e,t,r){if(O(t))return t;const n=t[a];if(!n)return d(t,(i,a)=>N(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return I(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),d(i,(i,o)=>N(e,n,t,i,o,r,a)),I(e,t,!1),r&&e.patches_&&j("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function N(e,t,r,n,i,a,o){if(l(i)){const o=D(e,i,a&&t&&3!==t.type_&&!p(t.assigned_,n)?a.concat(n):void 0);if(y(r,n,o),!l(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(u(i)&&!O(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;D(e,i),t&&t.scope_.parent_||"symbol"===typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||I(e,i)}}function I(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var z={get(e,t){if(t===a)return e;const r=m(e);if(!p(r,t))return function(e,t,r){const n=B(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!u(n)?n:n===L(e.base_,t)?(U(e),e.copy_[t]=F(n,e)):n},has:(e,t)=>t in m(e),ownKeys:e=>Reflect.ownKeys(m(e)),set(e,t,r){const n=B(m(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=L(m(e),t),c=n?.[a];if(c&&c.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((i=r)===(o=n)?0!==i||1/i===1/o:i!==i&&o!==o)&&(void 0!==r||p(e.base_,t)))return!0;U(e),$(e)}var i,o;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,U(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=m(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){o(11)},getPrototypeOf:e=>c(e.base_),setPrototypeOf(){o(12)}},R={};function L(e,t){const r=e[a];return(r?m(r):e)[t]}function B(e,t){if(!(t in e))return;let r=c(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=c(r)}}function $(e){e.modified_||(e.modified_=!0,e.parent_&&$(e.parent_))}function U(e){e.copy_||(e.copy_=b(e.base_,e.scope_.immer_.useStrictShallowCopy_))}d(z,(e,t)=>{R[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),R.deleteProperty=function(e,t){return R.set.call(this,e,t,void 0)},R.set=function(e,t,r){return z.set.call(this,e[0],t,r,e[0])};function F(e,t){const r=v(e)?j("MapSet").proxyMap_(e,t):g(e)?j("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:S(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,a=z;r&&(i=[n],a=R);const{revoke:o,proxy:c}=Proxy.revocable(i,a);return n.draft_=c,n.revoke_=o,c}(e,t);return(t?t.scope_:S()).drafts_.push(r),r}function K(e){if(!u(e)||O(e))return e;const t=e[a];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=b(e,t.scope_.immer_.useStrictShallowCopy_)}else r=b(e,!0);return d(r,(e,t)=>{y(r,e,K(t))}),t&&(t.finalized_=!1),r}var H=new class{constructor(e){var t=this;this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"===typeof e&&"function"!==typeof t){const r=t;t=e;const n=this;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;for(var i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];return n.produce(e,e=>t.call(this,e,...a))}}let i;if("function"!==typeof t&&o(6),void 0!==r&&"function"!==typeof r&&o(7),u(e)){const n=k(this),a=F(e,void 0);let o=!0;try{i=t(a),o=!1}finally{o?E(n):_(n)}return M(n,r),T(i,n)}if(!e||"object"!==typeof e){if(i=t(e),void 0===i&&(i=e),i===n&&(i=void 0),this.autoFreeze_&&w(i,!0),r){const t=[],n=[];j("Patches").generateReplacementPatches_(e,i,t,n),r(t,n)}return i}o(1)},this.produceWithPatches=(e,r)=>{if("function"===typeof e)return function(r){for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.produceWithPatches(r,t=>e(t,...i))};let n,i;return[this.produce(e,r,(e,t)=>{n=e,i=t}),n,i]},"boolean"===typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"===typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){u(e)||o(8),l(e)&&(e=function(e){l(e)||o(10);return K(e)}(e));const t=k(this),r=F(e,void 0);return r[a].isManual_=!0,_(t),r}finishDraft(e,t){const r=e&&e[a];r&&r.isManual_||o(9);const{scope_:n}=r;return M(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=j("Patches").applyPatches_;return l(e)?n(e,t):this.produce(e,e=>n(e,t))}};H.produce,H.produceWithPatches.bind(H),H.setAutoFreeze.bind(H),H.setUseStrictShallowCopy.bind(H),H.applyPatches.bind(H),H.createDraft.bind(H),H.finishDraft.bind(H);function G(e){return e}},5990:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},6015:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!==typeof window&&window.document&&Boolean(window.document.createElement)&&window.setTimeout)}},6096:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},6178:(e,t,r)=>{"use strict";r.d(t,{Be:()=>M,Cv:()=>D,D0:()=>I,Gl:()=>E,Dc:()=>N});var n=r(2099),i=r(3859),a=r(4721),o=r(165),c=r(6307),l=!0,u=0,s=!1,f="auto",d=!0,h="category",p=!1,y=!0,v=0,g="auto",m=!0,b=5,w="number",x=r(138),O=r(8796),P={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u,includeHidden:!1,name:void 0,reversed:s,scale:f,tick:d,tickCount:void 0,ticks:void 0,type:h,unit:void 0},A={allowDataOverflow:p,allowDecimals:!1,allowDuplicatedCategory:y,dataKey:void 0,domain:void 0,id:v,includeHidden:!1,name:void 0,reversed:!1,scale:g,tick:m,tickCount:b,ticks:void 0,type:w,unit:void 0},j={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:l,dataKey:void 0,domain:void 0,id:u,includeHidden:!1,name:void 0,reversed:!1,scale:f,tick:d,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},S={allowDataOverflow:p,allowDecimals:!1,allowDuplicatedCategory:y,dataKey:void 0,domain:void 0,id:v,includeHidden:!1,name:void 0,reversed:!1,scale:g,tick:m,tickCount:b,ticks:void 0,type:"category",unit:void 0},M=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?j:P,E=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?S:A,_=e=>e.polarOptions,k=(0,n.Mz)([i.Lp,i.A$,a.GO],o.lY),C=(0,n.Mz)([_,k],(e,t)=>{if(null!=e)return(0,c.F4)(e.innerRadius,t,0)}),T=(0,n.Mz)([_,k],(e,t)=>{if(null!=e)return(0,c.F4)(e.outerRadius,t,.8*t)}),D=(0,n.Mz)([_],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]}),N=((0,n.Mz)([M,D],x.I),(0,n.Mz)([k,C,T],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]})),I=((0,n.Mz)([E,N],x.I),(0,n.Mz)([O.fz,_,C,T,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:u,endAngle:s}=t;return{cx:(0,c.F4)(o,i,i/2),cy:(0,c.F4)(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:s,clockWise:!1}}}))},6307:(e,t,r)=>{"use strict";r.d(t,{CG:()=>h,Dj:()=>p,Et:()=>l,F4:()=>d,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>c,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>u});var n=r(7770),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,c=e=>"string"===typeof e&&e.indexOf("%")===e.length-1,l=e=>("number"===typeof e||e instanceof Number)&&!o(e),u=e=>l(e)||"string"===typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!l(e)&&"string"!==typeof e)return n;if(c(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},h=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},p=(e,t)=>l(e)&&l(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"===typeof t?t(e):i()(e,t))===r)}var v=e=>null===e||"undefined"===typeof e,g=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},6321:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(5990);t.isArguments=function(e){return null!==e&&"object"===typeof e&&"[object Arguments]"===n.getTag(e)}},6371:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e),a=t;return Object.keys(t).reduce((e,t)=>(void 0===e[t]&&void 0!==a[t]&&(e[t]=a[t]),e),r)}r.d(t,{e:()=>a})},6392:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},6433:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6443),i=r(5990),a=r(7599),o=r(4803),c=r(3757);function l(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:new Map,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0;const f=s?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){const t=new Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=l(e[i],i,r,n,s);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){const t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){const t=new Map;n.set(e,t);for(const[i,a]of e)t.set(i,l(a,i,r,n,s));return t}if(e instanceof Set){const t=new Set;n.set(e,t);for(const i of e)t.add(l(i,void 0,r,n,s));return t}if("undefined"!==typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(c.isTypedArray(e)){const t=new(Object.getPrototypeOf(e).constructor)(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=l(e[i],i,r,n,s);return t}if(e instanceof ArrayBuffer||"undefined"!==typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){const t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),u(t,e,r,n,s),t}if("undefined"!==typeof File&&e instanceof File){const t=new File([e],e.name,{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Blob){const t=new Blob([e],{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Error){const t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,u(t,e,r,n,s),t}if("object"===typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){const t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),u(t,e,r,n,s),t}return e}function u(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;const o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){const c=o[n],u=Object.getOwnPropertyDescriptor(e,c);(null==u||u.writable)&&(e[c]=l(t[c],c,r,i,a))}}t.cloneDeepWith=function(e,t){return l(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=l,t.copyProperties=u},6443:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},6770:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(2777);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},6831:(e,t,r)=>{"use strict";r.d(t,{f:()=>p});var n=r(6307),i=r(7213),a=r(6015);function o(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c{static create(e){return new c(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}o(c,"EPS",1e-4);var l=function(e){var{width:t,height:r}=e,n=function(e){return(e%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=n*Math.PI/180,a=Math.atan(r/t),o=i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i);return Math.abs(o)};function u(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function s(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){h(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e,t,r){var o,{tick:c,ticks:f,viewBox:h,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=e;if(!f||!f.length||!c)return[];if((0,n.Et)(v)||a.m.isSsr)return null!==(o=function(e,t){return u(e,t+1)}(f,(0,n.Et)(v)?v:0))&&void 0!==o?o:[];var w=[],x="top"===y||"bottom"===y?"width":"height",O=m&&"width"===x?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},P=(e,n)=>{var a="function"===typeof g?g(e.value,n):e.value;return"width"===x?function(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return l(n,r)}((0,i.P)(a,{fontSize:t,letterSpacing:r}),O,b):(0,i.P)(a,{fontSize:t,letterSpacing:r})[x]},A=f.length>=2?(0,n.sA)(f[1].coordinate-f[0].coordinate):1,j=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:c}=e;return 1===t?{start:n?i:a,end:n?i+o:a+c}:{start:n?i+o:a+c,end:n?i:a}}(h,A,x);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:c,end:l}=t,f=0,d=1,h=c,p=function(){var t=null===n||void 0===n?void 0:n[f];if(void 0===t)return{v:u(n,d)};var a,o=f,p=()=>(void 0===a&&(a=r(t,o)),a),y=t.coordinate,v=0===f||s(e,y,p,h,l);v||(f=0,h=c,d+=1),v&&(h=y+e*(p()/2+i),f+=d)};d<=o.length;)if(a=p())return a.v;return[]}(A,j,P,f,p):(w="preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var o=(n||[]).slice(),c=o.length,{start:l,end:u}=t;if(a){var f=n[c-1],h=r(f,c-1),p=e*(f.coordinate+e*h/2-u);o[c-1]=f=d(d({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate}),s(e,f.tickCoord,()=>h,l,u)&&(u=f.tickCoord-e*(h/2+i),o[c-1]=d(d({},f),{},{isShow:!0}))}for(var y=a?c-1:c,v=function(t){var n,a=o[t],c=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var f=e*(a.coordinate-e*c()/2-l);o[t]=a=d(d({},a),{},{tickCoord:f<0?a.coordinate-f*e:a.coordinate})}else o[t]=a=d(d({},a),{},{tickCoord:a.coordinate});s(e,a.tickCoord,c,l,u)&&(l=a.tickCoord+e*(c()/2+i),o[t]=d(d({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(A,j,P,f,p,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:c}=t,{end:l}=t,u=function(t){var n,u=a[t],f=()=>(void 0===n&&(n=r(u,t)),n);if(t===o-1){var h=e*(u.coordinate+e*f()/2-l);a[t]=u=d(d({},u),{},{tickCoord:h>0?u.coordinate-h*e:u.coordinate})}else a[t]=u=d(d({},u),{},{tickCoord:u.coordinate});s(e,u.tickCoord,f,c,l)&&(l=u.tickCoord-e*(f()/2+i),a[t]=d(d({},u),{},{isShow:!0}))},f=o-1;f>=0;f--)u(f);return a}(A,j,P,f,p),w.filter(e=>e.isShow))}},7043:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(2777),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!("number"!==typeof e&&"boolean"!==typeof e&&null!=e&&!n.isSymbol(e))||("string"===typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e)))}},7118:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},7213:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});var n=r(6015);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){o(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var c={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span";var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0===e||null===e||n.m.isSsr)return{width:0,height:0};var r=function(e){var t=a({},e);return Object.keys(t).forEach(e=>{t[e]||delete t[e]}),t}(t),i=JSON.stringify({text:e,copyStyle:r});if(c.widthCache[i])return c.widthCache[i];try{var o=document.getElementById(u);o||((o=document.createElement("span")).setAttribute("id",u),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var s=a(a({},l),r);Object.assign(o.style,s),o.textContent="".concat(e);var f=o.getBoundingClientRect(),d={width:f.width,height:f.height};return c.widthCache[i]=d,++c.cacheCount>2e3&&(c.cacheCount=0,c.widthCache={}),d}catch(h){return{width:0,height:0}}}},7231:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3799),i=r(4879);t.range=function(e,t,r){r&&"number"!==typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);const a=Math.max(Math.ceil((t-e)/(r||1)),0),o=new Array(a);for(let n=0;n<a;n++)o[n]=e,e+=r;return o}},7237:(e,t,r)=>{"use strict";r(8365)},7250:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(5043).createContext)(null)},7260:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},7287:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>u,_U:()=>l,j2:()=>c});var n=r(5043),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},c=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],l=(e,t)=>{if(!e||"function"===typeof e||"boolean"===typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!==typeof r&&"function"!==typeof r)return null;var i={};return Object.keys(r).forEach(e=>{c.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},u=(e,t,r)=>{if(null===e||"object"!==typeof e&&"function"!==typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];c.includes(i)&&"function"===typeof a&&(n||(n={}),n[i]=((e,t,r)=>n=>(e(t,r,n),null))(a,t,r))}),n}},7312:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;const r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){const o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},7330:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9323);t.property=function(e){return function(t){return n.get(t,e)}}},7371:(e,t,r)=>{"use strict";r.d(t,{i:()=>u});const n=Math.PI,i=2*n,a=1e-6,o=i-a;function c(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?c:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return c;const r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,o){if(e=+e,t=+t,r=+r,i=+i,(o=+o)<0)throw new Error(`negative radius: ${o}`);let c=this._x1,l=this._y1,u=r-e,s=i-t,f=c-e,d=l-t,h=f*f+d*d;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(h>a)if(Math.abs(d*u-s*f)>a&&o){let p=r-c,y=i-l,v=u*u+s*s,g=p*p+y*y,m=Math.sqrt(v),b=Math.sqrt(h),w=o*Math.tan((n-Math.acos((v+h-g)/(2*m*b)))/2),x=w/b,O=w/m;Math.abs(x-1)>a&&this._append`L${e+x*f},${t+x*d}`,this._append`A${o},${o},0,0,${+(d*p>f*y)},${this._x1=e+O*u},${this._y1=t+O*s}`}else this._append`L${this._x1=e},${this._y1=t}`;else;}arc(e,t,r,c,l,u){if(e=+e,t=+t,u=!!u,(r=+r)<0)throw new Error(`negative radius: ${r}`);let s=r*Math.cos(c),f=r*Math.sin(c),d=e+s,h=t+f,p=1^u,y=u?c-l:l-c;null===this._x1?this._append`M${d},${h}`:(Math.abs(this._x1-d)>a||Math.abs(this._y1-h)>a)&&this._append`L${d},${h}`,r&&(y<0&&(y=y%i+i),y>o?this._append`A${r},${r},0,1,${p},${e-s},${t-f}A${r},${r},0,1,${p},${this._x1=d},${this._y1=h}`:y>a&&this._append`A${r},${r},0,${+(y>=n)},${p},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{const e=Math.floor(r);if(!(e>=0))throw new RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},7591:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},7599:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},7734:(e,t,r)=>{"use strict";r.d(t,{d:()=>D});var n=r(5043),i=r(155),a=r(6307),o=r(240),c=r(4804),l=r(6831),u=r(528),s=r(8796),f=r(4954),d=r(787),h=r(3987),p=r(6371),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},O.apply(null,arguments)}function P(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var A=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:c,ry:l}=e;return n.createElement("rect",{x:i,y:a,ry:l,width:o,height:c,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function j(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"===typeof e)r=e(t);else{var{x1:i,y1:a,x2:c,y2:l,key:u}=t,s=P(t,y),f=(0,o.J9)(s,!1),{offset:d}=f,h=P(f,v);r=n.createElement("line",O({},h,{x1:i,y1:a,x2:c,y2:l,fill:"none",key:u}))}return r}function S(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:c}=e,l=P(e,g),u=a.map((e,n)=>{var a=w(w({},l),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n});return j(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function M(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:c}=e,l=P(e,m),u=a.map((e,n)=>{var a=w(w({},l),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n});return j(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function E(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:c,horizontalPoints:l,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var s=l.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,l)=>{var u=!s[l+1]?a+c-e:s[l+1]-e;if(u<=0)return null;var f=l%t.length;return n.createElement("rect",{key:"react-".concat(l),y:e,x:i,height:u,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function _(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:c,height:l,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var s=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var u=!s[t+1]?a+c-e:s[t+1]-e;if(u<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:u,height:l,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var k=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,c.PW)((0,l.f)(w(w(w({},u.u.defaultProps),r),{},{ticks:(0,c.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},C=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,c.PW)((0,l.f)(w(w(w({},u.u.defaultProps),r),{},{ticks:(0,c.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function D(e){var t=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.hj)(),c=w(w({},(0,p.e)(e,T)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:l,yAxisId:u,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:x,verticalValues:P}=c,j=(0,h.r)(),D=(0,d.G)(e=>(0,f.ZB)(e,"xAxis",l,j)),N=(0,d.G)(e=>(0,f.ZB)(e,"yAxis",u,j));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=c.verticalCoordinatesGenerator||k,z=c.horizontalCoordinatesGenerator||C,{horizontalPoints:R,verticalPoints:L}=c;if((!R||!R.length)&&"function"===typeof z){var B=x&&x.length,$=z({yAxis:N?w(w({},N),{},{ticks:B?x:N.ticks}):void 0,width:t,height:r,offset:o},!!B||b);(0,i.R)(Array.isArray($),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof $,"]")),Array.isArray($)&&(R=$)}if((!L||!L.length)&&"function"===typeof I){var U=P&&P.length,F=I({xAxis:D?w(w({},D),{},{ticks:U?P:D.ticks}):void 0,width:t,height:r,offset:o},!!U||b);(0,i.R)(Array.isArray(F),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(L=F)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(A,{fill:c.fill,fillOpacity:c.fillOpacity,x:c.x,y:c.y,width:c.width,height:c.height,ry:c.ry}),n.createElement(E,O({},c,{horizontalPoints:R})),n.createElement(_,O({},c,{verticalPoints:L})),n.createElement(S,O({},c,{offset:o,horizontalPoints:R,xAxis:D,yAxis:N})),n.createElement(M,O({},c,{offset:o,verticalPoints:L,xAxis:D,yAxis:N})))}D.displayName="CartesianGrid"},7770:(e,t,r)=>{e.exports=r(9323).get},7773:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>o,hq:()=>a});var n={chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},i=(0,r(2017).Z0)({name:"chartData",initialState:n,reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload)return e.dataStartIndex=0,void(e.dataEndIndex=0);t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:a,setDataStartEndIndexes:o,setComputedData:c}=i.actions,l=i.reducer},7820:(e,t,r)=>{e.exports=r(5414).throttle},7886:(e,t,r)=>{"use strict";r.d(t,{C:()=>c,U:()=>l});var n=r(2099),i=r(4721),a=r(3859),o=r(6307),c=e=>e.brush,l=(0,n.Mz)([c,i.GO,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null===r||void 0===r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},8182:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9330),i=r(7118);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},8184:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(2099),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},8210:function(e,t,r){var n;!function(){"use strict";var i,a=1e9,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},c=!0,l="[DecimalError] ",u=l+"Invalid argument: ",s=l+"Exponent out of range: ",f=Math.floor,d=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=1e7,y=9007199254740991,v=f(1286742750677284.5),g={};function m(e,t){var r,n,i,a,o,l,u,s,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),c?E(t,d):t;if(u=e.d,s=t.d,o=e.e,i=t.e,u=u.slice(),a=o-i){for(a<0?(n=u,a=-a,l=s.length):(n=s,i=o,l=u.length),a>(l=(o=Math.ceil(d/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=u.length)-(a=s.length)<0&&(a=l,n=s,s=u,u=n),r=0;a;)r=(u[--a]=u[a]+s[a]+r)/p|0,u[a]%=p;for(r&&(u.unshift(r),++i),l=u.length;0==u[--l];)u.pop();return t.d=u,t.e=i,c?E(t,d):t}function b(e,t,r){if(e!==~~e||e<t||e>r)throw Error(u+e)}function w(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=j(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=j(r))}else if(0===o)return"0";for(;o%10===0;)o/=10;return a+o}g.absoluteValue=g.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},g.comparedTo=g.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(t=0,r=(n=a.d.length)<(i=e.d.length)?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1},g.decimalPlaces=g.dp=function(){var e=this,t=e.d.length-1,r=7*(t-e.e);if(t=e.d[t])for(;t%10==0;t/=10)r--;return r<0?0:r},g.dividedBy=g.div=function(e){return x(this,new this.constructor(e))},g.dividedToIntegerBy=g.idiv=function(e){var t=this.constructor;return E(x(this,new t(e),0,1),t.precision)},g.equals=g.eq=function(e){return!this.cmp(e)},g.exponent=function(){return P(this)},g.greaterThan=g.gt=function(e){return this.cmp(e)>0},g.greaterThanOrEqualTo=g.gte=function(e){return this.cmp(e)>=0},g.isInteger=g.isint=function(){return this.e>this.d.length-2},g.isNegative=g.isneg=function(){return this.s<0},g.isPositive=g.ispos=function(){return this.s>0},g.isZero=function(){return 0===this.s},g.lessThan=g.lt=function(e){return this.cmp(e)<0},g.lessThanOrEqualTo=g.lte=function(e){return this.cmp(e)<1},g.logarithm=g.log=function(e){var t,r=this,n=r.constructor,a=n.precision,o=a+5;if(void 0===e)e=new n(10);else if((e=new n(e)).s<1||e.eq(i))throw Error(l+"NaN");if(r.s<1)throw Error(l+(r.s?"NaN":"-Infinity"));return r.eq(i)?new n(0):(c=!1,t=x(S(r,o),S(e,o),o),c=!0,E(t,a))},g.minus=g.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?_(t,e):m(t,(e.s=-e.s,e))},g.modulo=g.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(!(e=new n(e)).s)throw Error(l+"NaN");return r.s?(c=!1,t=x(r,e,0,1).times(e),c=!0,r.minus(t)):E(new n(r),i)},g.naturalExponential=g.exp=function(){return O(this)},g.naturalLogarithm=g.ln=function(){return S(this)},g.negated=g.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},g.plus=g.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?m(t,e):_(t,(e.s=-e.s,e))},g.precision=g.sd=function(e){var t,r,n,i=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(u+e);if(t=P(i)+1,r=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},g.squareRoot=g.sqrt=function(){var e,t,r,n,i,a,o,u=this,s=u.constructor;if(u.s<1){if(!u.s)return new s(0);throw Error(l+"NaN")}for(e=P(u),c=!1,0==(i=Math.sqrt(+u))||i==1/0?(((t=w(u.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new s(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new s(i.toString()),i=o=(r=s.precision)+3;;)if(n=(a=n).plus(x(u,a,o+2)).times(.5),w(a.d).slice(0,o)===(t=w(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(E(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if("9999"!=t)break;o+=4}return c=!0,E(n,r)},g.times=g.mul=function(e){var t,r,n,i,a,o,l,u,s,f=this,d=f.constructor,h=f.d,y=(e=new d(e)).d;if(!f.s||!e.s)return new d(0);for(e.s*=f.s,r=f.e+e.e,(u=h.length)<(s=y.length)&&(a=h,h=y,y=a,o=u,u=s,s=o),a=[],n=o=u+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=u+n;i>n;)l=a[i]+y[n]*h[i-n-1]+t,a[i--]=l%p|0,t=l/p|0;a[i]=(a[i]+t)%p|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,c?E(e,d.precision):e},g.toDecimalPlaces=g.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(b(e,0,a),void 0===t?t=n.rounding:b(t,0,8),E(r,e+P(r)+1,t))},g.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=k(n,!0):(b(e,0,a),void 0===t?t=i.rounding:b(t,0,8),r=k(n=E(new i(n),e+1,t),!0,e+1)),r},g.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?k(i):(b(e,0,a),void 0===t?t=o.rounding:b(t,0,8),r=k((n=E(new o(i),e+P(i)+1,t)).abs(),!1,e+P(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)},g.toInteger=g.toint=function(){var e=this,t=e.constructor;return E(new t(e),P(e)+1,t.rounding)},g.toNumber=function(){return+this},g.toPower=g.pow=function(e){var t,r,n,a,o,u,s=this,d=s.constructor,h=+(e=new d(e));if(!e.s)return new d(i);if(!(s=new d(s)).s){if(e.s<1)throw Error(l+"Infinity");return s}if(s.eq(i))return s;if(n=d.precision,e.eq(i))return E(s,n);if(u=(t=e.e)>=(r=e.d.length-1),o=s.s,u){if((r=h<0?-h:h)<=y){for(a=new d(i),t=Math.ceil(n/7+4),c=!1;r%2&&C((a=a.times(s)).d,t),0!==(r=f(r/2));)C((s=s.times(s)).d,t);return c=!0,e.s<0?new d(i).div(a):E(a,n)}}else if(o<0)throw Error(l+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,c=!1,a=e.times(S(s,n+12)),c=!0,(a=O(a)).s=o,a},g.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?n=k(i,(r=P(i))<=o.toExpNeg||r>=o.toExpPos):(b(e,1,a),void 0===t?t=o.rounding:b(t,0,8),n=k(i=E(new o(i),e,t),e<=(r=P(i))||r<=o.toExpNeg,e)),n},g.toSignificantDigits=g.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(b(e,1,a),void 0===t?t=r.rounding:b(t,0,8)),E(new r(this),e,t)},g.toString=g.valueOf=g.val=g.toJSON=function(){var e=this,t=P(e),r=e.constructor;return k(e,t<=r.toExpNeg||t>=r.toExpPos)};var x=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%p|0,n=r/p|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=n*p+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var c,u,s,f,d,h,y,v,g,m,b,w,x,O,A,j,S,M,_=n.constructor,k=n.s==i.s?1:-1,C=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(l+"Division by zero");for(u=n.e-i.e,S=T.length,A=C.length,v=(y=new _(k)).d=[],s=0;T[s]==(C[s]||0);)++s;if(T[s]>(C[s]||0)&&--u,(w=null==a?a=_.precision:o?a+(P(n)-P(i))+1:a)<0)return new _(0);if(w=w/7+2|0,s=0,1==S)for(f=0,T=T[0],w++;(s<A||f)&&w--;s++)x=f*p+(C[s]||0),v[s]=x/T|0,f=x%T|0;else{for((f=p/(T[0]+1)|0)>1&&(T=e(T,f),C=e(C,f),S=T.length,A=C.length),O=S,m=(g=C.slice(0,S)).length;m<S;)g[m++]=0;(M=T.slice()).unshift(0),j=T[0],T[1]>=p/2&&++j;do{f=0,(c=t(T,g,S,m))<0?(b=g[0],S!=m&&(b=b*p+(g[1]||0)),(f=b/j|0)>1?(f>=p&&(f=p-1),1==(c=t(d=e(T,f),g,h=d.length,m=g.length))&&(f--,r(d,S<h?M:T,h))):(0==f&&(c=f=1),d=T.slice()),(h=d.length)<m&&d.unshift(0),r(g,d,m),-1==c&&(c=t(T,g,S,m=g.length))<1&&(f++,r(g,S<m?M:T,m)),m=g.length):0===c&&(f++,g=[0]),v[s++]=f,c&&g[0]?g[m++]=C[O]||0:(g=[C[O]],m=1)}while((O++<A||void 0!==g[0])&&w--)}return v[0]||v.shift(),y.e=u,E(y,o?a+P(y)+1:a)}}();function O(e,t){var r,n,a,o,l,u=0,f=0,h=e.constructor,p=h.precision;if(P(e)>16)throw Error(s+P(e));if(!e.s)return new h(i);for(null==t?(c=!1,l=p):l=t,o=new h(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(l+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=a=new h(i),h.precision=l;;){if(n=E(n.times(e),l),r=r.times(++u),w((o=a.plus(x(n,r,l))).d).slice(0,l)===w(a.d).slice(0,l)){for(;f--;)a=E(a.times(a),l);return h.precision=p,null==t?(c=!0,E(a,p)):a}a=o}}function P(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function A(e,t,r){if(t>e.LN10.sd())throw c=!0,r&&(e.precision=r),Error(l+"LN10 precision limit exceeded");return E(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function S(e,t){var r,n,a,o,u,s,f,d,h,p=1,y=e,v=y.d,g=y.constructor,m=g.precision;if(y.s<1)throw Error(l+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new g(0);if(null==t?(c=!1,d=m):d=t,y.eq(10))return null==t&&(c=!0),A(g,d);if(d+=10,g.precision=d,n=(r=w(v)).charAt(0),o=P(y),!(Math.abs(o)<15e14))return f=A(g,d+2,m).times(o+""),y=S(new g(n+"."+r.slice(1)),d-10).plus(f),g.precision=m,null==t?(c=!0,E(y,m)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=w((y=y.times(e)).d)).charAt(0),p++;for(o=P(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=u=y=x(y.minus(i),y.plus(i),d),h=E(y.times(y),d),a=3;;){if(u=E(u.times(h),d),w((f=s.plus(x(u,new g(a),d))).d).slice(0,d)===w(s.d).slice(0,d))return s=s.times(2),0!==o&&(s=s.plus(A(g,d+2,m).times(o+""))),s=x(s,new g(p),d),g.precision=m,null==t?(c=!0,E(s,m)):s;s=f,a+=2}}function M(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=f(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),c&&(e.e>v||e.e<-v))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function E(e,t,r){var n,i,a,o,l,u,h,y,g=e.d;for(o=1,a=g[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,h=g[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=g.length))return e;for(h=a=g[y],o=1;a>=10;a/=10)o++;i=(n%=7)-7+o}if(void 0!==r&&(l=h/(a=d(10,o-i-1))%10|0,u=t<0||void 0!==g[y+1]||h%a,u=r<4?(l||u)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||u||6==r&&(n>0?i>0?h/d(10,o-i):0:g[y-1])%10&1||r==(e.s<0?8:7))),t<1||!g[0])return u?(a=P(e),g.length=1,t=t-a-1,g[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(g.length=1,g[0]=e.e=e.s=0),e;if(0==n?(g.length=y,a=1,y--):(g.length=y+1,a=d(10,7-n),g[y]=i>0?(h/d(10,o-i)%d(10,i)|0)*a:0),u)for(;;){if(0==y){(g[0]+=a)==p&&(g[0]=1,++e.e);break}if(g[y]+=a,g[y]!=p)break;g[y--]=0,a=1}for(n=g.length;0===g[--n];)g.pop();if(c&&(e.e>v||e.e<-v))throw Error(s+P(e));return e}function _(e,t){var r,n,i,a,o,l,u,s,f,d,h=e.constructor,y=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),c?E(t,y):t;if(u=e.d,d=t.d,n=t.e,s=e.e,u=u.slice(),o=s-n){for((f=o<0)?(r=u,o=-o,l=d.length):(r=d,n=s,l=u.length),o>(i=Math.max(Math.ceil(y/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=u.length)<(l=d.length))&&(l=i),i=0;i<l;i++)if(u[i]!=d[i]){f=u[i]<d[i];break}o=0}for(f&&(r=u,u=d,d=r,t.s=-t.s),l=u.length,i=d.length-l;i>0;--i)u[l++]=0;for(i=d.length;i>o;){if(u[--i]<d[i]){for(a=i;a&&0===u[--a];)u[a]=p-1;--u[a],u[i]+=p}u[i]-=d[i]}for(;0===u[--l];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(t.d=u,t.e=n,c?E(t,y):t):new h(0)}function k(e,t,r){var n,i=P(e),a=w(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+j(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+j(-i-1)+a,r&&(n=r-o)>0&&(a+=j(n))):i>=o?(a+=j(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+j(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=j(n))),e.s<0?"-"+a:a}function C(e,t){if(e.length>t)return e.length=t,!0}function T(e){if(!e||"object"!==typeof e)throw Error(l+"Object expected");var t,r,n,i=["precision",1,a,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(!(f(n)===n&&n>=i[t+1]&&n<=i[t+2]))throw Error(u+r+": "+n);this[r]=n}if(void 0!==(n=e[r="LN10"])){if(n!=Math.LN10)throw Error(u+r+": "+n);this[r]=new this(n)}return this}o=function e(t){var r,n,i;function a(e){var t=this;if(!(t instanceof a))return new a(e);if(t.constructor=a,e instanceof a)return t.s=e.s,t.e=e.e,void(t.d=(e=e.d)?e.slice():e);if("number"===typeof e){if(0*e!==0)throw Error(u+e);if(e>0)t.s=1;else{if(!(e<0))return t.s=0,t.e=0,void(t.d=[0]);e=-e,t.s=-1}return e===~~e&&e<1e7?(t.e=0,void(t.d=[e])):M(t,e.toString())}if("string"!==typeof e)throw Error(u+e);if(45===e.charCodeAt(0)?(e=e.slice(1),t.s=-1):t.s=1,!h.test(e))throw Error(u+e);M(t,e)}if(a.prototype=g,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=T,void 0===t&&(t={}),t)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o),o.default=o.Decimal=o,i=new o(1),void 0===(n=function(){return o}.call(t,r,t,e))||(e.exports=n)}()},8268:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(2132);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>{})}},8343:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>l,a6:()=>u,jM:()=>W,ss:()=>K});var n=Symbol.for("immer-nothing"),i=Symbol.for("immer-draftable"),a=Symbol.for("immer-state");function o(e){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function l(e){return!!e&&!!e[a]}function u(e){return!!e&&(f(e)||Array.isArray(e)||!!e[i]||!!e.constructor?.[i]||v(e)||g(e))}var s=Object.prototype.constructor.toString();function f(e){if(!e||"object"!==typeof e)return!1;const t=c(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===s}function d(e,t){0===h(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function h(e){const t=e[a];return t?t.type_:Array.isArray(e)?1:v(e)?2:g(e)?3:0}function p(e,t){return 2===h(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function y(e,t,r){const n=h(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function v(e){return e instanceof Map}function g(e){return e instanceof Set}function m(e){return e.copy_||e.base_}function b(e,t){if(v(e))return new Map(e);if(g(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=f(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[a];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(c(e),t)}{const t=c(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function w(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return O(e)||l(e)||!u(e)||(h(e)>1&&(e.set=e.add=e.clear=e.delete=x),Object.freeze(e),t&&Object.entries(e).forEach(e=>{let[t,r]=e;return w(r,!0)})),e}function x(){o(2)}function O(e){return Object.isFrozen(e)}var P,A={};function j(e){const t=A[e];return t||o(0),t}function S(){return P}function M(e,t){t&&(j("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function E(e){_(e),e.drafts_.forEach(C),e.drafts_=null}function _(e){e===P&&(P=e.parent_)}function k(e){return P={drafts_:[],parent_:P,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function C(e){const t=e[a];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[a].modified_&&(E(t),o(4)),u(e)&&(e=D(t,e),t.parent_||I(t,e)),t.patches_&&j("Patches").generateReplacementPatches_(r[a].base_,e,t.patches_,t.inversePatches_)):e=D(t,r,[]),E(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==n?e:void 0}function D(e,t,r){if(O(t))return t;const n=t[a];if(!n)return d(t,(i,a)=>N(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return I(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),d(i,(i,o)=>N(e,n,t,i,o,r,a)),I(e,t,!1),r&&e.patches_&&j("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function N(e,t,r,n,i,a,o){if(l(i)){const o=D(e,i,a&&t&&3!==t.type_&&!p(t.assigned_,n)?a.concat(n):void 0);if(y(r,n,o),!l(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(u(i)&&!O(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;D(e,i),t&&t.scope_.parent_||"symbol"===typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||I(e,i)}}function I(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var z={get(e,t){if(t===a)return e;const r=m(e);if(!p(r,t))return function(e,t,r){const n=B(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!u(n)?n:n===L(e.base_,t)?(U(e),e.copy_[t]=F(n,e)):n},has:(e,t)=>t in m(e),ownKeys:e=>Reflect.ownKeys(m(e)),set(e,t,r){const n=B(m(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=L(m(e),t),c=n?.[a];if(c&&c.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((i=r)===(o=n)?0!==i||1/i===1/o:i!==i&&o!==o)&&(void 0!==r||p(e.base_,t)))return!0;U(e),$(e)}var i,o;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,U(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=m(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){o(11)},getPrototypeOf:e=>c(e.base_),setPrototypeOf(){o(12)}},R={};function L(e,t){const r=e[a];return(r?m(r):e)[t]}function B(e,t){if(!(t in e))return;let r=c(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=c(r)}}function $(e){e.modified_||(e.modified_=!0,e.parent_&&$(e.parent_))}function U(e){e.copy_||(e.copy_=b(e.base_,e.scope_.immer_.useStrictShallowCopy_))}d(z,(e,t)=>{R[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),R.deleteProperty=function(e,t){return R.set.call(this,e,t,void 0)},R.set=function(e,t,r){return z.set.call(this,e[0],t,r,e[0])};function F(e,t){const r=v(e)?j("MapSet").proxyMap_(e,t):g(e)?j("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:S(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,a=z;r&&(i=[n],a=R);const{revoke:o,proxy:c}=Proxy.revocable(i,a);return n.draft_=c,n.revoke_=o,c}(e,t);return(t?t.scope_:S()).drafts_.push(r),r}function K(e){return l(e)||o(10),H(e)}function H(e){if(!u(e)||O(e))return e;const t=e[a];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=b(e,t.scope_.immer_.useStrictShallowCopy_)}else r=b(e,!0);return d(r,(e,t)=>{y(r,e,H(t))}),t&&(t.finalized_=!1),r}var G=new class{constructor(e){var t=this;this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"===typeof e&&"function"!==typeof t){const r=t;t=e;const n=this;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;for(var i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];return n.produce(e,e=>t.call(this,e,...a))}}let i;if("function"!==typeof t&&o(6),void 0!==r&&"function"!==typeof r&&o(7),u(e)){const n=k(this),a=F(e,void 0);let o=!0;try{i=t(a),o=!1}finally{o?E(n):_(n)}return M(n,r),T(i,n)}if(!e||"object"!==typeof e){if(i=t(e),void 0===i&&(i=e),i===n&&(i=void 0),this.autoFreeze_&&w(i,!0),r){const t=[],n=[];j("Patches").generateReplacementPatches_(e,i,t,n),r(t,n)}return i}o(1)},this.produceWithPatches=(e,r)=>{if("function"===typeof e)return function(r){for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.produceWithPatches(r,t=>e(t,...i))};let n,i;return[this.produce(e,r,(e,t)=>{n=e,i=t}),n,i]},"boolean"===typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"===typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){u(e)||o(8),l(e)&&(e=K(e));const t=k(this),r=F(e,void 0);return r[a].isManual_=!0,_(t),r}finishDraft(e,t){const r=e&&e[a];r&&r.isManual_||o(9);const{scope_:n}=r;return M(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=j("Patches").applyPatches_;return l(e)?n(e,t):this.produce(e,e=>n(e,t))}},W=G.produce;G.produceWithPatches.bind(G),G.setAutoFreeze.bind(G),G.setUseStrictShallowCopy.bind(G),G.applyPatches.bind(G),G.createDraft.bind(G),G.finishDraft.bind(G)},8346:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},8360:(e,t,r)=>{"use strict";r.d(t,{CA:()=>v,MC:()=>s,QG:()=>y,Vi:()=>u,cU:()=>f,fR:()=>d});var n=r(2017),i=r(5839);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){c(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:s,addYAxis:f,removeYAxis:d,addZAxis:h,removeZAxis:p,updateYAxisWidth:y}=l.actions,v=l.reducer},8365:(e,t,r)=>{"use strict";var n=r(5043);var i="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},a=n.useSyncExternalStore,o=n.useRef,c=n.useEffect,l=n.useMemo,u=n.useDebugValue},8387:(e,t,r)=>{"use strict";function n(e){var t,r,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=n(e[t]))&&(i&&(i+=" "),i+=r)}else for(r in e)e[r]&&(i&&(i+=" "),i+=r);return i}function i(){for(var e,t,r=0,i="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=n(e))&&(i&&(i+=" "),i+=t);return i}r.d(t,{$:()=>i})},8396:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(5508);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},8420:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},8443:(e,t,r)=>{"use strict";e.exports=r(9717)},8471:(e,t,r)=>{"use strict";r.d(t,{I:()=>H});var n=r(5043);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function c(e){this._context=e}function l(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},c.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class u{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}function h(e){return e<0?-1:1}function p(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),c=(a*i+o*n)/(n+i);return(h(a)+h(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(c))||0}function y(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function v(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,c=(a-n)/3;e._context.bezierCurveTo(n+c,i+c*t,a-c,o-c*r,a,o)}function g(e){this._context=e}function m(e){this._context=new b(e)}function b(e){this._context=e}function w(e){this._context=e}function x(e){var t,r,n=e.length-1,i=new Array(n),a=new Array(n),o=new Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[n-1]=(e[n]+i[n-1])/2,t=0;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function O(e,t){this._context=e,this._t=t}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v(this,this._t0,y(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,v(this,y(this,r=p(this,e,t)),r);break;default:v(this,this._t0,r=p(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(m.prototype=Object.create(g.prototype)).point=function(e,t){g.prototype.point.call(this,t,e)},b.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var P=r(9236),A=r(3809),j=r(7371);function S(e){return e[0]}function M(e){return e[1]}function E(e,t){var r=(0,A.A)(!0),n=null,i=d,a=null,o=(0,j.i)(c);function c(c){var l,u,s,f=(c=(0,P.A)(c)).length,d=!1;for(null==n&&(a=i(s=o())),l=0;l<=f;++l)!(l<f&&r(u=c[l],l,c))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(u,l,c),+t(u,l,c));if(s)return a=null,s+""||null}return e="function"===typeof e?e:void 0===e?S:(0,A.A)(e),t="function"===typeof t?t:void 0===t?M:(0,A.A)(t),c.x=function(t){return arguments.length?(e="function"===typeof t?t:(0,A.A)(+t),c):e},c.y=function(e){return arguments.length?(t="function"===typeof e?e:(0,A.A)(+e),c):t},c.defined=function(e){return arguments.length?(r="function"===typeof e?e:(0,A.A)(!!e),c):r},c.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),c):i},c.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),c):n},c}function _(e,t,r){var n=null,i=(0,A.A)(!0),a=null,o=d,c=null,l=(0,j.i)(u);function u(u){var s,f,d,h,p,y=(u=(0,P.A)(u)).length,v=!1,g=new Array(y),m=new Array(y);for(null==a&&(c=o(p=l())),s=0;s<=y;++s){if(!(s<y&&i(h=u[s],s,u))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),d=s-1;d>=f;--d)c.point(g[d],m[d]);c.lineEnd(),c.areaEnd()}v&&(g[s]=+e(h,s,u),m[s]=+t(h,s,u),c.point(n?+n(h,s,u):g[s],r?+r(h,s,u):m[s]))}if(p)return c=null,p+""||null}function s(){return E().defined(i).curve(o).context(a)}return e="function"===typeof e?e:void 0===e?S:(0,A.A)(+e),t="function"===typeof t?t:void 0===t?(0,A.A)(0):(0,A.A)(+t),r="function"===typeof r?r:void 0===r?M:(0,A.A)(+r),u.x=function(t){return arguments.length?(e="function"===typeof t?t:(0,A.A)(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"===typeof t?t:(0,A.A)(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"===typeof e?e:(0,A.A)(+e),u):n},u.y=function(e){return arguments.length?(t="function"===typeof e?e:(0,A.A)(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"===typeof e?e:(0,A.A)(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"===typeof e?e:(0,A.A)(+e),u):r},u.lineX0=u.lineY0=function(){return s().x(e).y(t)},u.lineY1=function(){return s().x(e).y(r)},u.lineX1=function(){return s().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"===typeof e?e:(0,A.A)(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(c=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=c=null:c=o(a=e),u):a},u}var k=r(8387),C=r(7287),T=r(240),D=r(6307),N=r(2574);function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},I.apply(null,arguments)}function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){L(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function L(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var B={curveBasisClosed:function(e){return new c(e)},curveBasisOpen:function(e){return new l(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new u(e,!0)},curveBumpY:function(e){return new u(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:d,curveMonotoneX:function(e){return new g(e)},curveMonotoneY:function(e){return new m(e)},curveNatural:function(e){return new w(e)},curveStep:function(e){return new O(e,.5)},curveStepAfter:function(e){return new O(e,1)},curveStepBefore:function(e){return new O(e,0)}},$=e=>(0,N.H)(e.x)&&(0,N.H)(e.y),U=e=>e.x,F=e=>e.y,K=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,c=((e,t)=>{if("function"===typeof e)return e;var r="curve".concat((0,D.Zb)(e));return"curveMonotone"!==r&&"curveBump"!==r||!t?B[r]||d:B["".concat(r).concat("vertical"===t?"Y":"X")]})(r,a),l=o?n.filter($):n;if(Array.isArray(i)){var u=o?i.filter(e=>$(e)):i,s=l.map((e,t)=>R(R({},e),{},{base:u[t]}));return(t="vertical"===a?_().y(F).x1(U).x0(e=>e.base.x):_().x(U).y1(F).y0(e=>e.base.y)).defined($).curve(c),t(s)}return(t="vertical"===a&&(0,D.Et)(i)?_().y(F).x1(U).x0(i):(0,D.Et)(i)?_().x(U).y1(F).y0(i):E().x(U).y(F)).defined($).curve(c),t(l)},H=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?K(e):i;return n.createElement("path",I({},(0,T.J9)(e,!1),(0,C._U)(e),{className:(0,k.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},8499:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!==typeof n)throw new TypeError("The listener must be a function");var c=new i(n,a||e,o),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],c]:e._events[l].push(c):(e._events[l]=c,e._eventsCount++),e}function o(e,t){0===--e._eventsCount?e._events=new n:delete e._events[t]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),c.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},c.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=new Array(a);i<a;i++)o[i]=n[i].fn;return o},c.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},c.prototype.emit=function(e,t,n,i,a,o){var c=r?r+e:e;if(!this._events[c])return!1;var l,u,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(u=1,l=new Array(f-1);u<f;u++)l[u-1]=arguments[u];s.fn.apply(s.context,l)}else{var d,h=s.length;for(u=0;u<h;u++)switch(s[u].once&&this.removeListener(e,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,t);break;case 3:s[u].fn.call(s[u].context,t,n);break;case 4:s[u].fn.call(s[u].context,t,n,i);break;default:if(!l)for(d=1,l=new Array(f-1);d<f;d++)l[d-1]=arguments[d];s[u].fn.apply(s[u].context,l)}}return!0},c.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},c.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},c.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var c=this._events[a];if(c.fn)c.fn!==t||i&&!c.once||n&&c.context!==n||o(this,a);else{for(var l=0,u=[],s=c.length;l<s;l++)(c[l].fn!==t||i&&!c[l].once||n&&c[l].context!==n)&&u.push(c[l]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,e.exports=c},8602:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(8139),i=r.n(n),a=r(5043),o=r(7852),c=r(579);const l=a.forwardRef((e,t)=>{const[{className:r,...n},{as:a="div",bsPrefix:l,spans:u}]=function(e){let{as:t,bsPrefix:r,className:n,...a}=e;r=(0,o.oU)(r,"col");const c=(0,o.gy)(),l=(0,o.Jm)(),u=[],s=[];return c.forEach(e=>{const t=a[e];let n,i,o;delete a[e],"object"===typeof t&&null!=t?({span:n,offset:i,order:o}=t):n=t;const c=e!==l?`-${e}`:"";n&&u.push(!0===n?`${r}${c}`:`${r}${c}-${n}`),null!=o&&s.push(`order${c}-${o}`),null!=i&&s.push(`offset${c}-${i}`)}),[{...a,className:i()(n,...u,...s)},{as:t,bsPrefix:r,spans:u}]}(e);return(0,c.jsx)(a,{...n,ref:t,className:i()(r,!u.length&&l)})});l.displayName="Col";const u=l},8628:(e,t,r)=>{"use strict";r.d(t,{A:()=>C});var n=r(8139),i=r.n(n),a=r(5043),o=r(7852),c=r(579);const l=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a="div",...l}=e;return n=(0,o.oU)(n,"card-body"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});l.displayName="CardBody";const u=l,s=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a="div",...l}=e;return n=(0,o.oU)(n,"card-footer"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});s.displayName="CardFooter";const f=s;var d=r(1778);const h=a.forwardRef((e,t)=>{let{bsPrefix:r,className:n,as:l="div",...u}=e;const s=(0,o.oU)(r,"card-header"),f=(0,a.useMemo)(()=>({cardHeaderBsPrefix:s}),[s]);return(0,c.jsx)(d.A.Provider,{value:f,children:(0,c.jsx)(l,{ref:t,...u,className:i()(n,s)})})});h.displayName="CardHeader";const p=h,y=a.forwardRef((e,t)=>{let{bsPrefix:r,className:n,variant:a,as:l="img",...u}=e;const s=(0,o.oU)(r,"card-img");return(0,c.jsx)(l,{ref:t,className:i()(a?`${s}-${a}`:s,n),...u})});y.displayName="CardImg";const v=y,g=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a="div",...l}=e;return n=(0,o.oU)(n,"card-img-overlay"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});g.displayName="CardImgOverlay";const m=g,b=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a="a",...l}=e;return n=(0,o.oU)(n,"card-link"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});b.displayName="CardLink";const w=b;var x=r(4488);const O=(0,x.A)("h6"),P=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a=O,...l}=e;return n=(0,o.oU)(n,"card-subtitle"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});P.displayName="CardSubtitle";const A=P,j=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a="p",...l}=e;return n=(0,o.oU)(n,"card-text"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});j.displayName="CardText";const S=j,M=(0,x.A)("h5"),E=a.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:a=M,...l}=e;return n=(0,o.oU)(n,"card-title"),(0,c.jsx)(a,{ref:t,className:i()(r,n),...l})});E.displayName="CardTitle";const _=E,k=a.forwardRef((e,t)=>{let{bsPrefix:r,className:n,bg:a,text:l,border:s,body:f=!1,children:d,as:h="div",...p}=e;const y=(0,o.oU)(r,"card");return(0,c.jsx)(h,{ref:t,...p,className:i()(n,y,a&&`bg-${a}`,l&&`text-${l}`,s&&`border-${s}`),children:f?(0,c.jsx)(u,{children:d}):d})});k.displayName="Card";const C=Object.assign(k,{Img:v,Title:_,Subtitle:A,Body:u,Link:w,Text:S,Header:p,Footer:f,ImgOverlay:m})},8796:(e,t,r)=>{"use strict";r.d(t,{Kp:()=>p,WX:()=>v,fz:()=>y,hj:()=>s,rY:()=>d,sk:()=>l,yi:()=>f});r(5043);var n=r(787),i=r(4721),a=r(3859),o=r(3987),c=r(7886),l=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(c.U),l=null===(e=(0,n.G)(c.C))||void 0===e?void 0:e.padding;return t&&a&&l?{width:a.width-l.left-l.right,height:a.height-l.top-l.bottom,x:l.left,y:l.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!==(e=(0,n.G)(i.GO))&&void 0!==e?e:u},f=()=>(0,n.G)(a.Lp),d=()=>(0,n.G)(a.A$),h={top:0,right:0,bottom:0,left:0},p=()=>{var e;return null!==(e=(0,n.G)(e=>e.layout.margin))&&void 0!==e?e:h},y=e=>e.layout.layoutType,v=()=>(0,n.G)(y)},8845:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8268),i=r(3272),a=r(8396),o=r(9323),c=r(5303);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){const i=o.get(r,e);return void 0===i?c.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},9236:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});Array.prototype.slice;function n(e){return"object"===typeof e&&"length"in e?e:Array.from(e)}},9256:(e,t,r)=>{"use strict";r.d(t,{Vi:()=>s,g5:()=>u,iZ:()=>h});var n=r(2017),i=r(8343),a=r(5839),o=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,a.h4)(t.payload))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,a.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,a.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,a.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:c,removeBar:l,addCartesianGraphicalItem:u,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:d}=o.actions,h=o.reducer},9323:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(7260),i=r(2489),a=r(3272),o=r(1444);t.get=function e(t,r,c){if(null==t)return c;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return c;const a=t[r];return void 0===a?i.isDeepKey(r)?e(t,o.toPath(r),c):c:a}case"number":case"symbol":{"number"===typeof r&&(r=a.toKey(r));const e=t[r];return void 0===e?c:e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let i=e;for(let a=0;a<t.length;a++){if(null==i)return r;if(n.isUnsafeProperty(t[a]))return r;i=i[t[a]]}if(void 0===i)return r;return i}(t,r,c);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return c;const e=t[r];return void 0===e?c:e}}}},9330:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4385),i=r(6443),a=r(5990),o=r(7599),c=r(9972);function l(e,t,r,n,i,a,o){const c=o(e,t,r,n,i,a);if(void 0!==c)return c;if(typeof e===typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===t;case"number":return e===t||Object.is(e,t);case"object":return u(e,t,a,o)}return u(e,t,a,o)}function u(e,t,r,s){if(Object.is(e,t))return!0;let f=a.getTag(e),d=a.getTag(t);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return e.toString()===t.toString();case o.numberTag:{const r=e.valueOf(),n=t.valueOf();return c.eq(r,n)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(e.valueOf(),t.valueOf());case o.regexpTag:return e.source===t.source&&e.flags===t.flags;case o.functionTag:return e===t}const h=(r=r??new Map).get(e),p=r.get(t);if(null!=h&&null!=p)return h===t;r.set(e,t),r.set(t,e);try{switch(f){case o.mapTag:if(e.size!==t.size)return!1;for(const[n,i]of e.entries())if(!t.has(n)||!l(i,t.get(n),n,e,t,r,s))return!1;return!0;case o.setTag:{if(e.size!==t.size)return!1;const n=Array.from(e.values()),i=Array.from(t.values());for(let a=0;a<n.length;a++){const o=n[a],c=i.findIndex(n=>l(o,n,void 0,e,t,r,s));if(-1===c)return!1;i.splice(c,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!==typeof Buffer&&Buffer.isBuffer(e)!==Buffer.isBuffer(t))return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!l(e[n],t[n],n,e,t,r,s))return!1;return!0;case o.arrayBufferTag:return e.byteLength===t.byteLength&&u(new Uint8Array(e),new Uint8Array(t),r,s);case o.dataViewTag:return e.byteLength===t.byteLength&&e.byteOffset===t.byteOffset&&u(new Uint8Array(e),new Uint8Array(t),r,s);case o.errorTag:return e.name===t.name&&e.message===t.message;case o.objectTag:{if(!(u(e.constructor,t.constructor,r,s)||n.isPlainObject(e)&&n.isPlainObject(t)))return!1;const a=[...Object.keys(e),...i.getSymbols(e)],o=[...Object.keys(t),...i.getSymbols(t)];if(a.length!==o.length)return!1;for(let n=0;n<a.length;n++){const i=a[n],o=e[i];if(!Object.hasOwn(t,i))return!1;if(!l(o,t[i],i,e,t,r,s))return!1}return!0}default:return!1}}finally{r.delete(e),r.delete(t)}}t.isEqualWith=function(e,t,r){return l(e,t,void 0,void 0,void 0,void 0,r)}},9461:(e,t,r)=>{"use strict";e.exports=r(2330)},9599:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},9602:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i});var i}},9645:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6392),i=r(8346),a=r(1203);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},9709:(e,t)=>{"use strict";function r(e){return"symbol"===typeof e?1:null===e?2:void 0===e?3:e!==e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});t.compareValues=(e,t,n)=>{if(e!==t){const i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},9717:(e,t,r)=>{"use strict";var n=r(5043),i=r(9461);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=i.useSyncExternalStore,c=n.useRef,l=n.useEffect,u=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=c(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=u(function(){function e(e){if(!l){if(l=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return c=t}return c=e}if(t=c,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,c=r)}var o,c,l=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]);var h=o(e,f[0],f[1]);return l(function(){d.hasValue=!0,d.value=h},[h]),s(h),h}},9859:(e,t,r)=>{e.exports=r(7231).range},9921:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"===typeof e&&null!==e}},9923:(e,t,r)=>{"use strict";r.d(t,{m:()=>ye});var n=r(5043),i=r(7950),a=r(3821),o=r.n(a),c=r(8387),l=r(6307);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},u.apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){return Array.isArray(e)&&(0,l.vh)(e[0])&&(0,l.vh)(e[1])?e.join(" ~ "):e}var p=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:d,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,w=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),x=f({margin:0},a),O=!(0,l.uy)(g),P=O?g:"",A=(0,c.$)("recharts-default-tooltip",y),j=(0,c.$)("recharts-tooltip-label",v);O&&m&&void 0!==s&&null!==s&&(P=m(g,s));var S=b?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",u({className:A,style:w},S),n.createElement("p",{className:j,style:x},n.isValidElement(P)?P:"".concat(P)),(()=>{if(s&&s.length){var e=(p?o()(s,p):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||d||h,{value:o,name:c}=e,u=o,p=c;if(a){var y=a(o,c,e,r,s);if(Array.isArray(y))[u,p]=y;else{if(null==y)return null;u=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,l.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,l.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},y="recharts-tooltip-wrapper",v={visibility:"hidden"};function g(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,c.$)(y,{["".concat(y,"-right")]:(0,l.Et)(r)&&t&&(0,l.Et)(t.x)&&r>=t.x,["".concat(y,"-left")]:(0,l.Et)(r)&&t&&(0,l.Et)(t.x)&&r<t.x,["".concat(y,"-bottom")]:(0,l.Et)(n)&&t&&(0,l.Et)(t.y)&&n>=t.y,["".concat(y,"-top")]:(0,l.Et)(n)&&t&&(0,l.Et)(t.y)&&n<t.y})}function m(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:c,viewBox:u,viewBoxDimension:s}=e;if(a&&(0,l.Et)(a[n]))return a[n];var f=r[n]-c-(i>0?i:0),d=r[n]+i;if(t[n])return o[n]?f:d;var h=u[n];return null==h?0:o[n]?f<h?Math.max(d,h):Math.max(f,h):null==s?0:d+c>h+s?Math.max(f,h):Math.max(d,h)}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.PureComponent{constructor(){super(...arguments),x(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),x(this,"handleKeyDown",e=>{var t,r,n,i;"Escape"===e.key&&this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:c,isAnimationActive:l,offset:u,position:s,reverseDirection:f,useTranslate3d:d,viewBox:h,wrapperStyle:p,lastBoundingBox:y,innerRef:b,hasPortalFromProps:x}=this.props,{cssClasses:O,cssProperties:P}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:c,reverseDirection:l,tooltipBox:u,useTranslate3d:s,viewBox:f}=e;return t=u.height>0&&u.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=m({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:c,reverseDirection:l,tooltipDimension:u.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=m({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:c,reverseDirection:l,tooltipDimension:u.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:s}):v,{cssProperties:t,cssClasses:g({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:u,position:s,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:h}),A=x?{}:w(w({transition:l&&e?"transform ".concat(r,"ms ").concat(i):void 0},P),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),j=w(w({},A),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},p);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:O,style:j,ref:b},a)}}var P=r(6015),A=r(2598),j=r(8796),S=r(9949),M=r(982),E=r(8471),_=r(240),k=["x","y","top","left","width","height","className"];function C(){return C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},C.apply(null,arguments)}function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function D(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var N=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),I=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:u=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){D(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:u},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,k));return(0,l.Et)(t)&&(0,l.Et)(r)&&(0,l.Et)(o)&&(0,l.Et)(u)&&(0,l.Et)(i)&&(0,l.Et)(a)?n.createElement("path",C({},(0,_.J9)(f,!0),{className:(0,c.$)("recharts-cross",s),d:N(t,r,o,u,i,a)})):null};var z=r(6371),R=r(5654);function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},L.apply(null,arguments)}var B=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,l=r>=0?1:-1,u=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+c*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(e+l*s[0],",").concat(t)),a+="L ".concat(e+r-l*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+c*s[1])),a+="L ".concat(e+r,",").concat(t+n-c*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(e+r-l*s[2],",").concat(t+n)),a+="L ".concat(e+l*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-c*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+c*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+l*d,",").concat(t,"\n            L ").concat(e+r-l*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r,",").concat(t+c*d,"\n            L ").concat(e+r,",").concat(t+n-c*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r-l*d,",").concat(t+n,"\n            L ").concat(e+l*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e,",").concat(t+n-c*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},$={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},U=e=>{var t=(0,z.e)(e,$),r=(0,n.useRef)(null),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(t){}},[]);var{x:o,y:l,width:u,height:s,radius:f,className:d}=t,{animationEasing:h,animationDuration:p,animationBegin:y,isAnimationActive:v,isUpdateAnimationActive:g}=t;if(o!==+o||l!==+l||u!==+u||s!==+s||0===u||0===s)return null;var m=(0,c.$)("recharts-rectangle",d);return g?n.createElement(R.i,{canBegin:i>0,from:{width:u,height:s,x:o,y:l},to:{width:u,height:s,x:o,y:l},duration:p,animationEasing:h,isActive:g},e=>{var{width:a,height:o,x:c,y:l}=e;return n.createElement(R.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:p,isActive:v,easing:h},n.createElement("path",L({},(0,_.J9)(t,!0),{className:m,d:B(c,l,a,o,f),ref:r})))}):n.createElement("path",L({},(0,_.J9)(t,!0),{className:m,d:B(o,l,u,s,f)}))},F=r(165);function K(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,F.IZ)(t,r,n,i),(0,F.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},H.apply(null,arguments)}var G=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:c,cornerIsExternal:l}=e,u=c*(o?1:-1)+n,s=Math.asin(c/u)/F.Kg,f=l?i:i+a*s,d=l?i-a*s:i;return{center:(0,F.IZ)(t,r,u,f),circleTangency:(0,F.IZ)(t,r,n,f),lineTangency:(0,F.IZ)(t,r,u*Math.cos(s*F.Kg),d),theta:s}},W=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,c=((e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999))(a,o),u=a+c,s=(0,F.IZ)(t,r,i,a),f=(0,F.IZ)(t,r,i,u),d="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var h=(0,F.IZ)(t,r,n,a),p=(0,F.IZ)(t,r,n,u);d+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(h.x,",").concat(h.y," Z")}else d+="L ".concat(t,",").concat(r," Z");return d},V={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},q=e=>{var t=(0,z.e)(e,V),{cx:r,cy:i,innerRadius:a,outerRadius:o,cornerRadius:u,forceCornerRadius:s,cornerIsExternal:f,startAngle:d,endAngle:h,className:p}=t;if(o<a||d===h)return null;var y,v=(0,c.$)("recharts-sector",p),g=o-a,m=(0,l.F4)(u,g,0,!0);return y=m>0&&Math.abs(d-h)<360?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:c,startAngle:u,endAngle:s}=e,f=(0,l.sA)(s-u),{circleTangency:d,lineTangency:h,theta:p}=G({cx:t,cy:r,radius:i,angle:u,sign:f,cornerRadius:a,cornerIsExternal:c}),{circleTangency:y,lineTangency:v,theta:g}=G({cx:t,cy:r,radius:i,angle:s,sign:-f,cornerRadius:a,cornerIsExternal:c}),m=c?Math.abs(u-s):Math.abs(u-s)-p-g;if(m<0)return o?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*-a,",0\n      "):W({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var b="M ".concat(h.x,",").concat(h.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(m>180),",").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var{circleTangency:w,lineTangency:x,theta:O}=G({cx:t,cy:r,radius:n,angle:u,sign:f,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),{circleTangency:P,lineTangency:A,theta:j}=G({cx:t,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),S=c?Math.abs(u-s):Math.abs(u-s)-O-j;if(S<0&&0===a)return"".concat(b,"L").concat(t,",").concat(r,"Z");b+="L".concat(A.x,",").concat(A.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(f>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"Z")}else b+="L".concat(t,",").concat(r,"Z");return b})({cx:r,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(m,g/2),forceCornerRadius:s,cornerIsExternal:f,startAngle:d,endAngle:h}):W({cx:r,cy:i,innerRadius:a,outerRadius:o,startAngle:d,endAngle:h}),n.createElement("path",H({},(0,_.J9)(t,!0),{className:v,d:y}))};function Z(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return K(t);var{cx:c,cy:l,innerRadius:u,outerRadius:s,angle:f}=t,d=(0,F.IZ)(c,l,u,f),h=(0,F.IZ)(c,l,s,f);n=d.x,i=d.y,a=h.x,o=h.y}return[{x:n,y:i},{x:a,y:o}]}var Y=r(3374),X=r(1428);function J(){return J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},J.apply(null,arguments)}function Q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ee(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(r),!0).forEach(function(t){te(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function te(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function re(e){var t,r,{coordinate:i,payload:a,index:o,offset:l,tooltipAxisBandSize:u,layout:s,cursor:f,tooltipEventType:d,chartName:h}=e,p=i,y=a,v=o;if(!f||!p||"ScatterChart"!==h&&"axis"!==d)return null;if("ScatterChart"===h)t=p,r=I;else if("BarChart"===h)t=function(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:r.left+.5,y:"horizontal"===e?r.top+.5:t.y-i,width:"horizontal"===e?n:r.width-1,height:"horizontal"===e?r.height-1:n}}(s,p,l,u),r=U;else if("radial"===s){var{cx:g,cy:m,radius:b,startAngle:w,endAngle:x}=K(p);t={cx:g,cy:m,startAngle:w,endAngle:x,innerRadius:b,outerRadius:b},r=q}else t={points:Z(s,p,l)},r=E.I;var O="object"===typeof f&&"className"in f?f.className:void 0,P=ee(ee(ee(ee({stroke:"#ccc",pointerEvents:"none"},l),t),(0,_.J9)(f,!1)),{},{payload:y,payloadIndex:v,className:(0,c.$)("recharts-tooltip-cursor",O)});return(0,n.isValidElement)(f)?(0,n.cloneElement)(f,P):(0,n.createElement)(r,P)}function ne(e){var t=(0,Y.O)(),r=(0,j.hj)(),i=(0,j.WX)(),a=(0,X.fW)();return n.createElement(re,J({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var ie=r(317),ae=r(787),oe=r(2768),ce=r(425),le=r(2277);function ue(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ue(Object(r),!0).forEach(function(t){fe(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ue(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function de(e){return e.dataKey}var he=[],pe={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!P.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function ye(e){var t=(0,z.e)(e,pe),{active:r,allowEscapeViewBox:a,animationDuration:o,animationEasing:c,content:l,filterNull:u,isAnimationActive:s,offset:f,payloadUniqBy:d,position:h,reverseDirection:y,useTranslate3d:v,wrapperStyle:g,cursor:m,shared:b,trigger:w,defaultIndex:x,portal:P,axisId:E}=t,_=(0,ae.j)(),k="number"===typeof x?String(x):x;(0,n.useEffect)(()=>{_((0,oe.UF)({shared:b,trigger:w,axisId:E,active:r,defaultIndex:k}))},[_,b,w,E,r,k]);var C=(0,j.sk)(),T=(0,S.$)(),D=(0,le.Td)(b),{activeIndex:N,isActive:I}=(0,ae.G)(e=>(0,X.yn)(e,D,w,k)),R=(0,ae.G)(e=>(0,X.u9)(e,D,w,k)),L=(0,ae.G)(e=>(0,X.BZ)(e,D,w,k)),B=(0,ae.G)(e=>(0,X.dS)(e,D,w,k)),$=R,U=(0,ie.X)(),F=null!==r&&void 0!==r?r:I,[K,H]=(0,M.V)([$,F]),G="axis"===D?L:void 0;(0,ce.m7)(D,w,B,G,N,F);var W=null!==P&&void 0!==P?P:U;if(null==W)return null;var V=null!==$&&void 0!==$?$:he;F||(V=he),u&&V.length&&(V=(0,A.s)($.filter(e=>null!=e.value&&(!0!==e.hide||t.includeHidden)),d,de));var q=V.length>0,Z=n.createElement(O,{allowEscapeViewBox:a,animationDuration:o,animationEasing:c,isAnimationActive:s,active:F,coordinate:B,hasPayload:q,offset:f,position:h,reverseDirection:y,useTranslate3d:v,viewBox:C,wrapperStyle:g,lastBoundingBox:K,innerRef:H,hasPortalFromProps:Boolean(P)},function(e,t){return n.isValidElement(e)?n.cloneElement(e,t):"function"===typeof e?n.createElement(e,t):n.createElement(p,t)}(l,se(se({},t),{},{payload:V,label:G,active:F,coordinate:B,accessibilityLayer:T})));return n.createElement(n.Fragment,null,(0,i.createPortal)(Z,W),F&&n.createElement(ne,{cursor:m,tooltipEventType:D,coordinate:B,payload:$,index:N}))}},9949:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(787),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},9972:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}}}]);
//# sourceMappingURL=141.14649523.chunk.js.map