{"version": 3, "file": "static/js/93.4cc1ddf4.chunk.js", "mappings": "mNAMA,MA2DA,EA3DkBA,KACd,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,GA6BvC,OACIO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAmDC,MAAO,CAAEC,UAAW,QAASC,UAC3FJ,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACH,MAAO,CAAEI,MAAO,SAAUF,UAC5BG,EAAAA,EAAAA,MAACF,EAAAA,EAAKG,KAAI,CAAAJ,SAAA,EACNJ,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mBAAkBG,SAAEf,EAAE,WACnCO,IAASI,EAAAA,EAAAA,KAACS,EAAAA,EAAK,CAACC,QAAQ,SAAQN,SAAER,KACnCW,EAAAA,EAAAA,MAACI,EAAAA,EAAI,CAACC,SAjCDC,UACjBC,EAAEC,iBACFlB,EAAS,IACTE,GAAW,GAEX,IACI,MAAMiB,GAAWC,EAAAA,EAAAA,MACX,KAAEC,EAAI,MAAEtB,SAAgBoB,EAASG,KAAKC,mBAAmB,CAC3D7B,MAAOA,EACPG,SAAUA,IAGd,GAAIE,EACA,MAAMA,EAGVyB,QAAQC,IAAI,iBAAkBJ,EAGlC,CAAE,MAAOK,GACL1B,EAAS0B,EAAIC,SAAWnC,EAAE,iBAC1BgC,QAAQzB,MAAM,eAAgB2B,EAClC,CAEAxB,GAAW,IAS8BK,SAAA,EACzBG,EAAAA,EAAAA,MAACI,EAAAA,EAAKc,MAAK,CAACC,GAAG,QAAOtB,SAAA,EAClBJ,EAAAA,EAAAA,KAACW,EAAAA,EAAKgB,MAAK,CAAAvB,SAAEf,EAAE,oBACfW,EAAAA,EAAAA,KAACW,EAAAA,EAAKiB,QAAO,CAACC,KAAK,QAAQC,UAAQ,EAACC,MAAOxC,EAAOyC,SAAWlB,GAAMtB,EAASsB,EAAEmB,OAAOF,aAEzFxB,EAAAA,EAAAA,MAACI,EAAAA,EAAKc,MAAK,CAACC,GAAG,WAAUtB,SAAA,EACrBJ,EAAAA,EAAAA,KAACW,EAAAA,EAAKgB,MAAK,CAAAvB,SAAEf,EAAE,eACfW,EAAAA,EAAAA,KAACW,EAAAA,EAAKiB,QAAO,CAACC,KAAK,WAAWC,UAAQ,EAACC,MAAOrC,EAAUsC,SAAWlB,GAAMnB,EAAYmB,EAAEmB,OAAOF,aAElG/B,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACC,SAAUrC,EAASG,UAAU,aAAa4B,KAAK,SAAQzB,SAChDf,EAAVS,EAAY,aAAkB,qB", "sources": ["pages/LoginPage.js"], "sourcesContent": ["\nimport React, { useState } from 'react';\nimport { Form, Button, Card, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../supabaseClient';\n\nconst LoginPage = () => {\n    const { t } = useTranslation();\n    const [email, setEmail] = useState('');\n    const [password, setPassword] = useState('');\n    const [error, setError] = useState('');\n    const [loading, setLoading] = useState(false);\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n\n        try {\n            const supabase = getSupabase();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: email,\n                password: password,\n            });\n\n            if (error) {\n                throw error;\n            }\n\n            console.log('Login Success:', data);\n            // TODO: Handle successful login (e.g., save session, redirect based on role)\n\n        } catch (err) {\n            setError(err.message || t('login_failed'));\n            console.error('Login Error:', err);\n        }\n\n        setLoading(false);\n    };\n\n    return (\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '80vh' }}>\n            <Card style={{ width: '400px' }}>\n                <Card.Body>\n                    <h2 className=\"text-center mb-4\">{t('login')}</h2>\n                    {error && <Alert variant=\"danger\">{error}</Alert>}\n                    <Form onSubmit={handleSubmit}>\n                        <Form.Group id=\"email\">\n                            <Form.Label>{t('email_address')}</Form.Label>\n                            <Form.Control type=\"email\" required value={email} onChange={(e) => setEmail(e.target.value)} />\n                        </Form.Group>\n                        <Form.Group id=\"password\">\n                            <Form.Label>{t('password')}</Form.Label>\n                            <Form.Control type=\"password\" required value={password} onChange={(e) => setPassword(e.target.value)} />\n                        </Form.Group>\n                        <Button disabled={loading} className=\"w-100 mt-3\" type=\"submit\">\n                            {loading ? t('logging_in') : t('login')}\n                        </Button>\n                    </Form>\n                </Card.Body>\n            </Card>\n        </div>\n    );\n};\n\nexport default LoginPage;\n"], "names": ["LoginPage", "t", "useTranslation", "email", "setEmail", "useState", "password", "setPassword", "error", "setError", "loading", "setLoading", "_jsx", "className", "style", "minHeight", "children", "Card", "width", "_jsxs", "Body", "<PERSON><PERSON>", "variant", "Form", "onSubmit", "async", "e", "preventDefault", "supabase", "getSupabase", "data", "auth", "signInWithPassword", "console", "log", "err", "message", "Group", "id", "Label", "Control", "type", "required", "value", "onChange", "target", "<PERSON><PERSON>", "disabled"], "sourceRoot": ""}