
import React, { useState } from 'react';
import { Form, Button, Card, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../supabaseClient';

const LoginPage = () => {
    const { t } = useTranslation();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            const supabase = getSupabase();
            const { data, error } = await supabase.auth.signInWithPassword({
                email: email,
                password: password,
            });

            if (error) {
                throw error;
            }

            console.log('Login Success:', data);
            // TODO: Handle successful login (e.g., save session, redirect based on role)

        } catch (err) {
            setError(err.message || t('login_failed'));
            console.error('Login Error:', err);
        }

        setLoading(false);
    };

    return (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '80vh' }}>
            <Card style={{ width: '400px' }}>
                <Card.Body>
                    <h2 className="text-center mb-4">{t('login')}</h2>
                    {error && <Alert variant="danger">{error}</Alert>}
                    <Form onSubmit={handleSubmit}>
                        <Form.Group id="email">
                            <Form.Label>{t('email_address')}</Form.Label>
                            <Form.Control type="email" required value={email} onChange={(e) => setEmail(e.target.value)} />
                        </Form.Group>
                        <Form.Group id="password">
                            <Form.Label>{t('password')}</Form.Label>
                            <Form.Control type="password" required value={password} onChange={(e) => setPassword(e.target.value)} />
                        </Form.Group>
                        <Button disabled={loading} className="w-100 mt-3" type="submit">
                            {loading ? t('logging_in') : t('login')}
                        </Button>
                    </Form>
                </Card.Body>
            </Card>
        </div>
    );
};

export default LoginPage;
