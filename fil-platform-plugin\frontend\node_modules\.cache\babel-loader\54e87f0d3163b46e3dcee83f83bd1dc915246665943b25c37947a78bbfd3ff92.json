{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{Container,Row,Col,Card,Button}from'react-bootstrap';import{<PERSON><PERSON><PERSON>,Line,XAxis,YAxis,CartesianGrid,<PERSON>ltip,Legend,ResponsiveContainer}from'recharts';import{useNavigate}from'react-router-dom';// Mock data, we will fetch this from Supabase later\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const chartData=[{name:'7/1',fil:0.2,usd:1.1},{name:'7/2',fil:0.25,usd:1.3},{name:'7/3',fil:0.22,usd:1.2},{name:'7/4',fil:0.3,usd:1.5},{name:'7/5',fil:0.28,usd:1.4},{name:'7/6',fil:0.35,usd:1.8},{name:'7/7',fil:0.4,usd:2.0}];const StatCard=_ref=>{let{title,value,subValue,variant}=_ref;return/*#__PURE__*/_jsx(Card,{className:`bg-${variant} text-white mb-3`,children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:title}),/*#__PURE__*/_jsx(\"h3\",{children:value}),/*#__PURE__*/_jsx(\"p\",{children:subValue})]})});};const CustomerDashboard=()=>{const{t}=useTranslation();const navigate=useNavigate();const handleNavigation=path=>{console.log('Navigating to:',path);console.log('Current URL:',window.location.href);navigate(path);};return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(\"h2\",{children:t('dashboard')})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('total_earnings'),value:\"12,345.67 FIL\",subValue:\"\\u2248 $50,123.45 USD\",variant:\"primary\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('yesterday_earnings'),value:\"123.45 FIL\",subValue:\"\\u2248 $501.23 USD\",variant:\"success\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('available_balance'),value:\"1,234.56 FIL\",subValue:\"\\u2248 $5,012.34 USD\",variant:\"info\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('power_pledge'),value:\"10,000.00 FIL\",subValue:\"\\u2248 $40,500.00 USD\",variant:\"warning\"})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('earnings_trend')}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:400,children:/*#__PURE__*/_jsxs(LineChart,{data:chartData,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"name\"}),/*#__PURE__*/_jsx(YAxis,{yAxisId:\"left\",label:{value:t('fil'),angle:-90,position:'insideLeft'}}),/*#__PURE__*/_jsx(YAxis,{yAxisId:\"right\",orientation:\"right\",label:{value:t('usd'),angle:-90,position:'insideRight'}}),/*#__PURE__*/_jsx(Tooltip,{formatter:(value,name)=>[value,t(name)]}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{yAxisId:\"left\",type:\"monotone\",dataKey:\"fil\",stroke:\"#8884d8\",name:t('FIL_earnings')}),/*#__PURE__*/_jsx(Line,{yAxisId:\"right\",type:\"monotone\",dataKey:\"usd\",stroke:\"#82ca9d\",name:t('USD_estimate')})]})})]})})})}),/*#__PURE__*/_jsxs(Row,{className:\"mt-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('wallet_management')}),/*#__PURE__*/_jsx(\"p\",{children:t('manage_your_digital_assets')}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:()=>handleNavigation('/wallet'),children:t('enter_wallet')})]})})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-center\",children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h4\",{children:t('buy_power')}),/*#__PURE__*/_jsx(\"p\",{children:t('view_and_purchase_new_power_products')}),/*#__PURE__*/_jsx(Button,{variant:\"success\",onClick:()=>handleNavigation('/products'),children:t('browse_products')})]})})})]})]});};export default CustomerDashboard;", "map": {"version": 3, "names": ["React", "useTranslation", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "chartData", "name", "fil", "usd", "StatCard", "_ref", "title", "value", "subValue", "variant", "className", "children", "Body", "Title", "CustomerDashboard", "t", "navigate", "handleNavigation", "path", "console", "log", "window", "location", "href", "fluid", "md", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "yAxisId", "label", "angle", "position", "orientation", "formatter", "type", "stroke", "onClick"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { useNavigate } from 'react-router-dom';\n\n// Mock data, we will fetch this from Supabase later\nconst chartData = [\n  { name: '7/1', fil: 0.2, usd: 1.1 },\n  { name: '7/2', fil: 0.25, usd: 1.3 },\n  { name: '7/3', fil: 0.22, usd: 1.2 },\n  { name: '7/4', fil: 0.3, usd: 1.5 },\n  { name: '7/5', fil: 0.28, usd: 1.4 },\n  { name: '7/6', fil: 0.35, usd: 1.8 },\n  { name: '7/7', fil: 0.4, usd: 2.0 },\n];\n\nconst StatCard = ({ title, value, subValue, variant }) => (\n    <Card className={`bg-${variant} text-white mb-3`}>\n        <Card.Body>\n            <Card.Title>{title}</Card.Title>\n            <h3>{value}</h3>\n            <p>{subValue}</p>\n        </Card.Body>\n    </Card>\n);\n\nconst CustomerDashboard = () => {\n    const { t } = useTranslation();\n    const navigate = useNavigate();\n\n    const handleNavigation = (path) => {\n        console.log('Navigating to:', path);\n        console.log('Current URL:', window.location.href);\n        navigate(path);\n    };\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <h2>{t('dashboard')}</h2>\n                </Col>\n            </Row>\n\n            <Row>\n                <Col md={3}>\n                    <StatCard title={t('total_earnings')} value=\"12,345.67 FIL\" subValue=\"≈ $50,123.45 USD\" variant=\"primary\" />\n                </Col>\n                <Col md={3}>\n                    <StatCard title={t('yesterday_earnings')} value=\"123.45 FIL\" subValue=\"≈ $501.23 USD\" variant=\"success\" />\n                </Col>\n                <Col md={3}>\n                    <StatCard title={t('available_balance')} value=\"1,234.56 FIL\" subValue=\"≈ $5,012.34 USD\" variant=\"info\" />\n                </Col>\n                <Col md={3}>\n                    <StatCard title={t('power_pledge')} value=\"10,000.00 FIL\" subValue=\"≈ $40,500.00 USD\" variant=\"warning\" />\n                </Col>\n            </Row>\n\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('earnings_trend')}</Card.Title>\n                            <ResponsiveContainer width=\"100%\" height={400}>\n                                <LineChart data={chartData}>\n                                    <CartesianGrid strokeDasharray=\"3 3\" />\n                                    <XAxis dataKey=\"name\" />\n                                    <YAxis yAxisId=\"left\" label={{ value: t('fil'), angle: -90, position: 'insideLeft' }} />\n                                    <YAxis yAxisId=\"right\" orientation=\"right\" label={{ value: t('usd'), angle: -90, position: 'insideRight' }} />\n                                    <Tooltip formatter={(value, name) => [value, t(name)]} />\n                                    <Legend />\n                                    <Line yAxisId=\"left\" type=\"monotone\" dataKey=\"fil\" stroke=\"#8884d8\" name={t('FIL_earnings')} />\n                                    <Line yAxisId=\"right\" type=\"monotone\" dataKey=\"usd\" stroke=\"#82ca9d\" name={t('USD_estimate')} />\n                                </LineChart>\n                            </ResponsiveContainer>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n             <Row className=\"mt-4\">\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('wallet_management')}</h4>\n                            <p>{t('manage_your_digital_assets')}</p>\n                            <Button\n                                variant=\"primary\"\n                                onClick={() => handleNavigation('/wallet')}\n                            >\n                                {t('enter_wallet')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col md={6} className=\"text-center\">\n                    <Card>\n                        <Card.Body>\n                            <h4>{t('buy_power')}</h4>\n                            <p>{t('view_and_purchase_new_power_products')}</p>\n                            <Button\n                                variant=\"success\"\n                                onClick={() => handleNavigation('/products')}\n                            >\n                                {t('browse_products')}\n                            </Button>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n\n        </Container>\n    );\n};\n\nexport default CustomerDashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,KAAQ,iBAAiB,CACnE,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,KAAQ,UAAU,CAC7G,OAASC,WAAW,KAAQ,kBAAkB,CAE9C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEC,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAI,CAAC,CACnC,CAAEF,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,IAAI,CAAEC,GAAG,CAAE,GAAI,CAAC,CACpC,CAAEF,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,IAAI,CAAEC,GAAG,CAAE,GAAI,CAAC,CACpC,CAAEF,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAI,CAAC,CACnC,CAAEF,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,IAAI,CAAEC,GAAG,CAAE,GAAI,CAAC,CACpC,CAAEF,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,IAAI,CAAEC,GAAG,CAAE,GAAI,CAAC,CACpC,CAAEF,IAAI,CAAE,KAAK,CAAEC,GAAG,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAI,CAAC,CACpC,CAED,KAAM,CAAAC,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,OAAQ,CAAC,CAAAJ,IAAA,oBACjDR,IAAA,CAACZ,IAAI,EAACyB,SAAS,CAAE,MAAMD,OAAO,kBAAmB,CAAAE,QAAA,cAC7CZ,KAAA,CAACd,IAAI,CAAC2B,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACZ,IAAI,CAAC4B,KAAK,EAAAF,QAAA,CAAEL,KAAK,CAAa,CAAC,cAChCT,IAAA,OAAAc,QAAA,CAAKJ,KAAK,CAAK,CAAC,cAChBV,IAAA,MAAAc,QAAA,CAAIH,QAAQ,CAAI,CAAC,EACV,CAAC,CACV,CAAC,EACV,CAED,KAAM,CAAAM,iBAAiB,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAE,CAAC,CAAGlC,cAAc,CAAC,CAAC,CAC9B,KAAM,CAAAmC,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAsB,gBAAgB,CAAIC,IAAI,EAAK,CAC/BC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEF,IAAI,CAAC,CACnCC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CACjDP,QAAQ,CAACE,IAAI,CAAC,CAClB,CAAC,CAED,mBACInB,KAAA,CAACjB,SAAS,EAAC0C,KAAK,MAAAb,QAAA,eACZd,IAAA,CAACd,GAAG,EAAC2B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBd,IAAA,CAACb,GAAG,EAAA2B,QAAA,cACAd,IAAA,OAAAc,QAAA,CAAKI,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,CACxB,CAAC,CACL,CAAC,cAENhB,KAAA,CAAChB,GAAG,EAAA4B,QAAA,eACAd,IAAA,CAACb,GAAG,EAACyC,EAAE,CAAE,CAAE,CAAAd,QAAA,cACPd,IAAA,CAACO,QAAQ,EAACE,KAAK,CAAES,CAAC,CAAC,gBAAgB,CAAE,CAACR,KAAK,CAAC,eAAe,CAACC,QAAQ,CAAC,uBAAkB,CAACC,OAAO,CAAC,SAAS,CAAE,CAAC,CAC3G,CAAC,cACNZ,IAAA,CAACb,GAAG,EAACyC,EAAE,CAAE,CAAE,CAAAd,QAAA,cACPd,IAAA,CAACO,QAAQ,EAACE,KAAK,CAAES,CAAC,CAAC,oBAAoB,CAAE,CAACR,KAAK,CAAC,YAAY,CAACC,QAAQ,CAAC,oBAAe,CAACC,OAAO,CAAC,SAAS,CAAE,CAAC,CACzG,CAAC,cACNZ,IAAA,CAACb,GAAG,EAACyC,EAAE,CAAE,CAAE,CAAAd,QAAA,cACPd,IAAA,CAACO,QAAQ,EAACE,KAAK,CAAES,CAAC,CAAC,mBAAmB,CAAE,CAACR,KAAK,CAAC,cAAc,CAACC,QAAQ,CAAC,sBAAiB,CAACC,OAAO,CAAC,MAAM,CAAE,CAAC,CACzG,CAAC,cACNZ,IAAA,CAACb,GAAG,EAACyC,EAAE,CAAE,CAAE,CAAAd,QAAA,cACPd,IAAA,CAACO,QAAQ,EAACE,KAAK,CAAES,CAAC,CAAC,cAAc,CAAE,CAACR,KAAK,CAAC,eAAe,CAACC,QAAQ,CAAC,uBAAkB,CAACC,OAAO,CAAC,SAAS,CAAE,CAAC,CACzG,CAAC,EACL,CAAC,cAENZ,IAAA,CAACd,GAAG,EAAA4B,QAAA,cACAd,IAAA,CAACb,GAAG,EAAA2B,QAAA,cACAd,IAAA,CAACZ,IAAI,EAAA0B,QAAA,cACDZ,KAAA,CAACd,IAAI,CAAC2B,IAAI,EAAAD,QAAA,eACNd,IAAA,CAACZ,IAAI,CAAC4B,KAAK,EAAAF,QAAA,CAAEI,CAAC,CAAC,gBAAgB,CAAC,CAAa,CAAC,cAC9ClB,IAAA,CAACH,mBAAmB,EAACgC,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAAhB,QAAA,cAC1CZ,KAAA,CAACZ,SAAS,EAACyC,IAAI,CAAE5B,SAAU,CAAAW,QAAA,eACvBd,IAAA,CAACN,aAAa,EAACsC,eAAe,CAAC,KAAK,CAAE,CAAC,cACvChC,IAAA,CAACR,KAAK,EAACyC,OAAO,CAAC,MAAM,CAAE,CAAC,cACxBjC,IAAA,CAACP,KAAK,EAACyC,OAAO,CAAC,MAAM,CAACC,KAAK,CAAE,CAAEzB,KAAK,CAAEQ,CAAC,CAAC,KAAK,CAAC,CAAEkB,KAAK,CAAE,CAAC,EAAE,CAAEC,QAAQ,CAAE,YAAa,CAAE,CAAE,CAAC,cACxFrC,IAAA,CAACP,KAAK,EAACyC,OAAO,CAAC,OAAO,CAACI,WAAW,CAAC,OAAO,CAACH,KAAK,CAAE,CAAEzB,KAAK,CAAEQ,CAAC,CAAC,KAAK,CAAC,CAAEkB,KAAK,CAAE,CAAC,EAAE,CAAEC,QAAQ,CAAE,aAAc,CAAE,CAAE,CAAC,cAC9GrC,IAAA,CAACL,OAAO,EAAC4C,SAAS,CAAEA,CAAC7B,KAAK,CAAEN,IAAI,GAAK,CAACM,KAAK,CAAEQ,CAAC,CAACd,IAAI,CAAC,CAAE,CAAE,CAAC,cACzDJ,IAAA,CAACJ,MAAM,GAAE,CAAC,cACVI,IAAA,CAACT,IAAI,EAAC2C,OAAO,CAAC,MAAM,CAACM,IAAI,CAAC,UAAU,CAACP,OAAO,CAAC,KAAK,CAACQ,MAAM,CAAC,SAAS,CAACrC,IAAI,CAAEc,CAAC,CAAC,cAAc,CAAE,CAAE,CAAC,cAC/FlB,IAAA,CAACT,IAAI,EAAC2C,OAAO,CAAC,OAAO,CAACM,IAAI,CAAC,UAAU,CAACP,OAAO,CAAC,KAAK,CAACQ,MAAM,CAAC,SAAS,CAACrC,IAAI,CAAEc,CAAC,CAAC,cAAc,CAAE,CAAE,CAAC,EACzF,CAAC,CACK,CAAC,EACf,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,cAELhB,KAAA,CAAChB,GAAG,EAAC2B,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBd,IAAA,CAACb,GAAG,EAACyC,EAAE,CAAE,CAAE,CAACf,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC/Bd,IAAA,CAACZ,IAAI,EAAA0B,QAAA,cACDZ,KAAA,CAACd,IAAI,CAAC2B,IAAI,EAAAD,QAAA,eACNd,IAAA,OAAAc,QAAA,CAAKI,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjClB,IAAA,MAAAc,QAAA,CAAII,CAAC,CAAC,4BAA4B,CAAC,CAAI,CAAC,cACxClB,IAAA,CAACX,MAAM,EACHuB,OAAO,CAAC,SAAS,CACjB8B,OAAO,CAAEA,CAAA,GAAMtB,gBAAgB,CAAC,SAAS,CAAE,CAAAN,QAAA,CAE1CI,CAAC,CAAC,cAAc,CAAC,CACd,CAAC,EACF,CAAC,CACV,CAAC,CACN,CAAC,cACNlB,IAAA,CAACb,GAAG,EAACyC,EAAE,CAAE,CAAE,CAACf,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC/Bd,IAAA,CAACZ,IAAI,EAAA0B,QAAA,cACDZ,KAAA,CAACd,IAAI,CAAC2B,IAAI,EAAAD,QAAA,eACNd,IAAA,OAAAc,QAAA,CAAKI,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBlB,IAAA,MAAAc,QAAA,CAAII,CAAC,CAAC,sCAAsC,CAAC,CAAI,CAAC,cAClDlB,IAAA,CAACX,MAAM,EACHuB,OAAO,CAAC,SAAS,CACjB8B,OAAO,CAAEA,CAAA,GAAMtB,gBAAgB,CAAC,WAAW,CAAE,CAAAN,QAAA,CAE5CI,CAAC,CAAC,iBAAiB,CAAC,CACjB,CAAC,EACF,CAAC,CACV,CAAC,CACN,CAAC,EACL,CAAC,EAEC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}