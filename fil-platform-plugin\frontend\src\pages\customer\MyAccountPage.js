
import React from 'react';
import { Container, Row, Col, Card, ListGroup } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const MyAccountPage = () => {
    const { t } = useTranslation();

    return (
        <Container>
            <h2 className="mb-4">{t('my_account')}</h2>
            <Row>
                <Col md={6}>
                    <Card>
                        <Card.Header>{t('personal_info')}</Card.Header>
                        <ListGroup variant="flush">
                            <ListGroup.Item>
                                <Link to="/my/kyc">{t('kyc_verification')}</Link>
                            </ListGroup.Item>
                            <ListGroup.Item>
                                <Link to="/my/change-login-pass">{t('change_login_password')}</Link>
                            </ListGroup.Item>
                            <ListGroup.Item>
                                <Link to="/my/change-withdraw-pass">{t('change_withdraw_password')}</Link>
                            </ListGroup.Item>
                        </ListGroup>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card>
                        <Card.Header>{t('my_recommendations')}</Card.Header>
                        <ListGroup variant="flush">
                            <ListGroup.Item>
                                <Link to="/my/recommend">{t('view_my_recommendations')}</Link>
                            </ListGroup.Item>
                        </ListGroup>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MyAccountPage;
