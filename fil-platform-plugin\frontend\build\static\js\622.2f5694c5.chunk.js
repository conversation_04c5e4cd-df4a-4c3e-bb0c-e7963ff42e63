"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[622],{1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),c=r(7852),n=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...l}=e;const i=(0,c.oU)(r,"row"),o=(0,c.gy)(),f=(0,c.Jm)(),h=`${i}-cols`,x=[];return o.forEach(e=>{const s=l[e];let r;delete l[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&x.push(`${h}${a}-${r}`)}),(0,n.jsx)(d,{ref:s,...l,className:t()(a,i,...x)})});l.displayName="Row";const i=l},4063:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),c=r(7852),n=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,bg:a="primary",pill:d=!1,text:l,className:i,as:o="span",...f}=e;const h=(0,c.oU)(r,"badge");return(0,n.jsx)(o,{ref:s,...f,className:t()(i,h,d&&"rounded-pill",l&&`text-${l}`,a&&`bg-${a}`)})});l.displayName="Badge";const i=l},4196:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),c=r(7852),n=r(579);const l=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:l,borderless:i,hover:o,size:f,variant:h,responsive:x,...m}=e;const u=(0,c.oU)(r,"table"),j=t()(a,u,h&&`${u}-${h}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,l&&`${u}-bordered`,i&&`${u}-borderless`,o&&`${u}-hover`),p=(0,n.jsx)("table",{...m,className:j,ref:s});if(x){let e=`${u}-responsive`;return"string"===typeof x&&(e=`${e}-${x}`),(0,n.jsx)("div",{className:e,children:p})}return p});l.displayName="Table";const i=l},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),c=r(7852),n=r(579);const l=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:l,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,c.oU)(r,"col");const n=(0,c.gy)(),l=(0,c.Jm)(),i=[],o=[];return n.forEach(e=>{const s=d[e];let a,t,c;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:c}=s):a=s;const n=e!==l?`-${e}`:"";a&&i.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=c&&o.push(`order${n}-${c}`),null!=t&&o.push(`offset${n}-${t}`)}),[{...d,className:t()(a,...i,...o)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,n.jsx)(d,{...a,ref:s,className:t()(r,!i.length&&l)})});l.displayName="Col";const i=l},8622:(e,s,r)=>{r.r(s),r.d(s,{default:()=>m});var a=r(5043),t=r(3519),d=r(1072),c=r(8602),n=r(8628),l=r(4282),i=r(4196),o=r(4063),f=r(4312),h=r(4117),x=r(579);const m=()=>{const{t:e}=(0,h.Bd)(),[s,r]=(0,a.useState)([]),[m,u]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,f.b)();if(!e)return;u(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void u(!1);const{data:a,error:t}=await e.from("products").select("\n                    id,\n                    name,\n                    category,\n                    price,\n                    total_shares,\n                    sold_shares,\n                    is_disabled,\n                    review_status,\n                    created_at\n                ").eq("maker_id",s.id).order("created_at",{ascending:!1});t?console.error("Error fetching products:",t):r(a),u(!1)})()},[]),m?(0,x.jsx)("div",{children:e("loading_products")}):(0,x.jsxs)(t.A,{children:[(0,x.jsx)("h2",{className:"mb-4",children:e("my_products")}),(0,x.jsx)(d.A,{children:(0,x.jsx)(c.A,{children:(0,x.jsx)(n.A,{children:(0,x.jsxs)(n.A.Body,{children:[(0,x.jsx)(l.A,{variant:"success",className:"mb-3",children:e("add_new_product")}),(0,x.jsxs)(i.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,x.jsx)("thead",{children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{children:e("product_id")}),(0,x.jsx)("th",{children:e("product_name_header")}),(0,x.jsx)("th",{children:e("category")}),(0,x.jsx)("th",{children:e("price_per_share")}),(0,x.jsx)("th",{children:e("total_shares")}),(0,x.jsx)("th",{children:e("sold_shares")}),(0,x.jsx)("th",{children:e("status")}),(0,x.jsx)("th",{children:e("review_status")}),(0,x.jsx)("th",{children:e("created_at")}),(0,x.jsx)("th",{children:e("actions")})]})}),(0,x.jsx)("tbody",{children:0===s.length?(0,x.jsx)("tr",{children:(0,x.jsx)("td",{colSpan:"10",className:"text-center",children:e("no_products_available")})}):s.map(s=>(0,x.jsxs)("tr",{children:[(0,x.jsxs)("td",{children:[s.id.substring(0,8),"..."]}),(0,x.jsx)("td",{children:s.name}),(0,x.jsx)("td",{children:(0,x.jsx)(o.A,{bg:"spot"===s.category?"success":"primary",children:"spot"===s.category?e("spot_category"):e("futures_category")})}),(0,x.jsx)("td",{children:s.price}),(0,x.jsx)("td",{children:s.total_shares}),(0,x.jsx)("td",{children:s.sold_shares}),(0,x.jsx)("td",{children:(0,x.jsx)(o.A,{bg:s.is_disabled?"danger":"success",children:s.is_disabled?e("disabled"):e("enabled")})}),(0,x.jsx)("td",{children:(0,x.jsx)(o.A,{bg:"approved"===s.review_status?"success":"warning",children:s.review_status})}),(0,x.jsx)("td",{children:new Date(s.created_at).toLocaleString()}),(0,x.jsxs)("td",{children:[(0,x.jsx)(l.A,{variant:"info",size:"sm",className:"me-2",children:e("edit")}),(0,x.jsx)(l.A,{variant:"danger",size:"sm",children:e("delete")})]})]},s.id))})]})]})})})})]})}},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),c=r(7852),n=r(579);const l=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,c.oU)(a,"card-body"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});l.displayName="CardBody";const i=l,o=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,c.oU)(a,"card-footer"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});o.displayName="CardFooter";const f=o;var h=r(1778);const x=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...i}=e;const o=(0,c.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,n.jsx)(h.A.Provider,{value:f,children:(0,n.jsx)(l,{ref:s,...i,className:t()(a,o)})})});x.displayName="CardHeader";const m=x,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:l="img",...i}=e;const o=(0,c.oU)(r,"card-img");return(0,n.jsx)(l,{ref:s,className:t()(d?`${o}-${d}`:o,a),...i})});u.displayName="CardImg";const j=u,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...l}=e;return a=(0,c.oU)(a,"card-img-overlay"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});p.displayName="CardImgOverlay";const b=p,N=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...l}=e;return a=(0,c.oU)(a,"card-link"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});N.displayName="CardLink";const v=N;var y=r(4488);const g=(0,y.A)("h6"),$=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=g,...l}=e;return a=(0,c.oU)(a,"card-subtitle"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});$.displayName="CardSubtitle";const _=$,w=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...l}=e;return a=(0,c.oU)(a,"card-text"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});w.displayName="CardText";const A=w,P=(0,y.A)("h5"),R=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=P,...l}=e;return a=(0,c.oU)(a,"card-title"),(0,n.jsx)(d,{ref:s,className:t()(r,a),...l})});R.displayName="CardTitle";const U=R,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:l,border:o,body:f=!1,children:h,as:x="div",...m}=e;const u=(0,c.oU)(r,"card");return(0,n.jsx)(x,{ref:s,...m,className:t()(a,u,d&&`bg-${d}`,l&&`text-${l}`,o&&`border-${o}`),children:f?(0,n.jsx)(i,{children:h}):h})});C.displayName="Card";const k=Object.assign(C,{Img:j,Title:U,Subtitle:_,Body:i,Link:v,Text:A,Header:m,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=622.2f5694c5.chunk.js.map